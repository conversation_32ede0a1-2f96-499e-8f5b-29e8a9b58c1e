<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Lead Details')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('leads.index')); ?>"><?php echo e(__('Leads')); ?></a></li>
    <li class="breadcrumb-item" aria-current="page"><?php echo e($lead->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5><?php echo e($lead->name); ?></h5>
                        </div>
                        <div class="col-auto">
                            <?php if(Gate::check('edit leads')): ?>
                                <a href="#" class="btn btn-primary customModal" data-size="lg"
                                    data-url="<?php echo e(route('leads.edit', $lead->id)); ?>" data-title="<?php echo e(__('Edit Lead')); ?>">
                                    <i class="ti ti-pencil align-text-bottom"></i> <?php echo e(__('Edit')); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><?php echo e(__('Name')); ?>:</strong></td>
                                    <td><?php echo e($lead->name); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Phone')); ?>:</strong></td>
                                    <td>
                                        <a href="tel:<?php echo e($lead->phone); ?>"><?php echo e($lead->phone); ?></a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Email')); ?>:</strong></td>
                                    <td>
                                        <?php if($lead->email): ?>
                                            <a href="mailto:<?php echo e($lead->email); ?>"><?php echo e($lead->email); ?></a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('Not provided')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Alternate Phone')); ?>:</strong></td>
                                    <td>
                                        <?php if($lead->alternate_phone): ?>
                                            <a href="tel:<?php echo e($lead->alternate_phone); ?>"><?php echo e($lead->alternate_phone); ?></a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('Not provided')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Status')); ?>:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($lead->status == 'active' ? 'success' : ($lead->status == 'converted' ? 'primary' : 'warning')); ?>">
                                            <?php echo e(ucfirst($lead->status)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Priority')); ?>:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($lead->priority == 'high' ? 'danger' : ($lead->priority == 'medium' ? 'warning' : 'info')); ?>">
                                            <?php echo e(ucfirst($lead->priority)); ?>

                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><?php echo e(__('Score')); ?>:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($lead->score_grade == 'A' ? 'success' : ($lead->score_grade == 'B' ? 'warning' : 'secondary')); ?>">
                                            <?php echo e($lead->score ?? 0); ?> (<?php echo e($lead->score_grade ?? 'N/A'); ?>)
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Assigned Agent')); ?>:</strong></td>
                                    <td>
                                        <?php if($lead->assignedAgent): ?>
                                            <?php echo e($lead->assignedAgent->name); ?>

                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('City')); ?>:</strong></td>
                                    <td><?php echo e($lead->city ?? __('Not provided')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('State')); ?>:</strong></td>
                                    <td><?php echo e($lead->state ?? __('Not provided')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Pincode')); ?>:</strong></td>
                                    <td><?php echo e($lead->pincode ?? __('Not provided')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Created')); ?>:</strong></td>
                                    <td><?php echo e($lead->created_at->format('M d, Y H:i')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if($lead->address): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><?php echo e(__('Address')); ?></h6>
                                <p><?php echo e($lead->address); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($lead->requirements): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><?php echo e(__('Requirements')); ?></h6>
                                <p><?php echo e($lead->requirements); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/leads/show.blade.php ENDPATH**/ ?>