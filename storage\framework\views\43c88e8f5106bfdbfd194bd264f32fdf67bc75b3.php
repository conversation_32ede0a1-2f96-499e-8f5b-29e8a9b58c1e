<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Task Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Task Dashboard')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create tasks')): ?>
            <a href="#" data-url="<?php echo e(route('tasks.create')); ?>" data-size="lg" data-ajax-popup="true" 
               data-title="<?php echo e(__('Create New Task')); ?>" class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i> <?php echo e(__('Create Task')); ?>

            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Task Statistics -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Total Tasks')); ?></h6>
                            <h3 class="text-primary"><?php echo e($stats['total']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-primary-light ti ti-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('In Progress')); ?></h6>
                            <h3 class="text-warning"><?php echo e($stats['in_progress']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-warning-light ti ti-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Overdue')); ?></h6>
                            <h3 class="text-danger"><?php echo e($stats['overdue']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-danger-light ti ti-alert-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Completed')); ?></h6>
                            <h3 class="text-success"><?php echo e($stats['completed']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-success-light ti ti-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Due Today')); ?></h6>
                            <h3 class="text-info"><?php echo e($stats['due_today']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-info-light ti ti-calendar-event"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('High Priority')); ?></h6>
                            <h3 class="text-dark"><?php echo e($stats['high_priority']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-dark-light ti ti-flag"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Assigned to Me')); ?></h6>
                            <h3 class="text-secondary"><?php echo e($stats['assigned_to_me']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-secondary-light ti ti-user"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Created by Me')); ?></h6>
                            <h3 class="text-success"><?php echo e($stats['created_by_me']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-success-light ti ti-plus"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Overdue Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Overdue Tasks')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($overdueTasks->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Task')); ?></th>
                                        <th><?php echo e(__('Assigned To')); ?></th>
                                        <th><?php echo e(__('Due Date')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $overdueTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($task->title); ?></strong>
                                                <br><span class="badge bg-<?php echo e($task->priority == 'urgent' ? 'dark' : ($task->priority == 'high' ? 'danger' : 'warning')); ?>">
                                                    <?php echo e($task->priority_label); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($task->assignedTo): ?>
                                                    <?php echo e($task->assignedTo->name); ?>

                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="text-danger">
                                                    <?php echo e($task->due_date->format('M d, Y')); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('tasks.show', $task->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="ti ti-check-circle text-success" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2"><?php echo e(__('No overdue tasks!')); ?></h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Tasks Due Today -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Due Today')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($todayTasks->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Task')); ?></th>
                                        <th><?php echo e(__('Status')); ?></th>
                                        <th><?php echo e(__('Assigned To')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $todayTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($task->title); ?></strong>
                                                <br><span class="badge bg-<?php echo e($task->priority == 'urgent' ? 'dark' : ($task->priority == 'high' ? 'danger' : 'warning')); ?>">
                                                    <?php echo e($task->priority_label); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                    $statusColors = [
                                                        'not_started' => 'secondary',
                                                        'in_progress' => 'warning',
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                ?>
                                                <span class="badge bg-<?php echo e($statusColors[$task->status] ?? 'secondary'); ?>">
                                                    <?php echo e($task->status_label); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($task->assignedTo): ?>
                                                    <?php echo e($task->assignedTo->name); ?>

                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('tasks.show', $task->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="ti ti-calendar-check text-info" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2"><?php echo e(__('No tasks due today')); ?></h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5><?php echo e(__('Recent Tasks')); ?></h5>
                        </div>
                        <div class="col-auto">
                            <a href="<?php echo e(route('tasks.index')); ?>" class="btn btn-sm btn-outline-primary">
                                <?php echo e(__('View All')); ?>

                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($recentTasks->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Task')); ?></th>
                                        <th><?php echo e(__('Status')); ?></th>
                                        <th><?php echo e(__('Created')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($task->title); ?></strong>
                                                <?php if($task->lead): ?>
                                                    <br><small class="text-primary"><?php echo e(__('Lead')); ?>: <?php echo e($task->lead->name); ?></small>
                                                <?php elseif($task->project): ?>
                                                    <br><small class="text-info"><?php echo e(__('Project')); ?>: <?php echo e($task->project->name); ?></small>
                                                <?php elseif($task->property): ?>
                                                    <br><small class="text-success"><?php echo e(__('Property')); ?>: <?php echo e($task->property->name); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                    $statusColors = [
                                                        'not_started' => 'secondary',
                                                        'in_progress' => 'warning',
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                ?>
                                                <span class="badge bg-<?php echo e($statusColors[$task->status] ?? 'secondary'); ?>">
                                                    <?php echo e($task->status_label); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo e($task->created_at->diffForHumans()); ?></small>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('tasks.show', $task->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="ti ti-clipboard-list text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2"><?php echo e(__('No recent tasks')); ?></h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Upcoming Tasks (Next 7 Days)')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($upcomingTasks->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Task')); ?></th>
                                        <th><?php echo e(__('Due Date')); ?></th>
                                        <th><?php echo e(__('Assigned To')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $upcomingTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($task->title); ?></strong>
                                                <br><span class="badge bg-<?php echo e($task->priority == 'urgent' ? 'dark' : ($task->priority == 'high' ? 'danger' : 'warning')); ?>">
                                                    <?php echo e($task->priority_label); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php echo e($task->due_date->format('M d, Y')); ?>

                                                <br><small class="text-muted"><?php echo e($task->due_date->diffForHumans()); ?></small>
                                            </td>
                                            <td>
                                                <?php if($task->assignedTo): ?>
                                                    <?php echo e($task->assignedTo->name); ?>

                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('tasks.show', $task->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="ti ti-calendar-time text-info" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2"><?php echo e(__('No upcoming tasks')); ?></h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/tasks/dashboard.blade.php ENDPATH**/ ?>