<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lab404\Impersonate\Models\Impersonate;
use Spatie\Permission\Traits\HasRoles;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasRoles;
    use Notifiable;
    use Impersonate;
    use HasApiTokens;


    protected $fillable = [
        'name',
        'email',
        'password',
        'type',
        'phone_number',
        'profile',
        'lang',
        'subscription',
        'subscription_expire_date',
        'parent_id',
        'is_active',
        'twofa_secret',
    ];


    protected $hidden = [
        'password',
        'remember_token',
    ];


    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // User hierarchy is managed through parent_id
        // Owners have parent_id = null
        // Sub-users have parent_id = owner_user_id
    }

    /**
     * Set the user's language attribute with backward compatibility
     *
     * @param  string  $value
     * @return void
     */
    public function setLangAttribute($value)
    {
        // Convert legacy 'english' to 'en' for backward compatibility
        if ($value === 'english') {
            $value = 'en';
        }

        // Validate against supported locales
        $supportedLocales = $this->getSupportedLocales();
        if (!in_array($value, $supportedLocales)) {
            $value = config('app.locale', 'en');
        }

        $this->attributes['lang'] = $value;
    }

    /**
     * Get the user's language attribute with fallback
     *
     * @param  string  $value
     * @return string
     */
    public function getLangAttribute($value)
    {
        // Convert legacy 'english' to 'en' for backward compatibility
        if ($value === 'english') {
            return 'en';
        }

        // Return value or fallback to default
        return $value ?: config('app.locale', 'en');
    }

    /**
     * Get supported locales from configuration
     *
     * @return array
     */
    private function getSupportedLocales(): array
    {
        $multilingualConfig = config('multilingual.supported_locales', []);

        return collect($multilingualConfig)
            ->filter(function ($config) {
                return $config['enabled'] ?? false;
            })
            ->keys()
            ->toArray();
    }

    public function canImpersonate()
    {
        // Example: Only admins can impersonate others
        return $this->type == 'super admin';
    }

    public function totalUser()
    {
        return User::whereNotIn('type', ['tenant', 'maintainer'])->where('parent_id', $this->id)->count();
    }
    public function totalClient()
    {
        return User::where('parent_id', $this->id)->where('type','client')->count();
    }
    public function clients()
    {
        return $this->hasOne('App\Models\Client', 'user', 'id');
    }

    public function totalContact()
    {
        return Contact::where('parent_id', '=', parentId())->count();
    }

    public function roleWiseUserCount($role)
    {
        return User::where('type', $role)->where('parent_id', parentId())->count();
    }

    public static function getDevice($user)
    {
        $mobileType = '/(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera mini|avantgo|mobilesafari|docomo)/i';
        $tabletType = '/(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile))/i';
        if (preg_match_all($mobileType, $user)) {
            return 'mobile';
        } else {
            if (preg_match_all($tabletType, $user)) {
                return 'tablet';
            } else {
                return 'desktop';
            }
        }
    }

    public function subscriptions()
    {
        return $this->hasOne('App\Models\Subscription', 'id', 'subscription');
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function assignedLeads()
    {
        return $this->hasMany(Lead::class, 'assigned_agent_id');
    }

    public function ownedLeads()
    {
        return $this->hasMany(Lead::class, 'lead_owner_id');
    }

    public function callLogs()
    {
        return $this->hasMany(CallLog::class);
    }

    public function leadActivities()
    {
        return $this->hasMany(LeadActivity::class);
    }

    public function leadAssignments()
    {
        return $this->hasMany(LeadAssignment::class, 'agent_id');
    }

    public function assignedLeadAssignments()
    {
        return $this->hasMany(LeadAssignment::class, 'assigned_by');
    }

    // Helper methods for lead management
    public function isAgent()
    {
        return $this->type === 'agent';
    }

    public function isChannelPartner()
    {
        return $this->type === 'channel partner';
    }

    public function isManager()
    {
        return $this->type === 'manager';
    }

    public function canManageLeads()
    {
        return $this->hasPermissionTo('manage leads');
    }

    public function canAssignLeads()
    {
        return $this->hasPermissionTo('assign leads');
    }

    public static $systemModules = [
        'user',
        'client',
        'item',
        'estimation',
        'invoice',
        'expense',
        'leads',
        'projects',
        'properties',
        'documents',
        'tasks',
        'calling_system',
        'form',
        'category',
        'tax',
        'unit',
        'notification',
        'contact',
        'note',
        'logged history',
        'pricing transation',
        'account settings',
        'password settings',
        'general settings',
        'company settings',
    ];

    public function SubscriptionLeftDay()
    {
        $Subscription = Subscription::find($this->subscription);
        if ($Subscription->interval == 'Unlimited') {
            $return = '<span class="text-success">'.__('Unlimited Days Left').'</span>';
        } else {
            $date1 = date_create(date('Y-m-d'));
            $date2 = date_create($this->subscription_expire_date);
            $diff = date_diff($date1, $date2);
            $days = $diff->format("%R%a");
            if($days > 0) {
                $return = '<span class="text-success">'.$days.__(' Days Left').'</span>';
            } else {
                $return = '<span class="text-danger">'.$days.__(' Days Left').'</span>';
            }
        }


        return $return;
    }
}
