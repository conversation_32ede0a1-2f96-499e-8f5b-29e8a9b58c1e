<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Role')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('role.index')); ?>"><?php echo e(__('Role')); ?></a></li>
    <li class="breadcrumb-item" aria-current="Edit"> <?php echo e(__('Edit')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php
        $systemModules = \App\Models\User::$systemModules;
    ?>

    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5><?php echo e(__('Create Role And Permissions')); ?></h5>
                        </div>

                    </div>
                </div>
                <div class="card-body">
                    <?php echo e(Form::model($role,array('route' => array('role.update', $role->id), 'method' => 'PUT'))); ?>

                    <div class="form-group">
                        <?php echo e(Form::label('title',__('Role Title'),['class'=>'form-label'])); ?>

                        <?php echo e(Form::text('title',$role->name,array('class'=>'form-control','placeholder'=>__('Enter role title'),in_array($role->name,['tenant','maintainer'])?'readonly':''))); ?>

                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-12 col-md-12">
                                <?php $__currentLoopData = $systemModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="row">
                                        <div class="col-12">
                                            <h6 class="text-primary mb-3">
                                                <i class="ti ti-<?php echo e($module === 'form' ? 'forms' : ($module === 'leads' ? 'users' : ($module === 'projects' ? 'briefcase' : ($module === 'properties' ? 'building' : ($module === 'documents' ? 'file-text' : ($module === 'tasks' ? 'check-square' : ($module === 'calling_system' ? 'phone' : 'settings'))))))); ?>"></i>
                                                <?php echo e(ucfirst(str_replace('_', ' ', $module))); ?> <?php echo e(__('Permissions')); ?>

                                            </h6>
                                        </div>
                                        <?php
                                            $modulePermissions = [];
                                            foreach($permissionList as $permission) {
                                                $permissionName = strtolower($permission->name);
                                                $moduleName = strtolower($module);

                                                // Special handling for form module to exclude non-form permissions
                                                if ($module === 'form') {
                                                    if (str_contains($permissionName, 'form_') ||
                                                        str_contains($permissionName, 'form ') ||
                                                        (str_contains($permissionName, 'form') &&
                                                         !str_contains($permissionName, 'property performance'))) {
                                                        $modulePermissions[] = $permission;
                                                    }
                                                } else {
                                                    if (str_contains($permissionName, $moduleName)) {
                                                        $modulePermissions[] = $permission;
                                                    }
                                                }
                                            }
                                        ?>

                                        <?php $__currentLoopData = $modulePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="form-check custom-chek form-check-inline col-md-3">
                                                <?php echo e(Form::checkbox('user_permission[]', $permission->id, null, ['class'=>'form-check-input', 'id' => $module.'_permission'.$permission->id,in_array($permission->id,$assignPermission)?'checked':''])); ?>

                                                <?php echo e(Form::label($module.'_permission'.$permission->id, ucfirst(str_replace('_', ' ', $permission->name)), ['class'=>'form-check-label'])); ?>

                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        <?php if(count($modulePermissions) === 0): ?>
                                            <div class="col-12">
                                                <p class="text-muted"><?php echo e(__('No permissions available for this module')); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <hr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mt-20 text-end">
                        <?php echo e(Form::submit(__('Update'),array('class'=>'btn btn-secondary btn-rounded'))); ?>

                    </div>
                    <?php echo e(Form::close()); ?>

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/role/edit.blade.php ENDPATH**/ ?>