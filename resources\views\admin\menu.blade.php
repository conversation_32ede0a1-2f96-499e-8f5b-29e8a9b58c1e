@php
    $admin_logo = getSettingsValByName('company_logo');
    $ids = parentId();
    $authUser = \App\Models\User::find($ids);
    $subscription = \App\Models\Subscription::find($authUser->subscription);
    $routeName = \Request::route()->getName();
    $pricing_feature_settings = getSettingsValByIdName(1, 'pricing_feature');
@endphp
<nav class="pc-sidebar">
    <div class="navbar-wrapper">
        <div class="m-header">
            <a href="#" class="b-brand text-primary">
                <img src="{{ asset(Storage::url('upload/logo/')) . '/' . (isset($admin_logo) && !empty($admin_logo) ? $admin_logo : 'logo.png') }}"
                    alt="" class="logo logo-lg" />
            </a>
        </div>
        <div class="navbar-content">
            <ul class="pc-navbar">
                <li class="pc-item pc-caption">
                    <label>{{ __('navigation.home') }}</label>
                    <i class="ti ti-dashboard"></i>
                </li>
                <li class="pc-item {{ in_array($routeName, ['dashboard', 'home', '']) ? 'active' : '' }}">
                    <a href="{{ route('dashboard') }}" class="pc-link">
                        <span class="pc-micon"><i class="ti ti-dashboard"></i></span>
                        <span class="pc-mtext">{{ __('navigation.dashboard') }}</span>
                    </a>
                </li>
                
                @if (\Auth::user()->type == 'super admin')
                    @if (Gate::check('manage user'))
                        <li class="pc-item {{ in_array($routeName, ['users.index', 'users.show']) ? 'active' : '' }}">
                            <a href="{{ route('users.index') }}" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-user-plus"></i></span>
                                <span class="pc-mtext">{{ __('navigation.customers') }}</span>
                            </a>
                        </li>
                    @endif
                @else
                    @if (Gate::check('manage user') || Gate::check('manage role') || Gate::check('manage logged history'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['users.index', 'logged.history', 'role.index', 'role.create', 'role.edit']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-users"></i></span>
                                <span class="pc-mtext">{{ __('Staff Management') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['users.index', 'logged.history', 'role.index', 'role.create', 'role.edit']) ? 'block' : 'none' }}">
                                @if (Gate::check('manage user'))
                                    <li class="pc-item {{ in_array($routeName, ['users.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('users.index') }}">{{ __('navigation.users') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage role'))
                                    <li class="pc-item {{ in_array($routeName, ['role.index', 'role.create', 'role.edit']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('role.index') }}">{{ __('roles.roles') }}</a>
                                    </li>
                                @endif
                                @if ($pricing_feature_settings == 'off' || $subscription->enabled_logged_history == 1)
                                    @if (Gate::check('manage logged history'))
                                        <li class="pc-item {{ in_array($routeName, ['logged.history']) ? 'active' : '' }}">
                                            <a class="pc-link" href="{{ route('logged.history') }}">{{ __('Logged History') }}</a>
                                        </li>
                                    @endif
                                @endif
                            </ul>
                        </li>
                    @endif
                @endif

                {{-- Business Management Section --}}
                @if (Gate::check('manage client') ||
                        Gate::check('manage item') ||
                        Gate::check('manage estimation') ||
                        Gate::check('manage invoice') ||
                        Gate::check('manage expense') ||
                        Gate::check('manage contact') ||
                        Gate::check('manage note') ||
                        Gate::check('manage leads') ||
                        Gate::check('manage projects') ||
                        Gate::check('manage tasks'))
                    <li class="pc-item pc-caption">
                        <label>{{ __('Business Management') }}</label>
                        <i class="ti ti-chart-arcs"></i>
                    </li>

                    {{-- Client Management --}}
                    @if (Gate::check('manage client'))
                        <li class="pc-item {{ in_array($routeName, ['client.index']) ? 'active' : '' }}">
                            <a class="pc-link" href="{{ route('client.index') }}">
                                <span class="pc-micon"><i data-feather="user"></i></span>
                                <span class="pc-mtext">{{ __('Client') }}</span>
                            </a>
                        </li>
                    @endif

                    {{-- Smart Leads Management --}}
                    @if (Gate::check('manage leads'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['leads.index', 'leads.create', 'leads.show', 'leads.edit', 'leads.duplicates.stats', 'leads.lifecycle.analytics']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="phone-call"></i></span>
                                <span class="pc-mtext">{{ __('navigation.leads') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['leads.index', 'leads.create', 'leads.show', 'leads.edit', 'leads.duplicates.stats', 'leads.lifecycle.analytics']) ? 'block' : 'none' }}">
                                <li class="pc-item {{ in_array($routeName, ['leads.index']) ? 'active' : '' }}">
                                    <a class="pc-link" href="{{ route('leads.index') }}">{{ __('All Leads') }}</a>
                                </li>
                                @if (Gate::check('view duplicate stats'))
                                    <li class="pc-item {{ in_array($routeName, ['leads.duplicates.stats']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('leads.duplicates.stats') }}">{{ __('Duplicate Management') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('view lifecycle analytics'))
                                    <li class="pc-item {{ in_array($routeName, ['leads.lifecycle.analytics']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('leads.lifecycle.analytics') }}">{{ __('Lifecycle Analytics') }}</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Smart Project Management --}}
                    @if (Gate::check('manage projects'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['projects.index', 'projects.create', 'projects.show', 'projects.edit', 'projects.dashboard']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="briefcase"></i></span>
                                <span class="pc-mtext">{{ __('navigation.projects') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['projects.index', 'projects.create', 'projects.show', 'projects.edit', 'projects.dashboard']) ? 'block' : 'none' }}">
                                <li class="pc-item {{ in_array($routeName, ['projects.index']) ? 'active' : '' }}">
                                    <a class="pc-link" href="{{ route('projects.index') }}">{{ __('All Projects') }}</a>
                                </li>
                                @if (Gate::check('view project dashboard'))
                                    <li class="pc-item {{ in_array($routeName, ['projects.dashboard']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('projects.dashboard') }}">{{ __('Dashboard') }}</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Task Management System --}}
                    @if (Gate::check('manage tasks') || Gate::check('view tasks'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['tasks.index', 'tasks.create', 'tasks.show', 'tasks.edit', 'tasks.dashboard', 'tasks.calendar', 'tasks.analytics']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="check-square"></i></span>
                                <span class="pc-mtext">{{ __('Task Management') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['tasks.index', 'tasks.create', 'tasks.show', 'tasks.edit', 'tasks.dashboard', 'tasks.calendar', 'tasks.analytics']) ? 'block' : 'none' }}">
                                @if (Gate::check('view task dashboard'))
                                    <li class="pc-item {{ in_array($routeName, ['tasks.dashboard']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('tasks.dashboard') }}">{{ __('Dashboard') }}</a>
                                    </li>
                                @endif
                                <li class="pc-item {{ in_array($routeName, ['tasks.index']) ? 'active' : '' }}">
                                    <a class="pc-link" href="{{ route('tasks.index') }}">{{ __('All Tasks') }}</a>
                                </li>
                                @if (Gate::check('create tasks'))
                                    <li class="pc-item {{ in_array($routeName, ['tasks.create']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('tasks.create') }}">{{ __('Create Task') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('view task calendar'))
                                    <li class="pc-item {{ in_array($routeName, ['tasks.calendar']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('tasks.calendar') }}">{{ __('Task Calendar') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('view task analytics'))
                                    <li class="pc-item {{ in_array($routeName, ['tasks.analytics']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('tasks.analytics') }}">{{ __('Analytics & Reports') }}</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Calling System Management --}}
                    @if (Gate::check('calling_system_call_log_view') || Gate::check('calling_system_lead_assignment_view') || Gate::check('calling_system_follow_up_view') || Gate::check('calling_system_call_queue_view') || Gate::check('calling_system_performance_view_own'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['calling.dashboard', 'call-logs.index', 'lead-assignments.index', 'follow-ups.index', 'call-queue.index', 'calling.performance', 'calling.analytics']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="phone"></i></span>
                                <span class="pc-mtext">{{ __('Calling System') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['calling.dashboard', 'call-logs.index', 'lead-assignments.index', 'follow-ups.index', 'call-queue.index', 'calling.performance', 'calling.analytics']) ? 'block' : 'none' }}">
                                @if (Gate::check('calling_system_dashboard_agent') || Gate::check('calling_system_dashboard_manager') || Gate::check('calling_system_dashboard_admin'))
                                    <li class="pc-item {{ in_array($routeName, ['calling.dashboard']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('calling.dashboard') }}">{{ __('Dashboard') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('calling_system_call_log_view'))
                                    <li class="pc-item {{ in_array($routeName, ['call-logs.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('call-logs.index') }}">{{ __('Call Logs') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('calling_system_lead_assignment_view'))
                                    <li class="pc-item {{ in_array($routeName, ['lead-assignments.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('lead-assignments.index') }}">{{ __('Lead Assignments') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('calling_system_follow_up_view'))
                                    <li class="pc-item {{ in_array($routeName, ['follow-ups.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('follow-ups.index') }}">{{ __('Follow-ups') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('calling_system_call_queue_view'))
                                    <li class="pc-item {{ in_array($routeName, ['call-queue.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('call-queue.index') }}">{{ __('Call Queue') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('calling_system_performance_view_own') || Gate::check('calling_system_performance_view_team') || Gate::check('calling_system_performance_view_all'))
                                    <li class="pc-item {{ in_array($routeName, ['calling.performance']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('calling.performance') }}">{{ __('Performance') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('calling_system_analytics_view'))
                                    <li class="pc-item {{ in_array($routeName, ['calling.analytics']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('calling.analytics') }}">{{ __('Analytics') }}</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Smart Sales & Finance Management --}}
                    @if (Gate::check('manage item') || Gate::check('manage estimation') || Gate::check('manage invoice') || Gate::check('manage expense'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['item.index', 'estimation.index', 'estimation.show', 'invoice.index', 'invoice.show', 'expense.index']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="dollar-sign"></i></span>
                                <span class="pc-mtext">{{ __('Sales & Finance') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['item.index', 'estimation.index', 'estimation.show', 'invoice.index', 'invoice.show', 'expense.index']) ? 'block' : 'none' }}">
                                @if (Gate::check('manage item'))
                                    <li class="pc-item {{ in_array($routeName, ['item.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('item.index') }}">
                                            <span class="pc-micon"><i data-feather="sliders"></i></span>
                                            <span class="pc-mtext">{{ __('Items') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage estimation'))
                                    <li class="pc-item {{ in_array($routeName, ['estimation.index', 'estimation.show']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('estimation.index') }}">
                                            <span class="pc-micon"><i data-feather="file-text"></i></span>
                                            <span class="pc-mtext">{{ __('Estimation') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage invoice'))
                                    <li class="pc-item {{ in_array($routeName, ['invoice.index', 'invoice.show']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('invoice.index') }}">
                                            <span class="pc-micon"><i data-feather="file-plus"></i></span>
                                            <span class="pc-mtext">{{ __('Invoice') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage expense'))
                                    <li class="pc-item {{ in_array($routeName, ['expense.index']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('expense.index') }}">
                                            <span class="pc-micon"><i data-feather="check-circle"></i></span>
                                            <span class="pc-mtext">{{ __('Expense') }}</span>
                                        </a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Smart Document & Form Management --}}
                    @if (Gate::check('view documents') || Gate::check('form_builder_access'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, [
                            'documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard',
                            'forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show'
                        ]) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="file-text"></i></span>
                                <span class="pc-mtext">{{ __('Documents & Forms') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, [
                                'documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard',
                                'forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show'
                            ]) ? 'block' : 'none' }}">
                                
                                {{-- Document Management Subsection --}}
                                @if (Gate::check('view documents'))
                                    <li class="pc-item pc-hasmenu {{ in_array($routeName, ['documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard']) ? 'pc-trigger active' : '' }}">
                                        <a href="#!" class="pc-link">
                                            <span class="pc-mtext">{{ __('Document Management') }}</span>
                                            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                        </a>
                                        <ul class="pc-submenu" style="display: {{ in_array($routeName, ['documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard']) ? 'block' : 'none' }}">
                                            <li class="pc-item {{ in_array($routeName, ['documents.index']) ? 'active' : '' }}">
                                                <a class="pc-link" href="{{ route('documents.index') }}">{{ __('All Documents') }}</a>
                                            </li>
                                            @if (Gate::check('create documents'))
                                                <li class="pc-item {{ in_array($routeName, ['documents.create']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('documents.create') }}">{{ __('Create Document') }}</a>
                                                </li>
                                            @endif
                                            @if (Gate::check('view document dashboard'))
                                                <li class="pc-item {{ in_array($routeName, ['documents.dashboard']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('documents.dashboard') }}">{{ __('Dashboard') }}</a>
                                                </li>
                                            @endif
                                        </ul>
                                    </li>
                                @endif

                                {{-- Form Builder Subsection --}}
                                @if (Gate::check('form_builder_access'))
                                    <li class="pc-item pc-hasmenu {{ in_array($routeName, ['forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show']) ? 'pc-trigger active' : '' }}">
                                        <a href="#!" class="pc-link">
                                            <span class="pc-mtext">{{ __('Form Builder') }}</span>
                                            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                        </a>
                                        <ul class="pc-submenu" style="display: {{ in_array($routeName, ['forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show']) ? 'block' : 'none' }}">
                                            <li class="pc-item {{ in_array($routeName, ['forms.index']) ? 'active' : '' }}">
                                                <a class="pc-link" href="{{ route('forms.index') }}">{{ __('All Forms') }}</a>
                                            </li>
                                            @if (Gate::check('form_submissions_view'))
                                                <li class="pc-item {{ in_array($routeName, ['form-submissions.index']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('form-submissions.index') }}">{{ __('Submissions') }}</a>
                                                </li>
                                            @endif
                                            @if (Gate::check('form_templates_view'))
                                                <li class="pc-item">
                                                    <a class="pc-link" href="#" onclick="$('#commonModal').modal('show'); loadModal('{{ route('forms.templates') }}', '{{ __('Form Templates') }}');">{{ __('Templates') }}</a>
                                                </li>
                                            @endif
                                        </ul>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Property Management (Keep as is since it's already well organized) --}}
                    @if (Gate::check('view properties'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, [
                            'properties.index', 'properties.create', 'properties.show', 'properties.edit', 'properties.dashboard',
                            'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                            'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                        ]) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="home"></i></span>
                                <span class="pc-mtext">{{ __('navigation.properties') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, [
                                'properties.index', 'properties.create', 'properties.show', 'properties.edit', 'properties.dashboard',
                                'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                                'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                            ]) ? 'block' : 'none' }}">
                                <li class="pc-item {{ in_array($routeName, ['properties.index']) ? 'active' : '' }}">
                                    <a class="pc-link" href="{{ route('properties.index') }}">{{ __('All Properties') }}</a>
                                </li>
                                @if (Gate::check('create properties'))
                                    <li class="pc-item {{ in_array($routeName, ['properties.add']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('properties.add') }}">{{ __('Add Property') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('view property dashboard'))
                                    <li class="pc-item {{ in_array($routeName, ['properties.dashboard']) ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('properties.dashboard') }}">{{ __('Dashboard') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('view property analytics'))
                                    <li class="pc-item pc-hasmenu {{ in_array($routeName, [
                                        'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                                        'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                                    ]) ? 'pc-trigger active' : '' }}">
                                        <a href="#!" class="pc-link">
                                            <span class="pc-mtext">{{ __('Analytics & Reports') }}</span>
                                            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                        </a>
                                        <ul class="pc-submenu" style="display: {{ in_array($routeName, [
                                            'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                                            'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                                        ]) ? 'block' : 'none' }}">
                                            <li class="pc-item {{ in_array($routeName, ['properties.analytics.index']) ? 'active' : '' }}">
                                                <a class="pc-link" href="{{ route('properties.analytics.index') }}">{{ __('Overview') }}</a>
                                            </li>
                                            @if (Gate::check('view property performance'))
                                                <li class="pc-item {{ in_array($routeName, ['properties.analytics.performance']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('properties.analytics.performance') }}">{{ __('Performance') }}</a>
                                                </li>
                                            @endif
                                            @if (Gate::check('view occupancy reports'))
                                                <li class="pc-item {{ in_array($routeName, ['properties.analytics.occupancy']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('properties.analytics.occupancy') }}">{{ __('Occupancy') }}</a>
                                                </li>
                                            @endif
                                            @if (Gate::check('view pricing analytics'))
                                                <li class="pc-item {{ in_array($routeName, ['properties.analytics.pricing']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('properties.analytics.pricing') }}">{{ __('Pricing Analytics') }}</a>
                                                </li>
                                            @endif
                                            @if (Gate::check('view lead analytics'))
                                                <li class="pc-item {{ in_array($routeName, ['properties.analytics.leads']) ? 'active' : '' }}">
                                                    <a class="pc-link" href="{{ route('properties.analytics.leads') }}">{{ __('Lead Analytics') }}</a>
                                                </li>
                                            @endif
                                        </ul>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Communication & Notes --}}
                    @if (Gate::check('manage contact') || Gate::check('manage note'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['contact.index', 'note.index']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-message-circle"></i></span>
                                <span class="pc-mtext">{{ __('Communication') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['contact.index', 'note.index']) ? 'block' : 'none' }}">
                                @if (Gate::check('manage contact'))
                                    <li class="pc-item {{ in_array($routeName, ['contact.index']) ? 'active' : '' }}">
                                        <a href="{{ route('contact.index') }}" class="pc-link">
                                            <span class="pc-micon"><i class="ti ti-phone-call"></i></span>
                                            <span class="pc-mtext">{{ __('Contact Diary') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage note'))
                                    <li class="pc-item {{ in_array($routeName, ['note.index']) ? 'active' : '' }}">
                                        <a href="{{ route('note.index') }}" class="pc-link">
                                            <span class="pc-micon"><i class="ti ti-notebook"></i></span>
                                            <span class="pc-mtext">{{ __('Notice Board') }}</span>
                                        </a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                @endif

                {{-- System Configuration Section --}}
                @if (Gate::check('manage category') ||
                        Gate::check('manage tax') ||
                        Gate::check('manage unit') ||
                        Gate::check('manage notification'))
                    <li class="pc-item pc-caption">
                        <label>{{ __('System Configuration') }}</label>
                        <i class="ti ti-chart-arcs"></i>
                    </li>

                    {{-- Smart Item Configuration --}}
                    @if (Gate::check('manage category') || Gate::check('manage tax') || Gate::check('manage unit'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['item-category.index', 'item-tax.index', 'item-unit.index', 'sales-category.index']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="package"></i></span>
                                <span class="pc-mtext">{{ __('Item Configuration') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['item-category.index', 'item-tax.index', 'item-unit.index', 'sales-category.index']) ? 'block' : 'none' }}">
                                @if (Gate::check('manage category'))
                                    <li class="pc-item {{ Request::route()->getName() == 'item-category.index' ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('item-category.index') }}">
                                            <span class="pc-micon"><i data-feather="wind"></i></span>
                                            <span class="pc-mtext">{{ __('Item Category') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage tax'))
                                    <li class="pc-item {{ Request::route()->getName() == 'item-tax.index' ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('item-tax.index') }}">
                                            <span class="pc-micon"><i data-feather="trello"></i></span>
                                            <span class="pc-mtext">{{ __('Item Tax') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage unit'))
                                    <li class="pc-item {{ Request::route()->getName() == 'item-unit.index' ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('item-unit.index') }}">
                                            <span class="pc-micon"><i data-feather="tag"></i></span>
                                            <span class="pc-mtext">{{ __('Item Unit') }}</span>
                                        </a>
                                    </li>
                                @endif
                                @if (Gate::check('manage category'))
                                    <li class="pc-item {{ Request::route()->getName() == 'sales-category.index' ? 'active' : '' }}">
                                        <a class="pc-link" href="{{ route('sales-category.index') }}">
                                            <span class="pc-micon"><i data-feather="wind"></i></span>
                                            <span class="pc-mtext">{{ __('Sales Category') }}</span>
                                        </a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    @if (Gate::check('manage notification'))
                        <li class="pc-item {{ in_array($routeName, ['notification.index']) ? 'active' : '' }}">
                            <a class="pc-link" href="{{ route('notification.index') }}">
                                <span class="pc-micon"><i class="ti ti-bell"></i></span>
                                <span class="pc-mtext">{{ __('Email Notification') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

                {{-- System Settings Section --}}
                @if (Gate::check('manage pricing packages') ||
                        Gate::check('manage pricing transation') ||
                        Gate::check('manage account settings') ||
                        Gate::check('manage password settings') ||
                        Gate::check('manage general settings') ||
                        Gate::check('manage email settings') ||
                        Gate::check('manage payment settings') ||
                        Gate::check('manage company settings') ||
                        Gate::check('manage seo settings') ||
                        Gate::check('manage google recaptcha settings'))
                    <li class="pc-item pc-caption">
                        <label>{{ __('System Settings') }}</label>
                        <i class="ti ti-chart-arcs"></i>
                    </li>

                    {{-- CMS Management --}}
                    @if (Gate::check('manage FAQ') || Gate::check('manage Page'))
                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['homepage.index', 'FAQ.index', 'pages.index', 'footerSetting', 'authPage.index']) ? 'pc-trigger active' : '' }}">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-layout-rows"></i></span>
                                <span class="pc-mtext">{{ __('CMS') }}</span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['homepage.index', 'FAQ.index', 'pages.index', 'footerSetting', 'authPage.index']) ? 'block' : 'none' }}">
                                @if (Gate::check('manage home page'))
                                    <li class="pc-item {{ in_array($routeName, ['homepage.index']) ? 'active' : '' }}">
                                        <a href="{{ route('homepage.index') }}" class="pc-link">{{ __('Home Page') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage Page'))
                                    <li class="pc-item {{ in_array($routeName, ['pages.index']) ? 'active' : '' }}">
                                        <a href="{{ route('pages.index') }}" class="pc-link">{{ __('Custom Page') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage FAQ'))
                                    <li class="pc-item {{ in_array($routeName, ['FAQ.index']) ? 'active' : '' }}">
                                        <a href="{{ route('FAQ.index') }}" class="pc-link">{{ __('FAQ') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage footer'))
                                    <li class="pc-item {{ in_array($routeName, ['footerSetting']) ? 'active' : '' }}">
                                        <a href="{{ route('footerSetting') }}" class="pc-link">{{ __('Footer') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage auth page'))
                                    <li class="pc-item {{ in_array($routeName, ['authPage.index']) ? 'active' : '' }}">
                                        <a href="{{ route('authPage.index') }}" class="pc-link">{{ __('Auth Page') }}</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    {{-- Smart Pricing & Subscription Management --}}
                    @if (Auth::user()->type == 'super admin' || $pricing_feature_settings == 'on')
                        @if (Gate::check('manage pricing packages') || Gate::check('manage pricing transation') || Gate::check('manage coupon') || Gate::check('manage coupon history'))
                            <li class="pc-item pc-hasmenu {{ in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction', 'coupons.index', 'coupons.history']) ? 'pc-trigger active' : '' }}">
                                <a href="#!" class="pc-link">
                                    <span class="pc-micon"><i class="ti ti-package"></i></span>
                                    <span class="pc-mtext">{{ __('Pricing & Coupons') }}</span>
                                    <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                </a>
                                <ul class="pc-submenu" style="display: {{ in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction', 'coupons.index', 'coupons.history']) ? 'block' : 'none' }}">
                                    
                                    {{-- Pricing Subsection --}}
                                    @if (Gate::check('manage pricing packages') || Gate::check('manage pricing transation'))
                                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction']) ? 'pc-trigger active' : '' }}">
                                            <a href="#!" class="pc-link">
                                                <span class="pc-mtext">{{ __('Pricing Management') }}</span>
                                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction']) ? 'block' : 'none' }}">
                                                @if (Gate::check('manage pricing packages'))
                                                    <li class="pc-item {{ in_array($routeName, ['subscriptions.index', 'subscriptions.show']) ? 'active' : '' }}">
                                                        <a class="pc-link" href="{{ route('subscriptions.index') }}">{{ __('Packages') }}</a>
                                                    </li>
                                                @endif
                                                @if (Gate::check('manage pricing transation'))
                                                    <li class="pc-item {{ in_array($routeName, ['subscription.transaction']) ? 'active' : '' }}">
                                                        <a class="pc-link" href="{{ route('subscription.transaction') }}">{{ __('Transactions') }}</a>
                                                    </li>
                                                @endif
                                            </ul>
                                        </li>
                                    @endif

                                    {{-- Coupon Subsection --}}
                                    @if (Gate::check('manage coupon') || Gate::check('manage coupon history'))
                                        <li class="pc-item pc-hasmenu {{ in_array($routeName, ['coupons.index', 'coupons.history']) ? 'pc-trigger active' : '' }}">
                                            <a href="#!" class="pc-link">
                                                <span class="pc-mtext">{{ __('Coupon Management') }}</span>
                                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="pc-submenu" style="display: {{ in_array($routeName, ['coupons.index', 'coupons.history']) ? 'block' : 'none' }}">
                                                @if (Gate::check('manage coupon'))
                                                    <li class="pc-item {{ in_array($routeName, ['coupons.index']) ? 'active' : '' }}">
                                                        <a class="pc-link" href="{{ route('coupons.index') }}">{{ __('All Coupon') }}</a>
                                                    </li>
                                                @endif
                                                @if (Gate::check('manage coupon history'))
                                                    <li class="pc-item {{ in_array($routeName, ['coupons.history']) ? 'active' : '' }}">
                                                        <a class="pc-link" href="{{ route('coupons.history') }}">{{ __('Coupon History') }}</a>
                                                    </li>
                                                @endif
                                            </ul>
                                        </li>
                                    @endif
                                </ul>
                            </li>
                        @endif
                    @endif

                    {{-- System Settings --}}
                    @if (Gate::check('manage account settings') ||
                            Gate::check('manage password settings') ||
                            Gate::check('manage general settings') ||
                            Gate::check('manage email settings') ||
                            Gate::check('manage payment settings') ||
                            Gate::check('manage company settings') ||
                            Gate::check('manage seo settings') ||
                            Gate::check('manage google recaptcha settings'))
                        <li class="pc-item {{ in_array($routeName, ['setting.index']) ? 'active' : '' }}">
                            <a href="{{ route('setting.index') }}" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-settings"></i></span>
                                <span class="pc-mtext">{{ __('Settings') }}</span>
                            </a>
                        </li>
                    @endif
                @endif
            </ul>
            <div class="w-100 text-center">
                <div class="badge theme-version badge rounded-pill bg-light text-dark f-12"></div>
            </div>
        </div>
    </div>
</nav>