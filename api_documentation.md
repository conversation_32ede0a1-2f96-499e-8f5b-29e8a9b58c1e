# Calling Agent CRM API Documentation

## Base URL
```
http://localhost/calling_agent_crm/api/v1
```

## Authentication

The API uses Laravel Sanctum for authentication. After login, include the Bearer token in all authenticated requests.

### Headers for Authenticated Requests
```
Authorization: Bearer {your_token_here}
Content-Type: application/json
Accept: application/json
```

## Authentication Endpoints

### 1. Login
**POST** `/auth/login`

Login an agent and get access token.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123",
    "device_name": "Mobile App"
}
```

**Response (Success):**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 3,
            "name": "<PERSON><PERSON>",
            "email": "<EMAIL>",
            "type": "agent",
            "phone_number": "+919876543210",
            "parent_id": 2,
            "roles": ["agent"],
            "permissions": ["view leads", "create leads", "view call logs", ...]
        },
        "token": "1|abc123def456...",
        "token_type": "Bearer"
    }
}
```

**Response (Error):**
```json
{
    "success": false,
    "message": "Invalid credentials"
}
```

### 2. Logout
**POST** `/auth/logout`

Logout from current device.

**Headers:** `Authorization: Bearer {token}`

**Response:**
```json
{
    "success": true,
    "message": "Logout successful"
}
```

### 3. Logout All Devices
**POST** `/auth/logout-all`

Logout from all devices.

**Headers:** `Authorization: Bearer {token}`

**Response:**
```json
{
    "success": true,
    "message": "Logged out from all devices successfully"
}
```

### 4. Get Profile
**GET** `/auth/profile`

Get current user profile information.

**Headers:** `Authorization: Bearer {token}`

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 3,
            "name": "Rajesh Kumar",
            "email": "<EMAIL>",
            "type": "agent",
            "phone_number": "+919876543210",
            "parent_id": 2,
            "is_active": true,
            "email_verified_at": "2024-01-01T00:00:00.000000Z",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "roles": ["agent"],
            "permissions": ["view leads", "create leads", ...]
        }
    }
}
```

### 5. Update Profile
**PUT** `/auth/profile`

Update user profile information.

**Headers:** `Authorization: Bearer {token}`

**Request Body:**
```json
{
    "name": "Rajesh Kumar Singh",
    "phone_number": "+919876543211",
    "current_password": "password123",
    "password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Profile updated successfully",
    "data": {
        "user": {
            "id": 3,
            "name": "Rajesh Kumar Singh",
            "email": "<EMAIL>",
            "phone_number": "+919876543211",
            "updated_at": "2024-01-01T12:00:00.000000Z"
        }
    }
}
```

### 6. Refresh Token
**POST** `/auth/refresh-token`

Refresh the current access token.

**Headers:** `Authorization: Bearer {token}`

**Request Body:**
```json
{
    "device_name": "Mobile App"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Token refreshed successfully",
    "data": {
        "token": "2|xyz789abc123...",
        "token_type": "Bearer"
    }
}
```

### 7. Check Token
**GET** `/auth/check-token`

Check if current token is valid.

**Headers:** `Authorization: Bearer {token}`

**Response:**
```json
{
    "success": true,
    "message": "Token is valid",
    "data": {
        "user_id": 3,
        "token_name": "Mobile App",
        "expires_at": null
    }
}
```

## Sample Agent Credentials

Use these credentials to test the API:

```
Email: <EMAIL>
Password: password123

Email: <EMAIL>  
Password: password123

Email: <EMAIL>
Password: password123

Email: <EMAIL>
Password: password123

Email: <EMAIL>
Password: password123
```

## Error Responses

### Validation Error (422)
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email field is required."],
        "password": ["The password field is required."]
    }
}
```

### Unauthorized (401)
```json
{
    "success": false,
    "message": "Invalid credentials"
}
```

### Forbidden (403)
```json
{
    "success": false,
    "message": "Access denied. This API is for agents only."
}
```

### Server Error (500)
```json
{
    "success": false,
    "message": "Internal server error",
    "error": "Error details..."
}
```

## Testing with cURL

### Login Example
```bash
curl -X POST http://localhost/calling_agent_crm/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "device_name": "cURL Test"
  }'
```

### Get Profile Example
```bash
curl -X GET http://localhost/calling_agent_crm/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"
```

## Next Steps

After authentication, you can use the token to access other API endpoints:
- Dashboard endpoints (`/dashboard/*`)
- Call log endpoints (`/call-logs/*`)
- Lead assignment endpoints (`/lead-assignments/*`)
- Follow-up endpoints (`/follow-ups/*`)
- Agent endpoints (`/agents/*`)
- Performance endpoints (`/performance/*`)

All these endpoints require the `Authorization: Bearer {token}` header.
