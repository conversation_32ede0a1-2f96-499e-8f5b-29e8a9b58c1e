<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            
            // Task categorization
            $table->enum('category', [
                'lead_task', 'project_task', 'property_task', 
                'site_visit_task', 'followup_task', 'general_task'
            ])->default('general_task');
            
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', [
                'not_started', 'in_progress', 'completed', 'cancelled', 'overdue'
            ])->default('not_started');
            
            // Dates
            $table->datetime('due_date')->nullable();
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->datetime('cancelled_at')->nullable();
            
            // Relationships
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            
            // Related entities (polymorphic-like but explicit for better performance)
            $table->foreignId('lead_id')->nullable()->constrained('leads')->onDelete('cascade');
            $table->foreignId('project_id')->nullable()->constrained('projects')->onDelete('cascade');
            $table->foreignId('property_id')->nullable()->constrained('properties')->onDelete('cascade');
            
            // Task completion details
            $table->text('completion_notes')->nullable();
            $table->integer('estimated_hours')->nullable();
            $table->integer('actual_hours')->nullable();
            
            // Tenant isolation
            $table->index(['created_by']);
            $table->index(['assigned_to']);
            $table->index(['status']);
            $table->index(['priority']);
            $table->index(['category']);
            $table->index(['due_date']);
            $table->index(['lead_id']);
            $table->index(['project_id']);
            $table->index(['property_id']);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tasks');
    }
};
