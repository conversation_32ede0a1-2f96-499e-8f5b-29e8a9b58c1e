<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\TaskAssignment;
use App\Models\User;
use App\Models\Lead;
use App\Models\Project;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TaskController extends Controller
{
    /**
     * Display task dashboard
     */
    public function index(Request $request)
    {
        if (!Gate::check('view tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get user and tenant info
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Build base query with proper tenant filtering
        if ($user->type == 'owner') {
            $query = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $query = Task::query(); // Uses global scope
        }

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Date filters
        if ($request->filled('due_date_from')) {
            $query->where('due_date', '>=', $request->due_date_from);
        }

        if ($request->filled('due_date_to')) {
            $query->where('due_date', '<=', $request->due_date_to);
        }

        $tasks = $query->with(['assignedTo', 'createdBy', 'lead', 'project', 'property'])
                      ->orderBy('created_at', 'desc')
                      ->paginate(20);

        // Get filter data
        $users = User::where(function($q) use ($tenantId) {
            $q->where('id', $tenantId)->orWhere('parent_id', $tenantId);
        })->get();

        $leads = Lead::select('id', 'name')->limit(100)->get();
        $projects = Project::select('id', 'name')->limit(100)->get();
        $properties = Property::select('id', 'name')->limit(100)->get();

        // Task statistics
        $stats = $this->getTaskStatistics($user);

        return view('tasks.index', compact(
            'tasks', 'users', 'leads', 'projects', 'properties', 'stats'
        ));
    }

    /**
     * Show task dashboard
     */
    public function dashboard(Request $request)
    {
        if (!Gate::check('view tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $user = Auth::user();
        $stats = $this->getTaskStatistics($user);

        // Get recent tasks
        $recentTasks = Task::with(['assignedTo', 'createdBy', 'lead', 'project', 'property'])
                          ->orderBy('created_at', 'desc')
                          ->limit(10)
                          ->get();

        // Get overdue tasks
        $overdueTasks = Task::overdue()
                           ->with(['assignedTo', 'createdBy'])
                           ->orderBy('due_date', 'asc')
                           ->limit(10)
                           ->get();

        // Get tasks due today
        $todayTasks = Task::dueToday()
                         ->with(['assignedTo', 'createdBy'])
                         ->orderBy('due_date', 'asc')
                         ->get();

        // Get tasks due soon
        $upcomingTasks = Task::dueSoon(7)
                            ->with(['assignedTo', 'createdBy'])
                            ->orderBy('due_date', 'asc')
                            ->limit(10)
                            ->get();

        return view('tasks.dashboard', compact(
            'stats', 'recentTasks', 'overdueTasks', 'todayTasks', 'upcomingTasks'
        ));
    }

    /**
     * Show the form for creating a new task
     */
    public function create(Request $request)
    {
        if (!Gate::check('create tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Get dropdown data
        $users = User::where(function($q) use ($tenantId) {
            $q->where('id', $tenantId)->orWhere('parent_id', $tenantId);
        })->get();

        $leads = Lead::select('id', 'name')->limit(100)->get();
        $projects = Project::select('id', 'name')->limit(100)->get();
        $properties = Property::select('id', 'name')->limit(100)->get();

        // Pre-fill related entity if provided
        $relatedEntity = null;
        $relatedType = null;
        
        if ($request->filled('lead_id')) {
            $relatedEntity = Lead::find($request->lead_id);
            $relatedType = 'lead';
        } elseif ($request->filled('project_id')) {
            $relatedEntity = Project::find($request->project_id);
            $relatedType = 'project';
        } elseif ($request->filled('property_id')) {
            $relatedEntity = Property::find($request->property_id);
            $relatedType = 'property';
        }

        return view('tasks.create', compact(
            'users', 'leads', 'projects', 'properties', 'relatedEntity', 'relatedType'
        ));
    }

    /**
     * Store a newly created task
     */
    public function store(Request $request)
    {
        if (!Gate::check('create tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:' . implode(',', array_keys(Task::$categories)),
            'priority' => 'required|in:' . implode(',', array_keys(Task::$priorities)),
            'due_date' => 'nullable|date|after:now',
            'assigned_to' => 'nullable|exists:users,id',
            'lead_id' => 'nullable|exists:leads,id',
            'project_id' => 'nullable|exists:projects,id',
            'property_id' => 'nullable|exists:properties,id',
            'estimated_hours' => 'nullable|integer|min:1|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $user = Auth::user();
        
        $task = new Task();
        $task->fill($request->all());
        $task->created_by = $user->id;
        
        // Set default assigned_to if not provided
        if (!$task->assigned_to) {
            $task->assigned_to = $user->id;
        }
        
        $task->save();

        // Create assignment record if assigned to someone else
        if ($task->assigned_to != $user->id) {
            $task->assignTo($task->assigned_to, 'Initial assignment');
        }

        // Add creation comment
        $task->addComment('Task created', 'system');

        return redirect()->route('tasks.show', $task)
                        ->with('success', __('Task successfully created.'));
    }

    /**
     * Display the specified task
     */
    public function show($id)
    {
        if (!Gate::check('view tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get user and tenant info
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Find task with proper filtering based on user type
        if ($user->type == 'owner') {
            $task = Task::withoutGlobalScope('tenant')
                ->where('id', $id)
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                })
                ->first();
        } else {
            $task = Task::where('id', $id)->first();
        }

        if (!$task) {
            return redirect()->route('tasks.index')->with('error', __('Task not found or access denied.'));
        }

        // Load relationships
        $task->load([
            'assignedTo', 'createdBy', 'lead', 'project', 'property',
            'comments.user', 'attachments.uploadedBy', 'assignments.assignedBy', 'assignments.assignedTo'
        ]);

        return view('tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the specified task
     */
    public function edit($id)
    {
        if (!Gate::check('edit tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get user and tenant info
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Find task with proper filtering
        if ($user->type == 'owner') {
            $task = Task::withoutGlobalScope('tenant')
                ->where('id', $id)
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                })
                ->first();
        } else {
            $task = Task::where('id', $id)->first();
        }

        if (!$task) {
            return redirect()->route('tasks.index')->with('error', __('Task not found or access denied.'));
        }

        // Get dropdown data
        $users = User::where(function($q) use ($tenantId) {
            $q->where('id', $tenantId)->orWhere('parent_id', $tenantId);
        })->get();

        $leads = Lead::select('id', 'name')->limit(100)->get();
        $projects = Project::select('id', 'name')->limit(100)->get();
        $properties = Property::select('id', 'title')->limit(100)->get();

        return view('tasks.edit', compact('task', 'users', 'leads', 'projects', 'properties'));
    }

    /**
     * Update the specified task
     */
    public function update(Request $request, $id)
    {
        if (!Gate::check('edit tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get user and tenant info
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Find task with proper filtering
        if ($user->type == 'owner') {
            $task = Task::withoutGlobalScope('tenant')
                ->where('id', $id)
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                })
                ->first();
        } else {
            $task = Task::where('id', $id)->first();
        }

        if (!$task) {
            return redirect()->route('tasks.index')->with('error', __('Task not found or access denied.'));
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:' . implode(',', array_keys(Task::$categories)),
            'priority' => 'required|in:' . implode(',', array_keys(Task::$priorities)),
            'status' => 'required|in:' . implode(',', array_keys(Task::$statuses)),
            'due_date' => 'nullable|date',
            'assigned_to' => 'nullable|exists:users,id',
            'lead_id' => 'nullable|exists:leads,id',
            'project_id' => 'nullable|exists:projects,id',
            'property_id' => 'nullable|exists:properties,id',
            'estimated_hours' => 'nullable|integer|min:1|max:1000',
            'actual_hours' => 'nullable|integer|min:1|max:1000',
            'completion_notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        // Track changes for comments
        $oldStatus = $task->status;
        $oldAssignedTo = $task->assigned_to;

        $task->fill($request->all());
        $task->save();

        // Add comments for significant changes
        if ($oldStatus != $task->status) {
            $task->addComment(
                "Status changed from {$oldStatus} to {$task->status}",
                'status_change',
                ['old_status' => $oldStatus, 'new_status' => $task->status]
            );
        }

        if ($oldAssignedTo != $task->assigned_to && $task->assigned_to) {
            $task->assignTo($task->assigned_to, 'Task reassigned');
        }

        return redirect()->route('tasks.show', $task)
                        ->with('success', __('Task successfully updated.'));
    }

    /**
     * Remove the specified task
     */
    public function destroy($id)
    {
        if (!Gate::check('delete tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get user and tenant info
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Find task with proper filtering
        if ($user->type == 'owner') {
            $task = Task::withoutGlobalScope('tenant')
                ->where('id', $id)
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                })
                ->first();
        } else {
            $task = Task::where('id', $id)->first();
        }

        if (!$task) {
            return redirect()->route('tasks.index')->with('error', __('Task not found or access denied.'));
        }

        $task->delete();

        return redirect()->route('tasks.index')->with('success', __('Task successfully deleted.'));
    }

    /**
     * Update task status
     */
    public function updateStatus(Request $request, $id)
    {
        if (!Gate::check('edit tasks')) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:' . implode(',', array_keys(Task::$statuses)),
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $task = Task::find($id);
        if (!$task) {
            return response()->json(['success' => false, 'message' => 'Task not found'], 404);
        }

        $oldStatus = $task->status;
        $newStatus = $request->status;
        $notes = $request->notes;

        // Update task based on status
        switch ($newStatus) {
            case 'in_progress':
                $task->markAsStarted($notes);
                break;
            case 'completed':
                $task->markAsCompleted($notes);
                break;
            case 'cancelled':
                $task->markAsCancelled($notes);
                break;
            default:
                $task->update(['status' => $newStatus]);
                if ($notes) {
                    $task->addComment($notes, 'status_change');
                }
        }

        return response()->json([
            'success' => true,
            'message' => 'Task status updated successfully',
            'task' => $task->fresh()
        ]);
    }

    /**
     * Assign task to user
     */
    public function assign(Request $request, $id)
    {
        if (!Gate::check('assign tasks')) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $validator = Validator::make($request->all(), [
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $task = Task::find($id);
        if (!$task) {
            return response()->json(['success' => false, 'message' => 'Task not found'], 404);
        }

        $assignment = $task->assignTo($request->assigned_to, $request->notes);

        return response()->json([
            'success' => true,
            'message' => 'Task assigned successfully',
            'assignment' => $assignment
        ]);
    }

    /**
     * Add comment to task
     */
    public function addComment(Request $request, $id)
    {
        if (!Gate::check('view tasks')) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $validator = Validator::make($request->all(), [
            'comment' => 'required|string',
            'is_internal' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $task = Task::find($id);
        if (!$task) {
            return response()->json(['success' => false, 'message' => 'Task not found'], 404);
        }

        $comment = $task->comments()->create([
            'user_id' => Auth::id(),
            'comment' => $request->comment,
            'type' => 'comment',
            'is_internal' => $request->boolean('is_internal', false)
        ]);

        $comment->load('user');

        return response()->json([
            'success' => true,
            'message' => 'Comment added successfully',
            'comment' => $comment
        ]);
    }

    /**
     * Get task statistics
     */
    protected function getTaskStatistics($user)
    {
        $tenantId = $user->parent_id ?? $user->id;

        // Build base query
        if ($user->type == 'owner') {
            $baseQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $baseQuery = Task::query(); // Uses global scope
        }

        return [
            'total' => (clone $baseQuery)->count(),
            'not_started' => (clone $baseQuery)->where('status', 'not_started')->count(),
            'in_progress' => (clone $baseQuery)->where('status', 'in_progress')->count(),
            'completed' => (clone $baseQuery)->where('status', 'completed')->count(),
            'overdue' => (clone $baseQuery)->where('due_date', '<', now())
                                          ->whereNotIn('status', ['completed', 'cancelled'])
                                          ->count(),
            'due_today' => (clone $baseQuery)->whereDate('due_date', today())
                                            ->whereNotIn('status', ['completed', 'cancelled'])
                                            ->count(),
            'high_priority' => (clone $baseQuery)->whereIn('priority', ['high', 'urgent'])
                                                 ->whereNotIn('status', ['completed', 'cancelled'])
                                                 ->count(),
            'assigned_to_me' => (clone $baseQuery)->where('assigned_to', $user->id)->count(),
            'created_by_me' => (clone $baseQuery)->where('created_by', $user->id)->count()
        ];
    }

    /**
     * Display task calendar
     */
    public function calendar()
    {
        if (!Gate::check('view tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        return view('tasks.calendar');
    }

    /**
     * Get calendar data for tasks
     */
    public function calendarData(Request $request)
    {
        if (!Gate::check('view tasks')) {
            return response()->json(['error' => 'Permission Denied'], 403);
        }

        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Build base query with tenant filtering
        if ($user->type == 'owner') {
            $baseQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $baseQuery = Task::query(); // Uses global scope
        }

        $tasks = $baseQuery->with(['assignedTo:id,name', 'lead:id,name', 'project:id,name', 'property:id,name'])
                          ->whereNotNull('due_date')
                          ->get();

        $events = [];
        foreach ($tasks as $task) {
            $color = '#6c757d'; // default gray
            switch ($task->status) {
                case 'not_started':
                    $color = '#17a2b8'; // info blue
                    break;
                case 'in_progress':
                    $color = '#ffc107'; // warning yellow
                    break;
                case 'completed':
                    $color = '#28a745'; // success green
                    break;
                case 'cancelled':
                    $color = '#dc3545'; // danger red
                    break;
            }

            if ($task->priority === 'urgent') {
                $color = '#dc3545'; // urgent tasks are always red
            } elseif ($task->priority === 'high') {
                $color = '#fd7e14'; // high priority orange
            }

            $events[] = [
                'id' => $task->id,
                'title' => $task->title,
                'start' => $task->due_date->format('Y-m-d'),
                'backgroundColor' => $color,
                'borderColor' => $color,
                'url' => route('tasks.show', $task->id),
                'extendedProps' => [
                    'status' => $task->status,
                    'priority' => $task->priority,
                    'assigned_to' => $task->assignedTo ? $task->assignedTo->name : 'Unassigned',
                    'description' => $task->description
                ]
            ];
        }

        return response()->json($events);
    }

    /**
     * Display task analytics
     */
    public function analytics()
    {
        if (!Gate::check('view tasks')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Build base query with tenant filtering
        if ($user->type == 'owner') {
            $baseQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $baseQuery = Task::query(); // Uses global scope
        }

        // Get analytics data
        $analytics = [
            'total_tasks' => (clone $baseQuery)->count(),
            'completed_tasks' => (clone $baseQuery)->where('status', 'completed')->count(),
            'overdue_tasks' => (clone $baseQuery)->where('due_date', '<', now())
                                                 ->whereNotIn('status', ['completed', 'cancelled'])
                                                 ->count(),
            'tasks_by_status' => (clone $baseQuery)->select('status', \DB::raw('count(*) as count'))
                                                   ->groupBy('status')
                                                   ->get(),
            'tasks_by_priority' => (clone $baseQuery)->select('priority', \DB::raw('count(*) as count'))
                                                     ->groupBy('priority')
                                                     ->get(),
            'tasks_by_category' => (clone $baseQuery)->select('category', \DB::raw('count(*) as count'))
                                                     ->groupBy('category')
                                                     ->get(),
            'completion_rate' => 0,
            'average_completion_time' => 0
        ];

        // Calculate completion rate
        if ($analytics['total_tasks'] > 0) {
            $analytics['completion_rate'] = round(($analytics['completed_tasks'] / $analytics['total_tasks']) * 100, 2);
        }

        // Calculate average completion time
        $completedTasks = (clone $baseQuery)->where('status', 'completed')
                                           ->whereNotNull('completed_at')
                                           ->whereNotNull('started_at')
                                           ->get();

        if ($completedTasks->count() > 0) {
            $totalHours = 0;
            foreach ($completedTasks as $task) {
                if ($task->started_at && $task->completed_at) {
                    $totalHours += $task->started_at->diffInHours($task->completed_at);
                }
            }
            $analytics['average_completion_time'] = round($totalHours / $completedTasks->count(), 2);
        }

        return view('tasks.analytics', compact('analytics'));
    }
}
