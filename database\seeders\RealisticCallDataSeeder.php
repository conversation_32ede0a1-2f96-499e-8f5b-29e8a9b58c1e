<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Lead;
use App\Models\CallLog;
use App\Models\FollowUp;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Faker\Factory as Faker;

class RealisticCallDataSeeder extends Seeder
{
    public function run()
    {
        $faker = Faker::create('en_IN');
        
        // Get owner user
        $owner = User::where('type', 'owner')->first();
        if (!$owner) {
            $this->command->error('No owner found. Please run system data setup first.');
            return;
        }

        $this->command->info('Creating 10 realistic agents...');
        
        // Create 10 realistic agents
        $agents = [];
        $agentData = [
            ['name' => '<PERSON><PERSON>', 'email' => '<EMAIL>', 'specialization' => 'Mumbai Properties'],
            ['name' => 'Priya Sharma', 'email' => '<EMAIL>', 'specialization' => 'Pune Properties'],
            ['name' => 'Am<PERSON>', 'email' => '<EMAIL>', 'specialization' => 'Delhi NCR'],
            ['name' => '<PERSON><PERSON><PERSON>', 'email' => '<EMAIL>', 'specialization' => 'Bangalore Properties'],
            ['name' => 'Vikram Reddy', 'email' => '<EMAIL>', 'specialization' => 'Chennai Properties'],
            ['name' => 'Kavya Iyer', 'email' => '<EMAIL>', 'specialization' => 'Luxury Properties'],
            ['name' => 'Rohit Agarwal', 'email' => '<EMAIL>', 'specialization' => 'Commercial Properties'],
            ['name' => 'Pooja Jain', 'email' => '<EMAIL>', 'specialization' => 'Residential Properties'],
            ['name' => 'Arjun Mehta', 'email' => '<EMAIL>', 'specialization' => 'Investment Properties'],
            ['name' => 'Divya Nair', 'email' => '<EMAIL>', 'specialization' => 'First-time Buyers']
        ];

        foreach ($agentData as $agent) {
            // Check if agent already exists
            $existingUser = User::where('email', $agent['email'])->first();

            if ($existingUser) {
                $agents[] = $existingUser;
                $this->command->info("Using existing agent: {$agent['name']}");
            } else {
                $user = User::create([
                    'name' => $agent['name'],
                    'email' => $agent['email'],
                    'password' => Hash::make('password123'),
                    'type' => 'agent',
                    'parent_id' => $owner->id,
                    'phone_number' => '+91' . $faker->numerify('##########'),
                    'is_active' => 1,
                    'email_verified_at' => now(),
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now()
                ]);

                $user->assignRole('agent');
                $agents[] = $user;
                $this->command->info("Created agent: {$agent['name']}");
            }
        }

        $this->command->info('Creating 30 realistic leads...');
        
        // Create 30 realistic leads
        $leads = [];
        $indianNames = [
            'Rajesh Kumar Sharma', 'Priya Patel', 'Amit Singh Rajput', 'Sneha Gupta', 'Vikram Reddy',
            'Kavya Iyer', 'Rohit Agarwal', 'Pooja Jain', 'Arjun Mehta', 'Divya Nair',
            'Suresh Chandra', 'Meera Krishnan', 'Ravi Kumar', 'Anita Sharma', 'Deepak Verma',
            'Sunita Singh', 'Manoj Tiwari', 'Rekha Agarwal', 'Sanjay Mishra', 'Geeta Devi',
            'Ashok Kumar', 'Sushma Rani', 'Vinod Gupta', 'Shanti Devi', 'Ramesh Chand',
            'Kiran Bala', 'Sunil Kumar', 'Pushpa Devi', 'Naresh Singh', 'Usha Sharma'
        ];

        $cities = ['Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Chennai', 'Hyderabad', 'Ahmedabad', 'Kolkata'];
        $sources = ['Website', 'Facebook', 'Google Ads', 'Referral', 'Cold Call', 'Walk-in', 'WhatsApp'];
        $statuses = ['new', 'contacted', 'interested', 'not_interested', 'callback_scheduled', 'converted'];
        $priorities = ['low', 'medium', 'high', 'urgent'];

        for ($i = 0; $i < 30; $i++) {
            $assignedAgent = $faker->randomElement($agents);
            $createdAt = $faker->dateTimeBetween('-3 months', 'now');
            
            $lead = Lead::create([
                'name' => $indianNames[$i],
                'email' => strtolower(str_replace(' ', '.', $indianNames[$i])) . '@email.com',
                'phone' => '+91' . $faker->numerify('##########'),
                'alternate_phone' => $faker->boolean(30) ? '+91' . $faker->numerify('##########') : null,
                'address' => $faker->address,
                'city' => $faker->randomElement($cities),
                'state' => $faker->state,
                'pincode' => $faker->numerify('######'),
                'source' => $faker->randomElement($sources),
                'status' => $faker->randomElement($statuses),
                'priority' => $faker->randomElement($priorities),
                'score' => $faker->randomFloat(1, 0, 99.9),
                'budget_min' => $faker->numberBetween(2000000, 5000000),
                'budget_max' => $faker->numberBetween(5000000, 15000000),
                'requirements' => $faker->boolean(70) ? $faker->paragraph : null,
                'notes' => $faker->boolean(70) ? $faker->paragraph : null,
                'assigned_to' => $assignedAgent->id,
                'assigned_at' => $createdAt,
                'last_contacted_at' => $faker->boolean(60) ? $faker->dateTimeBetween($createdAt, 'now') : null,
                'next_followup_at' => $faker->boolean(50) ? $faker->dateTimeBetween('now', '+1 month') : null,
                'created_at' => $createdAt,
                'updated_at' => $faker->dateTimeBetween($createdAt, 'now'),
            ]);
            
            $leads[] = $lead;
        }

        $this->command->info('Creating 50 realistic call logs...');
        
        // Create 50 realistic call logs
        $callTypes = ['outgoing', 'incoming'];
        $callStatuses = ['completed', 'answered', 'busy', 'no_answer', 'failed', 'voicemail'];
        $callOutcomes = ['connected', 'not_interested', 'interested', 'callback_requested', 'follow_up_needed', 'converted', 'invalid_number'];

        for ($i = 0; $i < 50; $i++) {
            $agent = $faker->randomElement($agents);
            $lead = $faker->randomElement($leads);
            $callStarted = $faker->dateTimeBetween('-2 months', 'now');
            $duration = $faker->numberBetween(30, 1800); // 30 seconds to 30 minutes
            $callEnded = Carbon::parse($callStarted)->addSeconds($duration);

            CallLog::create([
                'call_type' => $faker->randomElement($callTypes),
                'call_direction' => 'outbound',
                'phone_number' => $lead->phone,
                'contact_name' => $lead->name,
                'lead_id' => $lead->id,
                'user_id' => $agent->id,
                'call_started_at' => $callStarted,
                'call_ended_at' => $callEnded,
                'duration_seconds' => $duration,
                'talk_duration_seconds' => $duration - $faker->numberBetween(0, 30),
                'call_status' => $faker->randomElement($callStatuses),
                'call_outcome' => $faker->randomElement($callOutcomes),
                'call_notes' => $this->generateRealisticCallNotes($faker),
                'agent_notes' => $this->generateRealisticAgentNotes($faker),
                'call_quality_score' => $faker->numberBetween(6, 10),
                'call_rating' => $faker->randomElement(['poor', 'fair', 'good', 'excellent']),
                'requires_follow_up' => $faker->boolean(60),
                'created_at' => $callStarted,
                'updated_at' => $callEnded
            ]);
        }

        $this->command->info('Creating follow-ups for calls...');
        
        // Create follow-ups for some calls
        $followUpTypes = ['call', 'email', 'whatsapp', 'meeting', 'site_visit', 'document_send', 'quote_send', 'demo'];
        $followUpStatuses = ['pending', 'in_progress', 'completed', 'cancelled', 'rescheduled', 'overdue', 'failed'];
        $priorities = ['low', 'medium', 'high', 'urgent'];

        for ($i = 0; $i < 25; $i++) {
            $lead = $faker->randomElement($leads);
            $agent = $faker->randomElement($agents);
            
            FollowUp::create([
                'lead_id' => $lead->id,
                'agent_id' => $agent->id,
                'created_by' => $agent->id,
                'follow_up_type' => $faker->randomElement($followUpTypes),
                'title' => $this->generateFollowUpTitle($faker),
                'description' => $this->generateFollowUpDescription($faker),
                'scheduled_at' => $faker->dateTimeBetween('now', '+2 weeks'),
                'priority' => $faker->randomElement($priorities),
                'status' => $faker->randomElement($followUpStatuses),
                'notes' => $faker->boolean(70) ? $faker->paragraph : null,
                'created_at' => $faker->dateTimeBetween('-1 month', 'now'),
                'updated_at' => now()
            ]);
        }

        $this->command->info('Successfully created realistic call data!');
        $this->command->info('- 10 agents created');
        $this->command->info('- 30 leads created');
        $this->command->info('- 50 call logs created');
        $this->command->info('- 25 follow-ups created');
    }

    private function generateRealisticCallNotes($faker)
    {
        $notes = [
            'Customer interested in 2BHK apartment in prime location. Discussed budget and amenities.',
            'Lead showed interest in commercial property. Requested site visit next week.',
            'Customer looking for investment property. Budget range 50L-1Cr. Prefers ready possession.',
            'Discussed luxury villa options. Customer wants swimming pool and garden area.',
            'Lead interested in residential plot. Asking about loan facilities and documentation.',
            'Customer comparing multiple properties. Needs time to decide. Follow-up scheduled.',
            'Discussed property pricing and payment plans. Customer satisfied with options.',
            'Lead looking for rental property. Discussed monthly rent and security deposit.',
            'Customer interested in under-construction project. Discussed possession timeline.',
            'Lead asking about property appreciation potential. Provided market analysis.'
        ];
        
        return $faker->randomElement($notes);
    }

    private function generateRealisticAgentNotes($faker)
    {
        $notes = [
            'Customer seems genuine. Good potential for conversion. Schedule site visit.',
            'Lead needs more information about amenities. Send property brochure via WhatsApp.',
            'Customer budget matches our premium properties. Arrange meeting with sales head.',
            'Lead comparing with competitors. Highlight our unique selling points in follow-up.',
            'Customer ready to visit site this weekend. Coordinate with site team.',
            'Lead interested but needs family approval. Follow up after 3 days.',
            'Customer asking about loan pre-approval. Connect with banking partner.',
            'Lead wants to see similar properties in different locations. Prepare options.',
            'Customer satisfied with property features. Discuss pricing and offers.',
            'Lead needs more time to arrange finances. Keep in touch weekly.'
        ];
        
        return $faker->randomElement($notes);
    }

    private function generateFollowUpTitle($faker)
    {
        $titles = [
            'Site visit for premium apartment',
            'Send property brochure and floor plans',
            'Discuss payment plan options',
            'Schedule meeting with sales manager',
            'Follow up on loan pre-approval status',
            'Share virtual tour link',
            'Discuss property documentation',
            'Arrange site visit for weekend',
            'Send competitor comparison sheet',
            'Follow up on family decision'
        ];
        
        return $faker->randomElement($titles);
    }

    private function generateFollowUpDescription($faker)
    {
        $descriptions = [
            'Customer requested site visit to see the actual property and amenities.',
            'Send detailed brochure with floor plans and pricing information.',
            'Discuss flexible payment options and EMI plans available.',
            'Arrange meeting to discuss special offers and discounts.',
            'Follow up on loan application status and documentation required.',
            'Share virtual tour to give better understanding of property layout.',
            'Explain property papers, approvals, and legal documentation.',
            'Coordinate weekend site visit with family members.',
            'Provide comparison with competitor properties in same area.',
            'Check if family has made decision and address any concerns.'
        ];
        
        return $faker->randomElement($descriptions);
    }
}
