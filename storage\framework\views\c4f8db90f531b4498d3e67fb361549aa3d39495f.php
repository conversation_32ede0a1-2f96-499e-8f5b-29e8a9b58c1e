<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Add Property')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('properties.index')); ?>"><?php echo e(__('Properties')); ?></a></li>
    <li class="breadcrumb-item" aria-current="page"><?php echo e(__('Add Property')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="<?php echo e(route('properties.index')); ?>" class="btn btn-sm btn-secondary">
            <i class="ti ti-arrow-left"></i> <?php echo e(__('Back to Properties')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5><?php echo e(__('Add New Property')); ?></h5>
                            <p class="text-muted mb-0"><?php echo e(__('Fill in the details below to add a new property to your inventory')); ?></p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php echo e(Form::open(['route' => 'properties.store', 'method' => 'POST', 'enctype' => 'multipart/form-data', 'class' => 'needs-validation', 'novalidate'])); ?>

                    
                    <!-- Basic Information Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">
                                <i class="ti ti-info-circle me-2"></i><?php echo e(__('Basic Information')); ?>

                            </h6>
                        </div>
                        
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('name', __('Property Name'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter property name'), 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please provide a property name.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('type', __('Property Type'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::select('type', [
                                    '' => __('Select property type'),
                                    'apartment' => __('Apartment'),
                                    'villa' => __('Villa'),
                                    'plot' => __('Plot'),
                                    'commercial' => __('Commercial'),
                                    'office' => __('Office'),
                                    'warehouse' => __('Warehouse'),
                                    'shop' => __('Shop'),
                                    'farmhouse' => __('Farmhouse')
                                ], null, ['class' => 'form-control', 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please select a property type.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('category', __('Category'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::select('category', [
                                    '' => __('Select category'),
                                    'residential' => __('Residential'),
                                    'commercial' => __('Commercial'),
                                    'industrial' => __('Industrial'),
                                    'agricultural' => __('Agricultural')
                                ], null, ['class' => 'form-control', 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please select a category.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('project_id', __('Project'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::select('project_id', $projects->pluck('name', 'id'), null, ['class' => 'form-control', 'placeholder' => __('Select project (optional)')])); ?>

                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('status', __('Status'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::select('status', [
                                    'available' => __('Available'),
                                    'sold' => __('Sold'),
                                    'reserved' => __('Reserved'),
                                    'under_construction' => __('Under Construction'),
                                    'ready_to_move' => __('Ready to Move')
                                ], 'available', ['class' => 'form-control', 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please select a status.')); ?></div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <?php echo e(Form::label('description', __('Description'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::textarea('description', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter property description')])); ?>

                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Location Information Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">
                                <i class="ti ti-map-pin me-2"></i><?php echo e(__('Location Information')); ?>

                            </h6>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <?php echo e(Form::label('address', __('Address'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::textarea('address', null, ['class' => 'form-control', 'rows' => 2, 'placeholder' => __('Enter complete address'), 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please provide an address.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('city', __('City'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::text('city', null, ['class' => 'form-control', 'placeholder' => __('Enter city'), 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please provide a city.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('state', __('State'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::text('state', null, ['class' => 'form-control', 'placeholder' => __('Enter state'), 'required' => true])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please provide a state.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('pincode', __('Pincode'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::text('pincode', null, ['class' => 'form-control', 'placeholder' => __('Enter pincode'), 'required' => true, 'pattern' => '[0-9]{6}', 'maxlength' => '6'])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please provide a valid 6-digit pincode.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('locality', __('Locality'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::text('locality', null, ['class' => 'form-control', 'placeholder' => __('Enter locality/area')])); ?>

                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('landmarks', __('Nearby Landmarks'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::text('landmarks', null, ['class' => 'form-control', 'placeholder' => __('Enter nearby landmarks')])); ?>

                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Unit Details Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">
                                <i class="ti ti-home me-2"></i><?php echo e(__('Unit Details')); ?>

                            </h6>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('area_sqft', __('Total Area (sq ft)'), ['class' => 'form-label required'])); ?>

                                <?php echo e(Form::number('area_sqft', null, ['class' => 'form-control', 'placeholder' => __('Total area'), 'required' => true, 'min' => '1', 'step' => '0.01'])); ?>

                                <div class="invalid-feedback"><?php echo e(__('Please provide the total area.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('carpet_area', __('Carpet Area (sq ft)'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('carpet_area', null, ['class' => 'form-control', 'placeholder' => __('Carpet area'), 'min' => '0', 'step' => '0.01'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('built_up_area', __('Built-up Area (sq ft)'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('built_up_area', null, ['class' => 'form-control', 'placeholder' => __('Built-up area'), 'min' => '0', 'step' => '0.01'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('super_area', __('Super Area (sq ft)'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('super_area', null, ['class' => 'form-control', 'placeholder' => __('Super area'), 'min' => '0', 'step' => '0.01'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <div class="form-group">
                                <?php echo e(Form::label('bedrooms', __('Bedrooms'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('bedrooms', null, ['class' => 'form-control', 'placeholder' => __('BHK'), 'min' => '0', 'max' => '20'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <div class="form-group">
                                <?php echo e(Form::label('bathrooms', __('Bathrooms'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('bathrooms', null, ['class' => 'form-control', 'placeholder' => __('Bathrooms'), 'min' => '0', 'max' => '20'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <div class="form-group">
                                <?php echo e(Form::label('balconies', __('Balconies'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('balconies', null, ['class' => 'form-control', 'placeholder' => __('Balconies'), 'min' => '0', 'max' => '10'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <div class="form-group">
                                <?php echo e(Form::label('parking_spaces', __('Parking'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('parking_spaces', null, ['class' => 'form-control', 'placeholder' => __('Parking'), 'min' => '0', 'max' => '10'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <div class="form-group">
                                <?php echo e(Form::label('floor_number', __('Floor'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('floor_number', null, ['class' => 'form-control', 'placeholder' => __('Floor'), 'min' => '0'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <div class="form-group">
                                <?php echo e(Form::label('total_floors', __('Total Floors'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::number('total_floors', null, ['class' => 'form-control', 'placeholder' => __('Total'), 'min' => '1', 'max' => '200'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('facing', __('Facing'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::select('facing', [
                                    '' => __('Select facing'),
                                    'north' => __('North'),
                                    'south' => __('South'),
                                    'east' => __('East'),
                                    'west' => __('West'),
                                    'north_east' => __('North East'),
                                    'north_west' => __('North West'),
                                    'south_east' => __('South East'),
                                    'south_west' => __('South West')
                                ], null, ['class' => 'form-control'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('furnishing_status', __('Furnishing'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::select('furnishing_status', [
                                    '' => __('Select furnishing'),
                                    'unfurnished' => __('Unfurnished'),
                                    'semi_furnished' => __('Semi-Furnished'),
                                    'fully_furnished' => __('Fully Furnished')
                                ], null, ['class' => 'form-control'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('age_of_property', __('Property Age'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::select('age_of_property', [
                                    '' => __('Select property age'),
                                    'under_construction' => __('Under Construction'),
                                    'new_launch' => __('New Launch'),
                                    '0-1' => __('0-1 Years'),
                                    '1-5' => __('1-5 Years'),
                                    '5-10' => __('5-10 Years'),
                                    '10-15' => __('10-15 Years'),
                                    '15+' => __('15+ Years')
                                ], null, ['class' => 'form-control'])); ?>

                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Pricing Information Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">
                                <i class="ti ti-currency-rupee me-2"></i><?php echo e(__('Pricing Information')); ?>

                            </h6>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('current_price', __('Current Price'), ['class' => 'form-label required'])); ?>

                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <?php echo e(Form::number('current_price', null, ['class' => 'form-control', 'placeholder' => __('Enter price'), 'required' => true, 'min' => '1', 'step' => '1'])); ?>

                                </div>
                                <div class="invalid-feedback"><?php echo e(__('Please provide the current price.')); ?></div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('price_per_sqft', __('Price per sq ft'), ['class' => 'form-label'])); ?>

                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <?php echo e(Form::number('price_per_sqft', null, ['class' => 'form-control', 'placeholder' => __('Per sq ft'), 'min' => '0', 'step' => '0.01'])); ?>

                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('booking_amount', __('Booking Amount'), ['class' => 'form-label'])); ?>

                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <?php echo e(Form::number('booking_amount', null, ['class' => 'form-control', 'placeholder' => __('Booking amount'), 'min' => '0', 'step' => '1'])); ?>

                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('maintenance_charge', __('Maintenance Charge'), ['class' => 'form-label'])); ?>

                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <?php echo e(Form::number('maintenance_charge', null, ['class' => 'form-control', 'placeholder' => __('Monthly maintenance'), 'min' => '0', 'step' => '0.01'])); ?>

                                    <span class="input-group-text">/month</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('security_deposit', __('Security Deposit'), ['class' => 'form-label'])); ?>

                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <?php echo e(Form::number('security_deposit', null, ['class' => 'form-control', 'placeholder' => __('Security deposit'), 'min' => '0', 'step' => '1'])); ?>

                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-check form-switch">
                                <?php echo e(Form::checkbox('price_negotiable', 1, null, ['class' => 'form-check-input', 'id' => 'price_negotiable'])); ?>

                                <?php echo e(Form::label('price_negotiable', __('Price is negotiable'), ['class' => 'form-check-label'])); ?>

                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Contact Information Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">
                                <i class="ti ti-phone me-2"></i><?php echo e(__('Contact Information')); ?>

                            </h6>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('contact_person', __('Contact Person'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::text('contact_person', null, ['class' => 'form-control', 'placeholder' => __('Contact person name')])); ?>

                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('contact_phone', __('Contact Phone'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::tel('contact_phone', null, ['class' => 'form-control', 'placeholder' => __('Contact phone number')])); ?>

                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('contact_email', __('Contact Email'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::email('contact_email', null, ['class' => 'form-control', 'placeholder' => __('Contact email address')])); ?>

                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Additional Settings Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">
                                <i class="ti ti-settings me-2"></i><?php echo e(__('Additional Settings')); ?>

                            </h6>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('assigned_agent_id', __('Assign to Agent'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::select('assigned_agent_id', $agents->pluck('name', 'id'), null, ['class' => 'form-control', 'placeholder' => __('Select agent (optional)')])); ?>

                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <?php echo e(Form::label('available_from', __('Available From'), ['class' => 'form-label'])); ?>

                                <?php echo e(Form::date('available_from', null, ['class' => 'form-control'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-check form-switch">
                                <?php echo e(Form::checkbox('is_featured', 1, null, ['class' => 'form-check-input', 'id' => 'is_featured'])); ?>

                                <?php echo e(Form::label('is_featured', __('Featured Property'), ['class' => 'form-check-label'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-check form-switch">
                                <?php echo e(Form::checkbox('is_premium', 1, null, ['class' => 'form-check-input', 'id' => 'is_premium'])); ?>

                                <?php echo e(Form::label('is_premium', __('Premium Property'), ['class' => 'form-check-label'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-check form-switch">
                                <?php echo e(Form::checkbox('is_active', 1, true, ['class' => 'form-check-input', 'id' => 'is_active'])); ?>

                                <?php echo e(Form::label('is_active', __('Active Property'), ['class' => 'form-check-label'])); ?>

                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6">
                            <div class="form-check form-switch">
                                <?php echo e(Form::checkbox('is_available', 1, true, ['class' => 'form-check-input', 'id' => 'is_available'])); ?>

                                <?php echo e(Form::label('is_available', __('Available for Sale'), ['class' => 'form-check-label'])); ?>

                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?php echo e(route('properties.index')); ?>" class="btn btn-secondary">
                                    <i class="ti ti-arrow-left me-1"></i><?php echo e(__('Cancel')); ?>

                                </a>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Save Property')); ?>

                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php echo e(Form::close()); ?>

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-calculate price per sqft
document.addEventListener('DOMContentLoaded', function() {
    const priceInput = document.querySelector('[name="current_price"]');
    const areaInput = document.querySelector('[name="area_sqft"]');
    const pricePerSqftInput = document.querySelector('[name="price_per_sqft"]');
    
    function calculatePricePerSqft() {
        const price = parseFloat(priceInput.value) || 0;
        const area = parseFloat(areaInput.value) || 0;
        
        if (price > 0 && area > 0) {
            const pricePerSqft = price / area;
            pricePerSqftInput.value = pricePerSqft.toFixed(2);
        }
    }
    
    if (priceInput && areaInput && pricePerSqftInput) {
        priceInput.addEventListener('input', calculatePricePerSqft);
        areaInput.addEventListener('input', calculatePricePerSqft);
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.card-header h5 {
    color: white;
    margin-bottom: 0;
}

.card-header p {
    color: rgba(255, 255, 255, 0.8);
}

.text-primary {
    color: #667eea !important;
}

hr {
    border-color: #e9ecef;
    opacity: 0.5;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/add.blade.php ENDPATH**/ ?>