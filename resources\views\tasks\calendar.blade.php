@extends('layouts.app')

@section('page-title')
    {{ __('Task Calendar') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('tasks.index') }}">{{ __('Tasks') }}</a></li>
    <li class="breadcrumb-item" aria-current="page">{{ __('Calendar') }}</li>
@endsection

@push('script-page')
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('task-calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        events: '{{ route("tasks.calendar.data") }}',
        eventClick: function(info) {
            info.jsEvent.preventDefault();
            if (info.event.url) {
                window.open(info.event.url, '_blank');
            }
        },
        eventDidMount: function(info) {
            // Add tooltip with task details
            info.el.setAttribute('title', 
                'Status: ' + info.event.extendedProps.status + '\n' +
                'Priority: ' + info.event.extendedProps.priority + '\n' +
                'Assigned to: ' + info.event.extendedProps.assigned_to + '\n' +
                'Description: ' + info.event.extendedProps.description
            );
        },
        height: 'auto',
        aspectRatio: 1.8
    });
    calendar.render();
});
</script>
@endpush

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center g-2">
                    <div class="col">
                        <h5>{{ __('Task Calendar') }}</h5>
                        <p class="text-muted mb-0">{{ __('View all tasks with due dates in calendar format') }}</p>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group" role="group">
                            <a href="{{ route('tasks.index') }}" class="btn btn-outline-primary">
                                <i class="ti ti-list"></i> {{ __('List View') }}
                            </a>
                            <a href="{{ route('tasks.dashboard') }}" class="btn btn-outline-primary">
                                <i class="ti ti-dashboard"></i> {{ __('Dashboard') }}
                            </a>
                            @can('create tasks')
                            <a href="#" class="btn btn-primary customModal" 
                               data-size="lg" 
                               data-url="{{ route('tasks.create') }}" 
                               data-title="{{ __('Create New Task') }}">
                                <i class="ti ti-plus"></i> {{ __('Create Task') }}
                            </a>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Calendar Legend -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap gap-3">
                            <div class="d-flex align-items-center">
                                <div class="badge bg-info me-2" style="width: 15px; height: 15px;"></div>
                                <small>{{ __('Not Started') }}</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="badge bg-warning me-2" style="width: 15px; height: 15px;"></div>
                                <small>{{ __('In Progress') }}</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="badge bg-success me-2" style="width: 15px; height: 15px;"></div>
                                <small>{{ __('Completed') }}</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="badge bg-danger me-2" style="width: 15px; height: 15px;"></div>
                                <small>{{ __('Cancelled / Urgent') }}</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="badge" style="background-color: #fd7e14; width: 15px; height: 15px;"></div>
                                <small class="ms-2">{{ __('High Priority') }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar Container -->
                <div id="task-calendar"></div>
            </div>
        </div>
    </div>
</div>
@endsection
