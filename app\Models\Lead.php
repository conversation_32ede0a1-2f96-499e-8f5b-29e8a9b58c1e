<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class Lead extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'alternate_phone',
        'address',
        'city',
        'state',
        'pincode',
        'status',
        'priority',
        'score',
        'score_grade',
        'scoring_factors',
        'score_updated_at',
        'stage',
        'sub_status',
        'conversion_probability',
        'days_in_current_stage',
        'project_id',
        'site_id',
        'channel_partner_id',
        'assigned_agent_id',
        'assigned_to',
        'created_by',
        'lead_owner_id',
        'assigned_at',
        'assignment_method',
        'reassignment_count',
        'last_reassigned_at',
        'budget_min',
        'budget_max',
        'requirements',
        'property_preferences',
        'preferred_location',
        'move_in_timeline',
        'financing_status',
        'down_payment_amount',
        'notes',
        'last_contacted_at',
        'next_followup_at',
        'source',
        'source_medium',
        'source_campaign',
        'source_content',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_content',
        'utm_term',
        'qualification_status',
        'qualified_at',
        'age_group',
        'occupation',
        'income_range',
        'family_size',
        'first_time_buyer',
        'communication_preferences',
        'preferred_contact_time',
        'timezone',
        'do_not_call',
        'do_not_email',
        'do_not_sms',
        'nurture_campaign_id',
        'nurture_stage',
        'nurture_started_at',
        'last_nurture_activity',
        'nurture_emails_sent',
        'nurture_emails_opened',
        'nurture_emails_clicked',
        'website_visits',
        'page_views',
        'time_on_site',
        'last_website_visit',
        'pages_visited',
        'email_opens',
        'email_clicks',
        'social_media_interactions',
        'temperature',
        'temperature_updated_at',
        'urgency_level',
        'urgency_reason',
        'competitor_properties_viewed',
        'price_sensitivity',
        'objections',
        'pain_points',
        'total_touchpoints',
        'meaningful_conversations',
        'appointments_scheduled',
        'appointments_completed',
        'proposals_sent',
        'estimated_deal_value',
        'actual_deal_value',
        'converted_at',
        'conversion_reason',
        'lost_at',
        'lost_reason',
        'external_id',
        'integration_data',
        'last_synced_at',
        'sync_status',
        'custom_fields',
    ];

    protected $casts = [
        'budget_min' => 'decimal:2',
        'budget_max' => 'decimal:2',
        'down_payment_amount' => 'decimal:2',
        'estimated_deal_value' => 'decimal:2',
        'actual_deal_value' => 'decimal:2',
        'score' => 'decimal:1',
        'conversion_probability' => 'decimal:2',
        'last_contacted_at' => 'datetime',
        'next_followup_at' => 'datetime',
        'score_updated_at' => 'datetime',
        'assigned_at' => 'datetime',
        'last_reassigned_at' => 'datetime',
        'qualified_at' => 'datetime',
        'nurture_started_at' => 'datetime',
        'last_nurture_activity' => 'datetime',
        'last_website_visit' => 'datetime',
        'temperature_updated_at' => 'datetime',
        'converted_at' => 'datetime',
        'lost_at' => 'datetime',
        'last_synced_at' => 'datetime',
        'custom_fields' => 'array',
        'scoring_factors' => 'array',
        'property_preferences' => 'array',
        'communication_preferences' => 'array',
        'pages_visited' => 'array',
        'competitor_properties_viewed' => 'array',
        'objections' => 'array',
        'pain_points' => 'array',
        'integration_data' => 'array',
        'first_time_buyer' => 'boolean',
        'do_not_call' => 'boolean',
        'do_not_email' => 'boolean',
        'do_not_sms' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-scope queries to current tenant (owner) using assigned_to relationship
        static::addGlobalScope('tenant', function ($builder) {
            if (Auth::check()) {
                $tenantId = Auth::user()->parent_id ?? Auth::user()->id;
                $builder->whereHas('assignedTo', function ($query) use ($tenantId) {
                    $query->where(function ($q) use ($tenantId) {
                        $q->where('id', $tenantId)
                          ->orWhere('parent_id', $tenantId);
                    });
                });
            }
        });

        // Auto-set assigned_to when creating if not set
        static::creating(function ($model) {
            if (Auth::check() && !$model->assigned_to) {
                $model->assigned_to = Auth::user()->id;
            }
        });

        // Sync with LeadAssignment when assigned_agent_id changes
        static::updated(function ($lead) {
            if ($lead->wasChanged('assigned_agent_id')) {
                // If assigned to an agent, create or update LeadAssignment
                if ($lead->assigned_agent_id) {
                    $existingAssignment = LeadAssignment::where('lead_id', $lead->id)
                        ->where('agent_id', $lead->assigned_agent_id)
                        ->where('assignment_status', 'pending')
                        ->first();

                    if (!$existingAssignment) {
                        LeadAssignment::create([
                            'lead_id' => $lead->id,
                            'agent_id' => $lead->assigned_agent_id,
                            'assigned_by' => Auth::id() ?? 1,
                            'assignment_type' => 'manual',
                            'assignment_status' => 'accepted',
                            'assigned_at' => $lead->assigned_at ?? now(),
                            'accepted_at' => now(),
                            'priority' => 1
                        ]);
                    }
                }
            }
        });
    }

    public static $statuses = [
        'new' => 'New',
        'contacted' => 'Contacted',
        'interested' => 'Interested',
        'not_interested' => 'Not Interested',
        'callback_scheduled' => 'Callback Scheduled',
        'site_visit_scheduled' => 'Site Visit Scheduled',
        'converted' => 'Converted',
        'lost' => 'Lost',
        'invalid' => 'Invalid',
    ];

    public static $priorities = [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'urgent' => 'Urgent',
    ];

    // Relationships
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    public function channelPartner()
    {
        return $this->belongsTo(ChannelPartner::class);
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function leadOwner()
    {
        return $this->belongsTo(User::class, 'lead_owner_id');
    }

    public function assignedAgent()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function callLogs()
    {
        return $this->hasMany(CallLog::class);
    }

    public function activities()
    {
        return $this->hasMany(LeadActivity::class);
    }

    public function tenant()
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    public function scores()
    {
        return $this->hasMany(LeadScore::class);
    }

    public function currentScore()
    {
        return $this->hasOne(LeadScore::class)->where('is_current', true);
    }

    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    // Scopes
    public function scopeForPartner($query, $partnerId)
    {
        return $query->where('channel_partner_id', $partnerId);
    }

    public function scopeForAgent($query, $agentId)
    {
        return $query->where('assigned_agent_id', $agentId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeDueForFollowup($query)
    {
        return $query->where('next_followup_at', '<=', Carbon::now())
                    ->whereNotIn('status', ['converted', 'lost', 'invalid']);
    }

    // Helper methods
    public function getTotalCallsAttribute()
    {
        return $this->callLogs()->count();
    }

    public function getLastCallAttribute()
    {
        return $this->callLogs()->latest('call_started_at')->first();
    }

    public function getTotalCallDurationAttribute()
    {
        return $this->callLogs()->sum('duration_seconds');
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'new' => 'blue',
            'contacted' => 'yellow',
            'interested' => 'green',
            'not_interested' => 'red',
            'callback_scheduled' => 'purple',
            'site_visit_scheduled' => 'indigo',
            'converted' => 'green',
            'lost' => 'red',
            'invalid' => 'gray',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red',
        ];

        return $colors[$this->priority] ?? 'gray';
    }

    public function getBudgetRangeAttribute()
    {
        if (!$this->budget_min && !$this->budget_max) {
            return 'Not specified';
        }

        if ($this->budget_min && $this->budget_max) {
            return '₹' . number_format($this->budget_min) . ' - ₹' . number_format($this->budget_max);
        }

        if ($this->budget_min) {
            return '₹' . number_format($this->budget_min) . '+';
        }

        return 'Up to ₹' . number_format($this->budget_max);
    }

    public function isDueForFollowup()
    {
        return $this->next_followup_at && $this->next_followup_at <= Carbon::now();
    }

    // Enhanced Accessors
    public function getScoreColorAttribute(): string
    {
        return match($this->score_grade) {
            'A' => 'success',
            'B' => 'info',
            'C' => 'warning',
            'D' => 'danger',
            'F' => 'dark',
            default => 'secondary'
        };
    }

    public function getTemperatureColorAttribute(): string
    {
        return match($this->temperature) {
            'hot' => 'danger',
            'warm' => 'warning',
            'cold' => 'info',
            default => 'secondary'
        };
    }

    public function getStageColorAttribute(): string
    {
        return match($this->stage) {
            'awareness' => 'secondary',
            'interest' => 'info',
            'consideration' => 'warning',
            'intent' => 'primary',
            'evaluation' => 'success',
            'purchase' => 'dark',
            default => 'secondary'
        };
    }

    public function getQualificationColorAttribute(): string
    {
        return match($this->qualification_status) {
            'sales_qualified' => 'success',
            'marketing_qualified' => 'info',
            'unqualified' => 'secondary',
            default => 'secondary'
        };
    }

    public function getEngagementScoreAttribute(): int
    {
        $score = 0;

        // Website engagement
        $score += min($this->website_visits * 2, 20);
        $score += min($this->page_views, 15);

        // Email engagement
        $score += min($this->email_opens * 3, 25);
        $score += min($this->email_clicks * 5, 20);

        // Social media
        $score += min($this->social_media_interactions * 2, 10);

        // Recent activity bonus
        if ($this->last_website_visit && $this->last_website_visit >= Carbon::now()->subDays(7)) {
            $score += 10;
        }

        return min($score, 100);
    }

    public function getResponseRateAttribute(): float
    {
        if ($this->total_touchpoints === 0) {
            return 0;
        }

        return round(($this->meaningful_conversations / $this->total_touchpoints) * 100, 1);
    }

    // Enhanced Helper Methods
    public function isHot(): bool
    {
        return $this->temperature === 'hot' || $this->score >= 8.0;
    }

    public function isQualified(): bool
    {
        return in_array($this->qualification_status, ['marketing_qualified', 'sales_qualified']);
    }

    public function isHighValue(): bool
    {
        return $this->estimated_deal_value >= 1000000 || $this->budget_max >= 1000000;
    }

    public function needsAttention(): bool
    {
        return $this->urgency_level === 'high' ||
               $this->urgency_level === 'critical' ||
               $this->isDueForFollowup() ||
               ($this->temperature === 'hot' && !$this->last_contacted_at) ||
               ($this->last_contacted_at && $this->last_contacted_at <= Carbon::now()->subDays(7));
    }

    public function canBeContacted(): bool
    {
        $now = Carbon::now($this->timezone ?? 'UTC');
        $hour = $now->hour;

        // Check do not contact preferences
        if ($this->do_not_call || $this->do_not_email || $this->do_not_sms) {
            return false;
        }

        // Check preferred contact time
        return match($this->preferred_contact_time) {
            'morning' => $hour >= 9 && $hour < 12,
            'afternoon' => $hour >= 12 && $hour < 17,
            'evening' => $hour >= 17 && $hour < 20,
            default => $hour >= 9 && $hour < 20 // Business hours
        };
    }

    public function updateScore(array $factors = []): void
    {
        $scoreData = $this->calculateScore($factors);

        // Update lead score fields
        $this->update([
            'score' => $scoreData['score'],
            'score_grade' => $scoreData['grade'],
            'scoring_factors' => $scoreData['factors'],
            'score_updated_at' => Carbon::now(),
            'conversion_probability' => $scoreData['conversion_probability'],
        ]);

        // Create/update score record
        $this->scores()->where('is_current', true)->update(['is_current' => false]);

        LeadScore::create([
            'lead_id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'score' => $scoreData['score'],
            'grade' => $scoreData['grade'],
            'scoring_factors' => $scoreData['factors'],
            'positive_factors' => $scoreData['positive_factors'],
            'negative_factors' => $scoreData['negative_factors'],
            'demographic_score' => $scoreData['demographic_score'],
            'behavioral_score' => $scoreData['behavioral_score'],
            'engagement_score' => $scoreData['engagement_score'],
            'intent_score' => $scoreData['intent_score'],
            'fit_score' => $scoreData['fit_score'],
            'timing_score' => $scoreData['timing_score'],
            'conversion_probability' => $scoreData['conversion_probability'],
            'calculated_at' => Carbon::now(),
            'is_current' => true,
        ]);
    }

    protected function calculateScore(array $factors = []): array
    {
        $scores = [
            'demographic_score' => $this->calculateDemographicScore(),
            'behavioral_score' => $this->calculateBehavioralScore(),
            'engagement_score' => $this->engagement_score,
            'intent_score' => $this->calculateIntentScore(),
            'fit_score' => $this->calculateFitScore(),
            'timing_score' => $this->calculateTimingScore(),
        ];

        // Weighted average
        $weights = [
            'demographic_score' => 0.15,
            'behavioral_score' => 0.20,
            'engagement_score' => 0.25,
            'intent_score' => 0.20,
            'fit_score' => 0.15,
            'timing_score' => 0.05,
        ];

        $weightedSum = 0;
        foreach ($weights as $component => $weight) {
            $weightedSum += ($scores[$component] / 100) * 10 * $weight;
        }

        $finalScore = round($weightedSum, 1);
        $grade = LeadScore::calculateGrade($finalScore);

        return [
            'score' => $finalScore,
            'grade' => $grade,
            'factors' => $scores,
            'positive_factors' => $this->getPositiveFactors($scores),
            'negative_factors' => $this->getNegativeFactors($scores),
            'conversion_probability' => LeadScore::calculateConversionProbability($finalScore, $factors),
            'demographic_score' => $scores['demographic_score'],
            'behavioral_score' => $scores['behavioral_score'],
            'engagement_score' => $scores['engagement_score'],
            'intent_score' => $scores['intent_score'],
            'fit_score' => $scores['fit_score'],
            'timing_score' => $scores['timing_score'],
        ];
    }

    protected function calculateDemographicScore(): int
    {
        $score = 50; // Base score

        // Age group scoring
        $score += match($this->age_group) {
            '26-35', '36-45' => 20,
            '18-25', '46-55' => 10,
            '55+' => 5,
            default => 0
        };

        // Income range scoring
        if ($this->income_range) {
            $score += 15;
        }

        // Location scoring (if in preferred areas)
        if ($this->city && in_array(strtolower($this->city), ['mumbai', 'delhi', 'bangalore', 'pune', 'hyderabad'])) {
            $score += 15;
        }

        return min($score, 100);
    }

    protected function calculateBehavioralScore(): int
    {
        $score = 0;

        // Website behavior
        $score += min($this->website_visits * 5, 30);
        $score += min($this->page_views * 2, 20);
        $score += min($this->time_on_site / 60, 15); // Convert seconds to minutes

        // Email behavior
        $score += min($this->email_opens * 3, 20);
        $score += min($this->email_clicks * 5, 15);

        return min($score, 100);
    }

    protected function calculateIntentScore(): int
    {
        $score = 0;

        // Explicit interest indicators
        if ($this->status === 'interested') $score += 30;
        if ($this->appointments_scheduled > 0) $score += 25;
        if ($this->meaningful_conversations > 0) $score += 20;

        // Urgency indicators
        $score += match($this->urgency_level) {
            'critical' => 25,
            'high' => 20,
            'medium' => 10,
            default => 0
        };

        return min($score, 100);
    }

    protected function calculateFitScore(): int
    {
        $score = 50; // Base score

        // Budget alignment
        if ($this->budget_min && $this->budget_max) {
            $score += 25;
        }

        // Property preferences defined
        if ($this->property_preferences) {
            $score += 15;
        }

        // Financing status
        $score += match($this->financing_status) {
            'pre_approved' => 10,
            'cash' => 15,
            'needs_financing' => 5,
            default => 0
        };

        return min($score, 100);
    }

    protected function calculateTimingScore(): int
    {
        $score = match($this->move_in_timeline) {
            'immediate' => 100,
            '1-3 months' => 80,
            '3-6 months' => 60,
            '6+ months' => 30,
            default => 50
        };

        return $score;
    }

    protected function getPositiveFactors(array $scores): array
    {
        $factors = [];

        foreach ($scores as $component => $score) {
            if ($score >= 70) {
                $factors[] = [
                    'factor' => str_replace('_', ' ', ucfirst($component)),
                    'impact' => $score,
                    'description' => $this->getFactorDescription($component, $score)
                ];
            }
        }

        return $factors;
    }

    protected function getNegativeFactors(array $scores): array
    {
        $factors = [];

        foreach ($scores as $component => $score) {
            if ($score <= 30) {
                $factors[] = [
                    'factor' => str_replace('_', ' ', ucfirst($component)),
                    'impact' => $score,
                    'description' => $this->getFactorDescription($component, $score)
                ];
            }
        }

        return $factors;
    }

    protected function getFactorDescription(string $component, int $score): string
    {
        return match($component) {
            'demographic_score' => $score >= 70 ? 'Strong demographic profile' : 'Limited demographic information',
            'behavioral_score' => $score >= 70 ? 'High website engagement' : 'Low digital engagement',
            'engagement_score' => $score >= 70 ? 'Very responsive to communications' : 'Limited response to outreach',
            'intent_score' => $score >= 70 ? 'Strong purchase intent signals' : 'Unclear purchase intent',
            'fit_score' => $score >= 70 ? 'Good fit for available properties' : 'Poor fit for current offerings',
            'timing_score' => $score >= 70 ? 'Ready to move soon' : 'Long-term timeline',
            default => 'Score: ' . $score
        };
    }

    public function advanceStage(string $newStage): void
    {
        $oldStage = $this->stage;

        $this->update([
            'stage' => $newStage,
            'days_in_current_stage' => 0,
        ]);

        // Log stage change activity
        $this->activities()->create([
            'type' => 'stage_change',
            'description' => "Stage changed from {$oldStage} to {$newStage}",
            'performed_by' => auth()->id(),
            'metadata' => [
                'old_stage' => $oldStage,
                'new_stage' => $newStage,
                'changed_at' => Carbon::now()->toISOString(),
            ],
        ]);
    }

    public function markAsConverted(?float $dealValue = null, ?string $reason = null): void
    {
        $this->update([
            'status' => 'converted',
            'stage' => 'purchase',
            'actual_deal_value' => $dealValue,
            'converted_at' => Carbon::now(),
            'conversion_reason' => $reason,
        ]);

        // Log conversion activity
        $this->activities()->create([
            'type' => 'conversion',
            'description' => 'Lead converted to customer',
            'performed_by' => auth()->id(),
            'metadata' => [
                'deal_value' => $dealValue,
                'reason' => $reason,
                'converted_at' => Carbon::now()->toISOString(),
            ],
        ]);
    }

    public function markAsLost(string $reason): void
    {
        $this->update([
            'status' => 'lost',
            'lost_at' => Carbon::now(),
            'lost_reason' => $reason,
        ]);

        // Log lost activity
        $this->activities()->create([
            'type' => 'lost',
            'description' => "Lead marked as lost: {$reason}",
            'performed_by' => auth()->id(),
            'metadata' => [
                'reason' => $reason,
                'lost_at' => Carbon::now()->toISOString(),
            ],
        ]);
    }





    public function getConversionTimelineAttribute(): string
    {
        if (!$this->conversion_probability || $this->conversion_probability < 20) {
            return 'Unlikely';
        }

        return match(true) {
            $this->conversion_probability >= 80 => 'Within 1 week',
            $this->conversion_probability >= 60 => 'Within 2 weeks',
            $this->conversion_probability >= 40 => 'Within 1 month',
            default => 'Within 3 months'
        };
    }
}
