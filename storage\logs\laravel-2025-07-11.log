[2025-07-11 01:13:36] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'first_contacted_at' in 'field list' (SQL: select AVG(TIMESTAMPDIFF(HOUR, created_at, first_contacted_at)) as avg_hours from `leads` where `first_contacted_at` is not null and `created_at` is not null and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1)) limit 1) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'first_contacted_at' in 'field list' (SQL: select AVG(TIMESTAMPDIFF(HOUR, created_at, first_contacted_at)) as avg_hours from `leads` where `first_contacted_at` is not null and `created_at` is not null and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1)) limit 1) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'first_contacted_at' in 'field list' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:44:00] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list' (SQL: select avg(`price`) as aggregate from `properties` where `status` = sold and `properties`.`deleted_at` is null and exists (select * from `users` where `properties`.`created_by` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list' (SQL: select avg(`price`) as aggregate from `properties` where `status` = sold and `properties`.`deleted_at` is null and exists (select * from `users` where `properties`.`created_by` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:49:29] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'deleted_at' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `deleted_at` is null) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'deleted_at' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `deleted_at` is null) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'deleted_at' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:50:41] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` = active) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` = active) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:51:11] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` != deleted) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` != deleted) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:52:19] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:56:42] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:16:27] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:19:17] INFO: Getting comprehensive dashboard data for user: 2 
[2025-07-11 02:19:18] ERROR: Dashboard error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) 
[2025-07-11 02:19:18] ERROR: Stack trace: #0 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(720): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(405): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2705): Illuminate\Database\Connection->select('select * from `...', Array, true)
#3 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2694): Illuminate\Database\Query\Builder->runSelect()
#4 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3230): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2693): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(710): Illuminate\Database\Query\Builder->get(Array)
#7 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(694): Illuminate\Database\Eloquent\Builder->getModels(Array)
#8 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(195): Illuminate\Database\Eloquent\Builder->get(Array)
#9 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(158): Illuminate\Database\Eloquent\Relations\Relation->get()
#10 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(758): Illuminate\Database\Eloquent\Relations\Relation->getEager()
#11 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(727): Illuminate\Database\Eloquent\Builder->eagerLoadRelation(Array, 'assignedLeads', Object(Closure))
#12 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(695): Illuminate\Database\Eloquent\Builder->eagerLoadRelations(Array)
#13 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(1596): Illuminate\Database\Eloquent\Builder->get()
#14 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(916): App\Http\Controllers\HomeController->getIndividualPerformanceMetrics(2)
#15 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(360): App\Http\Controllers\HomeController->getAdvancedAnalytics(2, Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon))
#16 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(52): App\Http\Controllers\HomeController->getComprehensiveDashboardData()
#17 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\HomeController->index()
#18 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#19 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\HomeController), 'index')
#20 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#21 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run()
#22 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\XSS.php(45): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\XSS->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\Verify2FA.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Verify2FA->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#35 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#44 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\SetLocaleMiddleware.php(40): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SetLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#65 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\calling_agent_crm\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\calling_agent_crm\index.php(21): require_once('D:\\xampp\\htdocs...')
#68 {main} 
[2025-07-11 02:19:18] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:25:57] INFO: Getting comprehensive dashboard data for user: 2 
[2025-07-11 02:25:58] ERROR: Dashboard error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) 
[2025-07-11 02:25:58] ERROR: Stack trace: #0 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(720): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(405): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2705): Illuminate\Database\Connection->select('select * from `...', Array, true)
#3 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2694): Illuminate\Database\Query\Builder->runSelect()
#4 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3230): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2693): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(710): Illuminate\Database\Query\Builder->get(Array)
#7 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(694): Illuminate\Database\Eloquent\Builder->getModels(Array)
#8 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(195): Illuminate\Database\Eloquent\Builder->get(Array)
#9 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(158): Illuminate\Database\Eloquent\Relations\Relation->get()
#10 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(758): Illuminate\Database\Eloquent\Relations\Relation->getEager()
#11 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(727): Illuminate\Database\Eloquent\Builder->eagerLoadRelation(Array, 'assignedLeads', Object(Closure))
#12 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(695): Illuminate\Database\Eloquent\Builder->eagerLoadRelations(Array)
#13 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(1608): Illuminate\Database\Eloquent\Builder->get()
#14 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(928): App\Http\Controllers\HomeController->getIndividualPerformanceMetrics(2)
#15 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(372): App\Http\Controllers\HomeController->getAdvancedAnalytics(2, Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon))
#16 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(52): App\Http\Controllers\HomeController->getComprehensiveDashboardData()
#17 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\HomeController->index()
#18 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#19 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\HomeController), 'index')
#20 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#21 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run()
#22 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\XSS.php(45): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\XSS->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\Verify2FA.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Verify2FA->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#35 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#44 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\SetLocaleMiddleware.php(40): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SetLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#65 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\calling_agent_crm\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\calling_agent_crm\index.php(21): require_once('D:\\xampp\\htdocs...')
#68 {main} 
[2025-07-11 02:25:58] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:32:52] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
