[2025-07-11 01:13:36] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'first_contacted_at' in 'field list' (SQL: select AVG(TIMESTAMPDIFF(HOUR, created_at, first_contacted_at)) as avg_hours from `leads` where `first_contacted_at` is not null and `created_at` is not null and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1)) limit 1) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'first_contacted_at' in 'field list' (SQL: select AVG(TIMESTAMPDIFF(HOUR, created_at, first_contacted_at)) as avg_hours from `leads` where `first_contacted_at` is not null and `created_at` is not null and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1)) limit 1) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'first_contacted_at' in 'field list' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:44:00] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list' (SQL: select avg(`price`) as aggregate from `properties` where `status` = sold and `properties`.`deleted_at` is null and exists (select * from `users` where `properties`.`created_by` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list' (SQL: select avg(`price`) as aggregate from `properties` where `status` = sold and `properties`.`deleted_at` is null and exists (select * from `users` where `properties`.`created_by` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:49:29] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'deleted_at' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `deleted_at` is null) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'deleted_at' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `deleted_at` is null) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'deleted_at' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:50:41] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` = active) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` = active) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:51:11] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` != deleted) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' (SQL: select count(*) as aggregate from `users` where `parent_id` = 2 and `type` = agent and `created_at` <= 2025-01-01 00:00:00 and `status` != deleted) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:52:19] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 01:56:42] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:16:27] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:19:17] INFO: Getting comprehensive dashboard data for user: 2 
[2025-07-11 02:19:18] ERROR: Dashboard error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) 
[2025-07-11 02:19:18] ERROR: Stack trace: #0 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(720): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(405): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2705): Illuminate\Database\Connection->select('select * from `...', Array, true)
#3 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2694): Illuminate\Database\Query\Builder->runSelect()
#4 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3230): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2693): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(710): Illuminate\Database\Query\Builder->get(Array)
#7 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(694): Illuminate\Database\Eloquent\Builder->getModels(Array)
#8 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(195): Illuminate\Database\Eloquent\Builder->get(Array)
#9 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(158): Illuminate\Database\Eloquent\Relations\Relation->get()
#10 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(758): Illuminate\Database\Eloquent\Relations\Relation->getEager()
#11 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(727): Illuminate\Database\Eloquent\Builder->eagerLoadRelation(Array, 'assignedLeads', Object(Closure))
#12 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(695): Illuminate\Database\Eloquent\Builder->eagerLoadRelations(Array)
#13 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(1596): Illuminate\Database\Eloquent\Builder->get()
#14 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(916): App\Http\Controllers\HomeController->getIndividualPerformanceMetrics(2)
#15 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(360): App\Http\Controllers\HomeController->getAdvancedAnalytics(2, Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon))
#16 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(52): App\Http\Controllers\HomeController->getComprehensiveDashboardData()
#17 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\HomeController->index()
#18 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#19 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\HomeController), 'index')
#20 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#21 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run()
#22 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\XSS.php(45): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\XSS->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\Verify2FA.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Verify2FA->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#35 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#44 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\SetLocaleMiddleware.php(40): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SetLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#65 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\calling_agent_crm\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\calling_agent_crm\index.php(21): require_once('D:\\xampp\\htdocs...')
#68 {main} 
[2025-07-11 02:19:18] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:25:57] INFO: Getting comprehensive dashboard data for user: 2 
[2025-07-11 02:25:58] ERROR: Dashboard error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) 
[2025-07-11 02:25:58] ERROR: Stack trace: #0 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(720): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Connection.php(405): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2705): Illuminate\Database\Connection->select('select * from `...', Array, true)
#3 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2694): Illuminate\Database\Query\Builder->runSelect()
#4 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3230): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2693): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(710): Illuminate\Database\Query\Builder->get(Array)
#7 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(694): Illuminate\Database\Eloquent\Builder->getModels(Array)
#8 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(195): Illuminate\Database\Eloquent\Builder->get(Array)
#9 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Relations\Relation.php(158): Illuminate\Database\Eloquent\Relations\Relation->get()
#10 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(758): Illuminate\Database\Eloquent\Relations\Relation->getEager()
#11 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(727): Illuminate\Database\Eloquent\Builder->eagerLoadRelation(Array, 'assignedLeads', Object(Closure))
#12 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(695): Illuminate\Database\Eloquent\Builder->eagerLoadRelations(Array)
#13 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(1608): Illuminate\Database\Eloquent\Builder->get()
#14 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(928): App\Http\Controllers\HomeController->getIndividualPerformanceMetrics(2)
#15 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(372): App\Http\Controllers\HomeController->getAdvancedAnalytics(2, Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon), Object(Carbon\Carbon))
#16 D:\xampp\htdocs\calling_agent_crm\app\Http\Controllers\HomeController.php(52): App\Http\Controllers\HomeController->getComprehensiveDashboardData()
#17 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\HomeController->index()
#18 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#19 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\HomeController), 'index')
#20 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#21 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run()
#22 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#23 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\XSS.php(45): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#24 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\XSS->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\Verify2FA.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\Verify2FA->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#35 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#44 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#45 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#46 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#47 D:\xampp\htdocs\calling_agent_crm\app\Http\Middleware\SetLocaleMiddleware.php(40): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\SetLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#54 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#65 D:\xampp\htdocs\calling_agent_crm\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#66 D:\xampp\htdocs\calling_agent_crm\public\index.php(54): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#67 D:\xampp\htdocs\calling_agent_crm\index.php(21): require_once('D:\\xampp\\htdocs...')
#68 {main} 
[2025-07-11 02:25:58] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 02:32:52] ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' (SQL: select * from `leads` where `leads`.`assigned_agent_id` in (4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `assigned_at` >= 2025-01-01 00:00:00 and exists (select * from `users` where `leads`.`assigned_to` = `users`.`id` and (`id` = 1 or `parent_id` = 1))) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leads.assigned_agent_id' in 'where clause' at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-11 03:10:09] ERROR: Cannot end a section without first starting one. {"view":{"view":"D:\\xampp\\htdocs\\calling_agent_crm\\resources\\views\\dashboard\\index.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1265650016 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1891</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1265650016\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","result":"<pre class=sf-dump id=sf-dump-605220779 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>totalUser</span>\" => <span class=sf-dump-num>13</span>
  \"<span class=sf-dump-key>totalClient</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>totalIncome</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>totalExpense</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:68</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>app_name</span>\" => \"\"
    \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"
    \"<span class=sf-dump-key>layout_font</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Roboto</span>\"
    \"<span class=sf-dump-key>accent_color</span>\" => \"<span class=sf-dump-str title=\"8 characters\">preset-6</span>\"
    \"<span class=sf-dump-key>sidebar_caption</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"
    \"<span class=sf-dump-key>theme_layout</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"
    \"<span class=sf-dump-key>layout_width</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"
    \"<span class=sf-dump-key>owner_email_verification</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>landing_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>register_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>company_logo</span>\" => \"<span class=sf-dump-str title=\"8 characters\">logo.png</span>\"
    \"<span class=sf-dump-key>company_favicon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">favicon.png</span>\"
    \"<span class=sf-dump-key>landing_logo</span>\" => \"<span class=sf-dump-str title=\"16 characters\">landing_logo.png</span>\"
    \"<span class=sf-dump-key>light_logo</span>\" => \"<span class=sf-dump-str title=\"14 characters\">light_logo.png</span>\"
    \"<span class=sf-dump-key>meta_seo_title</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_keyword</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_description</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_image</span>\" => \"\"
    \"<span class=sf-dump-key>company_date_format</span>\" => \"<span class=sf-dump-str title=\"6 characters\">M j, Y</span>\"
    \"<span class=sf-dump-key>company_time_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">g:i A</span>\"
    \"<span class=sf-dump-key>company_name</span>\" => \"\"
    \"<span class=sf-dump-key>company_phone</span>\" => \"\"
    \"<span class=sf-dump-key>company_address</span>\" => \"\"
    \"<span class=sf-dump-key>company_email</span>\" => \"\"
    \"<span class=sf-dump-key>company_email_from_name</span>\" => \"\"
    \"<span class=sf-dump-key>google_recaptcha</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>recaptcha_key</span>\" => \"\"
    \"<span class=sf-dump-key>recaptcha_secret</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_DRIVER</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_HOST</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_USERNAME</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_PASSWORD</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_ENCRYPTION</span>\" => \"\"
    \"<span class=sf-dump-key>FROM_EMAIL</span>\" => \"\"
    \"<span class=sf-dump-key>FROM_NAME</span>\" => \"\"
    \"<span class=sf-dump-key>estimation_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#EST-000</span>\"
    \"<span class=sf-dump-key>invoice_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#INV-000</span>\"
    \"<span class=sf-dump-key>client_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#CLI-000</span>\"
    \"<span class=sf-dump-key>CURRENCY</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"
    \"<span class=sf-dump-key>CURRENCY_SYMBOL</span>\" => \"<span class=sf-dump-str>$</span>\"
    \"<span class=sf-dump-key>STRIPE_PAYMENT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>STRIPE_KEY</span>\" => \"\"
    \"<span class=sf-dump-key>STRIPE_SECRET</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>paypal_mode</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_client_id</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"\"
    \"<span class=sf-dump-key>bank_transfer_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>bank_name</span>\" => \"\"
    \"<span class=sf-dump-key>bank_holder_name</span>\" => \"\"
    \"<span class=sf-dump-key>bank_account_number</span>\" => \"\"
    \"<span class=sf-dump-key>bank_ifsc_code</span>\" => \"\"
    \"<span class=sf-dump-key>bank_other_details</span>\" => \"\"
    \"<span class=sf-dump-key>flutterwave_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>flutterwave_public_key</span>\" => \"\"
    \"<span class=sf-dump-key>flutterwave_secret_key</span>\" => \"\"
    \"<span class=sf-dump-key>timezone</span>\" => \"\"
    \"<span class=sf-dump-key>footer_column_1</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Quick Links</span>\"
    \"<span class=sf-dump-key>footer_column_1_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_2</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Help</span>\"
    \"<span class=sf-dump-key>footer_column_2_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_3</span>\" => \"<span class=sf-dump-str title=\"8 characters\">OverView</span>\"
    \"<span class=sf-dump-key>footer_column_3_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_4</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Core System</span>\"
    \"<span class=sf-dump-key>footer_column_4_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>pricing_feature</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>copyright</span>\" => \"\"
  </samp>]
  \"<span class=sf-dump-key>incomeExpense</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>label</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Jan-2025</span>\"
      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">Feb-2025</span>\"
      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Mar-2025</span>\"
      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">Apr-2025</span>\"
      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">May-2025</span>\"
      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">Jun-2025</span>\"
      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">Jul-2025</span>\"
      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">Aug-2025</span>\"
      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">Sep-2025</span>\"
      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">Oct-2025</span>\"
      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"8 characters\">Nov-2025</span>\"
      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">Dec-2025</span>\"
    </samp>]
    \"<span class=sf-dump-key>income</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>4</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>5</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>6</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>7</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>8</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>9</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>10</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>11</span> => <span class=sf-dump-num>0</span>
    </samp>]
    \"<span class=sf-dump-key>expense</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>4</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>5</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>6</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>7</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>8</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>9</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>10</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>11</span> => <span class=sf-dump-num>0</span>
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>converted</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>hot_leads</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>contacted</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>interested</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>callback_scheduled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>site_visit_scheduled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>lost</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_this_week</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_this_month</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>conversion_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_response_time</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>calling</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>connected_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_call_duration</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>failed_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_this_week</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>peak_calling_hours</span>\" => []
  </samp>]
  \"<span class=sf-dump-key>projects</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>26</span>
    \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>completed</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>on_hold</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>planning</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>30.77</span>
    \"<span class=sf-dump-key>avg_project_duration</span>\" => <span class=sf-dump-num>563.8</span>
  </samp>]
  \"<span class=sf-dump-key>properties</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>sold</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>reserved</span>\" => <span class=sf-dump-num>5</span>
    \"<span class=sf-dump-key>under_construction</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>sales_rate</span>\" => <span class=sf-dump-num>20.0</span>
    \"<span class=sf-dump-key>avg_price</span>\" => \"<span class=sf-dump-str title=\"14 characters\">4250000.000000</span>\"
    \"<span class=sf-dump-key>inventory_turnover</span>\" => <span class=sf-dump-num>0.2</span>
  </samp>]
  \"<span class=sf-dump-key>documents</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>recent</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>shared</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>by_type</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>contract</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>agreement</span>\" => <span class=sf-dump-num>2</span>
      \"<span class=sf-dump-key>proposal</span>\" => <span class=sf-dump-num>3</span>
      \"<span class=sf-dump-key>report</span>\" => <span class=sf-dump-num>2</span>
      \"<span class=sf-dump-key>presentation</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>compliance</span>\" => <span class=sf-dump-num>2</span>
    </samp>]
    \"<span class=sf-dump-key>storage_usage</span>\" => <span class=sf-dump-num>57.48</span>
  </samp>]
  \"<span class=sf-dump-key>followups</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completed_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_response_time</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>agents</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>active_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>top_performer</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>performance_metrics</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
    </samp>]
    \"<span class=sf-dump-key>productivity_score</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>recent_activities</span>\" => []
  \"<span class=sf-dump-key>tasks</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>65</span>
    \"<span class=sf-dump-key>not_started</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>in_progress</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>completed</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>cancelled</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>due_today</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>due_this_week</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>high_priority</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>assigned_to_me</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>35.38</span>
    \"<span class=sf-dump-key>productivity_trends</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>completed_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completed_this_week</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>avg_completion_time</span>\" => <span class=sf-dump-num>52.2</span>
    \"<span class=sf-dump-key>most_productive_day</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Monday</span>\"
    \"<span class=sf-dump-key>task_categories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>lead_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>project_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>property_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>site_visit_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>followup_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>general_task</span>\" => <span class=sf-dump-num>15</span>
    </samp>]
    \"<span class=sf-dump-key>priority_distribution</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>low</span>\" => <span class=sf-dump-num>11</span>
      \"<span class=sf-dump-key>medium</span>\" => <span class=sf-dump-num>34</span>
      \"<span class=sf-dump-key>high</span>\" => <span class=sf-dump-num>16</span>
      \"<span class=sf-dump-key>urgent</span>\" => <span class=sf-dump-num>4</span>
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>charts</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>lead_funnel</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>contacted</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>qualified</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>proposal</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>converted</span>\" => <span class=sf-dump-num>0</span>
    </samp>]
    \"<span class=sf-dump-key>call_trends</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>agent_performance</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2349</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  \"<span class=sf-dump-key>analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>revenue_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>monthly_growth</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>profit_margin</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>revenue_per_client</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>revenue_forecast</span>\" => <span class=sf-dump-num>0.0</span>
    </samp>]
    \"<span class=sf-dump-key>lead_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>source_performance</span>\" => []
      \"<span class=sf-dump-key>conversion_funnel</span>\" => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      \"<span class=sf-dump-key>lead_scoring</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]
      \"<span class=sf-dump-key>seasonal_trends</span>\" => <span class=sf-dump-note>array:12</span> [ &#8230;12]
    </samp>]
    \"<span class=sf-dump-key>operational_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>resource_utilization</span>\" => <span class=sf-dump-num>0.0</span>
      \"<span class=sf-dump-key>efficiency_metrics</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      \"<span class=sf-dump-key>bottleneck_analysis</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>capacity_planning</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
    \"<span class=sf-dump-key>performance_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>team_performance</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      \"<span class=sf-dump-key>individual_performance</span>\" => <span class=sf-dump-note>array:11</span> [ &#8230;11]
      \"<span class=sf-dump-key>performance_trends</span>\" => <span class=sf-dump-note>array:6</span> [ &#8230;6]
      \"<span class=sf-dump-key>benchmark_comparison</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>insights</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Low Conversion Rate</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Your conversion rate of 0% needs improvement.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Review lead qualification process and follow-up strategies.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">high</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">calling</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Low Call Success Rate</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Call success rate of 0% is below optimal.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Provide additional training on call techniques and lead qualification.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">medium</span>\"
    </samp>]
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"4 characters\">team</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Team Productivity Opportunity</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Team productivity score is 0/100.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Consider team training sessions and performance improvement programs.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">medium</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>trends</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>leads_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>revenue_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>task_completion_growth</span>\" => <span class=sf-dump-num>100</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-605220779\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Cannot end a section without first starting one. at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php:94)
[previous exception] [object] (InvalidArgumentException(code: 0): Cannot end a section without first starting one. at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php:94)"}
[2025-07-11 03:11:02] ERROR: Cannot end a section without first starting one. {"view":{"view":"D:\\xampp\\htdocs\\calling_agent_crm\\resources\\views\\dashboard\\index.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-2049212278 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1891</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-2049212278\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","result":"<pre class=sf-dump id=sf-dump-1579995988 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>totalUser</span>\" => <span class=sf-dump-num>13</span>
  \"<span class=sf-dump-key>totalClient</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>totalIncome</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>totalExpense</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:68</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>app_name</span>\" => \"\"
    \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"
    \"<span class=sf-dump-key>layout_font</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Roboto</span>\"
    \"<span class=sf-dump-key>accent_color</span>\" => \"<span class=sf-dump-str title=\"8 characters\">preset-6</span>\"
    \"<span class=sf-dump-key>sidebar_caption</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"
    \"<span class=sf-dump-key>theme_layout</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"
    \"<span class=sf-dump-key>layout_width</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"
    \"<span class=sf-dump-key>owner_email_verification</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>landing_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>register_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>company_logo</span>\" => \"<span class=sf-dump-str title=\"8 characters\">logo.png</span>\"
    \"<span class=sf-dump-key>company_favicon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">favicon.png</span>\"
    \"<span class=sf-dump-key>landing_logo</span>\" => \"<span class=sf-dump-str title=\"16 characters\">landing_logo.png</span>\"
    \"<span class=sf-dump-key>light_logo</span>\" => \"<span class=sf-dump-str title=\"14 characters\">light_logo.png</span>\"
    \"<span class=sf-dump-key>meta_seo_title</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_keyword</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_description</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_image</span>\" => \"\"
    \"<span class=sf-dump-key>company_date_format</span>\" => \"<span class=sf-dump-str title=\"6 characters\">M j, Y</span>\"
    \"<span class=sf-dump-key>company_time_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">g:i A</span>\"
    \"<span class=sf-dump-key>company_name</span>\" => \"\"
    \"<span class=sf-dump-key>company_phone</span>\" => \"\"
    \"<span class=sf-dump-key>company_address</span>\" => \"\"
    \"<span class=sf-dump-key>company_email</span>\" => \"\"
    \"<span class=sf-dump-key>company_email_from_name</span>\" => \"\"
    \"<span class=sf-dump-key>google_recaptcha</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>recaptcha_key</span>\" => \"\"
    \"<span class=sf-dump-key>recaptcha_secret</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_DRIVER</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_HOST</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_USERNAME</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_PASSWORD</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_ENCRYPTION</span>\" => \"\"
    \"<span class=sf-dump-key>FROM_EMAIL</span>\" => \"\"
    \"<span class=sf-dump-key>FROM_NAME</span>\" => \"\"
    \"<span class=sf-dump-key>estimation_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#EST-000</span>\"
    \"<span class=sf-dump-key>invoice_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#INV-000</span>\"
    \"<span class=sf-dump-key>client_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#CLI-000</span>\"
    \"<span class=sf-dump-key>CURRENCY</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"
    \"<span class=sf-dump-key>CURRENCY_SYMBOL</span>\" => \"<span class=sf-dump-str>$</span>\"
    \"<span class=sf-dump-key>STRIPE_PAYMENT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>STRIPE_KEY</span>\" => \"\"
    \"<span class=sf-dump-key>STRIPE_SECRET</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>paypal_mode</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_client_id</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"\"
    \"<span class=sf-dump-key>bank_transfer_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>bank_name</span>\" => \"\"
    \"<span class=sf-dump-key>bank_holder_name</span>\" => \"\"
    \"<span class=sf-dump-key>bank_account_number</span>\" => \"\"
    \"<span class=sf-dump-key>bank_ifsc_code</span>\" => \"\"
    \"<span class=sf-dump-key>bank_other_details</span>\" => \"\"
    \"<span class=sf-dump-key>flutterwave_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>flutterwave_public_key</span>\" => \"\"
    \"<span class=sf-dump-key>flutterwave_secret_key</span>\" => \"\"
    \"<span class=sf-dump-key>timezone</span>\" => \"\"
    \"<span class=sf-dump-key>footer_column_1</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Quick Links</span>\"
    \"<span class=sf-dump-key>footer_column_1_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_2</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Help</span>\"
    \"<span class=sf-dump-key>footer_column_2_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_3</span>\" => \"<span class=sf-dump-str title=\"8 characters\">OverView</span>\"
    \"<span class=sf-dump-key>footer_column_3_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_4</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Core System</span>\"
    \"<span class=sf-dump-key>footer_column_4_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>pricing_feature</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>copyright</span>\" => \"\"
  </samp>]
  \"<span class=sf-dump-key>incomeExpense</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>label</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Jan-2025</span>\"
      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">Feb-2025</span>\"
      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Mar-2025</span>\"
      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">Apr-2025</span>\"
      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">May-2025</span>\"
      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">Jun-2025</span>\"
      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">Jul-2025</span>\"
      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">Aug-2025</span>\"
      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">Sep-2025</span>\"
      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">Oct-2025</span>\"
      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"8 characters\">Nov-2025</span>\"
      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">Dec-2025</span>\"
    </samp>]
    \"<span class=sf-dump-key>income</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>4</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>5</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>6</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>7</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>8</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>9</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>10</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>11</span> => <span class=sf-dump-num>0</span>
    </samp>]
    \"<span class=sf-dump-key>expense</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>4</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>5</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>6</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>7</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>8</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>9</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>10</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>11</span> => <span class=sf-dump-num>0</span>
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>converted</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>hot_leads</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>contacted</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>interested</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>callback_scheduled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>site_visit_scheduled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>lost</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_this_week</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_this_month</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>conversion_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_response_time</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>calling</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>connected_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_call_duration</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>failed_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_this_week</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>peak_calling_hours</span>\" => []
  </samp>]
  \"<span class=sf-dump-key>projects</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>26</span>
    \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>completed</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>on_hold</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>planning</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>30.77</span>
    \"<span class=sf-dump-key>avg_project_duration</span>\" => <span class=sf-dump-num>563.8</span>
  </samp>]
  \"<span class=sf-dump-key>properties</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>sold</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>reserved</span>\" => <span class=sf-dump-num>5</span>
    \"<span class=sf-dump-key>under_construction</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>sales_rate</span>\" => <span class=sf-dump-num>20.0</span>
    \"<span class=sf-dump-key>avg_price</span>\" => \"<span class=sf-dump-str title=\"14 characters\">4250000.000000</span>\"
    \"<span class=sf-dump-key>inventory_turnover</span>\" => <span class=sf-dump-num>0.2</span>
  </samp>]
  \"<span class=sf-dump-key>documents</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>recent</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>shared</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>by_type</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>contract</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>agreement</span>\" => <span class=sf-dump-num>2</span>
      \"<span class=sf-dump-key>proposal</span>\" => <span class=sf-dump-num>3</span>
      \"<span class=sf-dump-key>report</span>\" => <span class=sf-dump-num>2</span>
      \"<span class=sf-dump-key>presentation</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>compliance</span>\" => <span class=sf-dump-num>2</span>
    </samp>]
    \"<span class=sf-dump-key>storage_usage</span>\" => <span class=sf-dump-num>57.48</span>
  </samp>]
  \"<span class=sf-dump-key>followups</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completed_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_response_time</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>agents</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>active_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>top_performer</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>performance_metrics</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
    </samp>]
    \"<span class=sf-dump-key>productivity_score</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>recent_activities</span>\" => []
  \"<span class=sf-dump-key>tasks</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>65</span>
    \"<span class=sf-dump-key>not_started</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>in_progress</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>completed</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>cancelled</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>due_today</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>due_this_week</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>high_priority</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>assigned_to_me</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>35.38</span>
    \"<span class=sf-dump-key>productivity_trends</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>completed_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completed_this_week</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>avg_completion_time</span>\" => <span class=sf-dump-num>52.2</span>
    \"<span class=sf-dump-key>most_productive_day</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Monday</span>\"
    \"<span class=sf-dump-key>task_categories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>lead_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>project_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>property_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>site_visit_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>followup_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>general_task</span>\" => <span class=sf-dump-num>15</span>
    </samp>]
    \"<span class=sf-dump-key>priority_distribution</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>low</span>\" => <span class=sf-dump-num>11</span>
      \"<span class=sf-dump-key>medium</span>\" => <span class=sf-dump-num>34</span>
      \"<span class=sf-dump-key>high</span>\" => <span class=sf-dump-num>16</span>
      \"<span class=sf-dump-key>urgent</span>\" => <span class=sf-dump-num>4</span>
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>charts</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>lead_funnel</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>contacted</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>qualified</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>proposal</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>converted</span>\" => <span class=sf-dump-num>0</span>
    </samp>]
    \"<span class=sf-dump-key>call_trends</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>agent_performance</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2349</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  \"<span class=sf-dump-key>analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>revenue_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>monthly_growth</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>profit_margin</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>revenue_per_client</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>revenue_forecast</span>\" => <span class=sf-dump-num>0.0</span>
    </samp>]
    \"<span class=sf-dump-key>lead_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>source_performance</span>\" => []
      \"<span class=sf-dump-key>conversion_funnel</span>\" => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      \"<span class=sf-dump-key>lead_scoring</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]
      \"<span class=sf-dump-key>seasonal_trends</span>\" => <span class=sf-dump-note>array:12</span> [ &#8230;12]
    </samp>]
    \"<span class=sf-dump-key>operational_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>resource_utilization</span>\" => <span class=sf-dump-num>0.0</span>
      \"<span class=sf-dump-key>efficiency_metrics</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      \"<span class=sf-dump-key>bottleneck_analysis</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>capacity_planning</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
    \"<span class=sf-dump-key>performance_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>team_performance</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      \"<span class=sf-dump-key>individual_performance</span>\" => <span class=sf-dump-note>array:11</span> [ &#8230;11]
      \"<span class=sf-dump-key>performance_trends</span>\" => <span class=sf-dump-note>array:6</span> [ &#8230;6]
      \"<span class=sf-dump-key>benchmark_comparison</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>insights</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Low Conversion Rate</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Your conversion rate of 0% needs improvement.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Review lead qualification process and follow-up strategies.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">high</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">calling</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Low Call Success Rate</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Call success rate of 0% is below optimal.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Provide additional training on call techniques and lead qualification.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">medium</span>\"
    </samp>]
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"4 characters\">team</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Team Productivity Opportunity</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Team productivity score is 0/100.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Consider team training sessions and performance improvement programs.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">medium</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>trends</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>leads_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>revenue_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>task_completion_growth</span>\" => <span class=sf-dump-num>100</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1579995988\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Cannot end a section without first starting one. at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php:94)
[previous exception] [object] (InvalidArgumentException(code: 0): Cannot end a section without first starting one. at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php:94)"}
[2025-07-11 03:12:27] ERROR: Cannot end a section without first starting one. {"view":{"view":"D:\\xampp\\htdocs\\calling_agent_crm\\resources\\views\\dashboard\\index.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1358035102 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1891</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1358035102\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","result":"<pre class=sf-dump id=sf-dump-238187353 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>totalUser</span>\" => <span class=sf-dump-num>13</span>
  \"<span class=sf-dump-key>totalClient</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>totalIncome</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>totalExpense</span>\" => <span class=sf-dump-num>0</span>
  \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:68</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>app_name</span>\" => \"\"
    \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"
    \"<span class=sf-dump-key>layout_font</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Roboto</span>\"
    \"<span class=sf-dump-key>accent_color</span>\" => \"<span class=sf-dump-str title=\"8 characters\">preset-6</span>\"
    \"<span class=sf-dump-key>sidebar_caption</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"
    \"<span class=sf-dump-key>theme_layout</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"
    \"<span class=sf-dump-key>layout_width</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"
    \"<span class=sf-dump-key>owner_email_verification</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>landing_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>register_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>company_logo</span>\" => \"<span class=sf-dump-str title=\"8 characters\">logo.png</span>\"
    \"<span class=sf-dump-key>company_favicon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">favicon.png</span>\"
    \"<span class=sf-dump-key>landing_logo</span>\" => \"<span class=sf-dump-str title=\"16 characters\">landing_logo.png</span>\"
    \"<span class=sf-dump-key>light_logo</span>\" => \"<span class=sf-dump-str title=\"14 characters\">light_logo.png</span>\"
    \"<span class=sf-dump-key>meta_seo_title</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_keyword</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_description</span>\" => \"\"
    \"<span class=sf-dump-key>meta_seo_image</span>\" => \"\"
    \"<span class=sf-dump-key>company_date_format</span>\" => \"<span class=sf-dump-str title=\"6 characters\">M j, Y</span>\"
    \"<span class=sf-dump-key>company_time_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">g:i A</span>\"
    \"<span class=sf-dump-key>company_name</span>\" => \"\"
    \"<span class=sf-dump-key>company_phone</span>\" => \"\"
    \"<span class=sf-dump-key>company_address</span>\" => \"\"
    \"<span class=sf-dump-key>company_email</span>\" => \"\"
    \"<span class=sf-dump-key>company_email_from_name</span>\" => \"\"
    \"<span class=sf-dump-key>google_recaptcha</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>recaptcha_key</span>\" => \"\"
    \"<span class=sf-dump-key>recaptcha_secret</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_DRIVER</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_HOST</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_USERNAME</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_PASSWORD</span>\" => \"\"
    \"<span class=sf-dump-key>SERVER_ENCRYPTION</span>\" => \"\"
    \"<span class=sf-dump-key>FROM_EMAIL</span>\" => \"\"
    \"<span class=sf-dump-key>FROM_NAME</span>\" => \"\"
    \"<span class=sf-dump-key>estimation_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#EST-000</span>\"
    \"<span class=sf-dump-key>invoice_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#INV-000</span>\"
    \"<span class=sf-dump-key>client_number_prefix</span>\" => \"<span class=sf-dump-str title=\"8 characters\">#CLI-000</span>\"
    \"<span class=sf-dump-key>CURRENCY</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"
    \"<span class=sf-dump-key>CURRENCY_SYMBOL</span>\" => \"<span class=sf-dump-str>$</span>\"
    \"<span class=sf-dump-key>STRIPE_PAYMENT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>STRIPE_KEY</span>\" => \"\"
    \"<span class=sf-dump-key>STRIPE_SECRET</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>paypal_mode</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_client_id</span>\" => \"\"
    \"<span class=sf-dump-key>paypal_secret_key</span>\" => \"\"
    \"<span class=sf-dump-key>bank_transfer_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>bank_name</span>\" => \"\"
    \"<span class=sf-dump-key>bank_holder_name</span>\" => \"\"
    \"<span class=sf-dump-key>bank_account_number</span>\" => \"\"
    \"<span class=sf-dump-key>bank_ifsc_code</span>\" => \"\"
    \"<span class=sf-dump-key>bank_other_details</span>\" => \"\"
    \"<span class=sf-dump-key>flutterwave_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"
    \"<span class=sf-dump-key>flutterwave_public_key</span>\" => \"\"
    \"<span class=sf-dump-key>flutterwave_secret_key</span>\" => \"\"
    \"<span class=sf-dump-key>timezone</span>\" => \"\"
    \"<span class=sf-dump-key>footer_column_1</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Quick Links</span>\"
    \"<span class=sf-dump-key>footer_column_1_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_2</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Help</span>\"
    \"<span class=sf-dump-key>footer_column_2_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_3</span>\" => \"<span class=sf-dump-str title=\"8 characters\">OverView</span>\"
    \"<span class=sf-dump-key>footer_column_3_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>footer_column_4</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Core System</span>\"
    \"<span class=sf-dump-key>footer_column_4_enabled</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"
    \"<span class=sf-dump-key>pricing_feature</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"
    \"<span class=sf-dump-key>copyright</span>\" => \"\"
  </samp>]
  \"<span class=sf-dump-key>incomeExpense</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>label</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Jan-2025</span>\"
      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">Feb-2025</span>\"
      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Mar-2025</span>\"
      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">Apr-2025</span>\"
      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">May-2025</span>\"
      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">Jun-2025</span>\"
      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">Jul-2025</span>\"
      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">Aug-2025</span>\"
      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">Sep-2025</span>\"
      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">Oct-2025</span>\"
      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"8 characters\">Nov-2025</span>\"
      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"8 characters\">Dec-2025</span>\"
    </samp>]
    \"<span class=sf-dump-key>income</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>4</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>5</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>6</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>7</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>8</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>9</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>10</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>11</span> => <span class=sf-dump-num>0</span>
    </samp>]
    \"<span class=sf-dump-key>expense</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>4</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>5</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>6</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>7</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>8</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>9</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>10</span> => <span class=sf-dump-num>0</span>
      <span class=sf-dump-index>11</span> => <span class=sf-dump-num>0</span>
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>converted</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>hot_leads</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>contacted</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>interested</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>callback_scheduled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>site_visit_scheduled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>lost</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_this_week</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>new_this_month</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>conversion_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_response_time</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>calling</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>connected_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_call_duration</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>failed_calls</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_this_week</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>peak_calling_hours</span>\" => []
  </samp>]
  \"<span class=sf-dump-key>projects</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>26</span>
    \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>completed</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>on_hold</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>planning</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>30.77</span>
    \"<span class=sf-dump-key>avg_project_duration</span>\" => <span class=sf-dump-num>563.8</span>
  </samp>]
  \"<span class=sf-dump-key>properties</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>available</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>sold</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>reserved</span>\" => <span class=sf-dump-num>5</span>
    \"<span class=sf-dump-key>under_construction</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>sales_rate</span>\" => <span class=sf-dump-num>20.0</span>
    \"<span class=sf-dump-key>avg_price</span>\" => \"<span class=sf-dump-str title=\"14 characters\">4250000.000000</span>\"
    \"<span class=sf-dump-key>inventory_turnover</span>\" => <span class=sf-dump-num>0.2</span>
  </samp>]
  \"<span class=sf-dump-key>documents</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>recent</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>shared</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>by_type</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>contract</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>agreement</span>\" => <span class=sf-dump-num>2</span>
      \"<span class=sf-dump-key>proposal</span>\" => <span class=sf-dump-num>3</span>
      \"<span class=sf-dump-key>report</span>\" => <span class=sf-dump-num>2</span>
      \"<span class=sf-dump-key>presentation</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>compliance</span>\" => <span class=sf-dump-num>2</span>
    </samp>]
    \"<span class=sf-dump-key>storage_usage</span>\" => <span class=sf-dump-num>57.48</span>
  </samp>]
  \"<span class=sf-dump-key>followups</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>pending</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completed_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>avg_response_time</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>agents</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>active_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>top_performer</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>performance_metrics</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:8</span> [ &#8230;8]
    </samp>]
    \"<span class=sf-dump-key>productivity_score</span>\" => <span class=sf-dump-num>0.0</span>
  </samp>]
  \"<span class=sf-dump-key>recent_activities</span>\" => []
  \"<span class=sf-dump-key>tasks</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>65</span>
    \"<span class=sf-dump-key>not_started</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>in_progress</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>completed</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>cancelled</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>overdue</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>due_today</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>due_this_week</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>high_priority</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>assigned_to_me</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completion_rate</span>\" => <span class=sf-dump-num>35.38</span>
    \"<span class=sf-dump-key>productivity_trends</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>completed_today</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>completed_this_week</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>avg_completion_time</span>\" => <span class=sf-dump-num>52.2</span>
    \"<span class=sf-dump-key>most_productive_day</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Monday</span>\"
    \"<span class=sf-dump-key>task_categories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>lead_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>project_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>property_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>site_visit_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>followup_task</span>\" => <span class=sf-dump-num>10</span>
      \"<span class=sf-dump-key>general_task</span>\" => <span class=sf-dump-num>15</span>
    </samp>]
    \"<span class=sf-dump-key>priority_distribution</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>low</span>\" => <span class=sf-dump-num>11</span>
      \"<span class=sf-dump-key>medium</span>\" => <span class=sf-dump-num>34</span>
      \"<span class=sf-dump-key>high</span>\" => <span class=sf-dump-num>16</span>
      \"<span class=sf-dump-key>urgent</span>\" => <span class=sf-dump-num>4</span>
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>charts</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>lead_funnel</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>contacted</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>qualified</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>proposal</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>converted</span>\" => <span class=sf-dump-num>0</span>
    </samp>]
    \"<span class=sf-dump-key>call_trends</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>agent_performance</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2349</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  \"<span class=sf-dump-key>analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>revenue_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>monthly_growth</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>profit_margin</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>revenue_per_client</span>\" => <span class=sf-dump-num>0</span>
      \"<span class=sf-dump-key>revenue_forecast</span>\" => <span class=sf-dump-num>0.0</span>
    </samp>]
    \"<span class=sf-dump-key>lead_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>source_performance</span>\" => []
      \"<span class=sf-dump-key>conversion_funnel</span>\" => <span class=sf-dump-note>array:8</span> [ &#8230;8]
      \"<span class=sf-dump-key>lead_scoring</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]
      \"<span class=sf-dump-key>seasonal_trends</span>\" => <span class=sf-dump-note>array:12</span> [ &#8230;12]
    </samp>]
    \"<span class=sf-dump-key>operational_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>resource_utilization</span>\" => <span class=sf-dump-num>0.0</span>
      \"<span class=sf-dump-key>efficiency_metrics</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      \"<span class=sf-dump-key>bottleneck_analysis</span>\" => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      \"<span class=sf-dump-key>capacity_planning</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
    \"<span class=sf-dump-key>performance_analytics</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>team_performance</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      \"<span class=sf-dump-key>individual_performance</span>\" => <span class=sf-dump-note>array:11</span> [ &#8230;11]
      \"<span class=sf-dump-key>performance_trends</span>\" => <span class=sf-dump-note>array:6</span> [ &#8230;6]
      \"<span class=sf-dump-key>benchmark_comparison</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>insights</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Low Conversion Rate</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Your conversion rate of 0% needs improvement.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Review lead qualification process and follow-up strategies.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">high</span>\"
    </samp>]
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">warning</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">calling</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Low Call Success Rate</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Call success rate of 0% is below optimal.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Provide additional training on call techniques and lead qualification.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">medium</span>\"
    </samp>]
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"
      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"4 characters\">team</span>\"
      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Team Productivity Opportunity</span>\"
      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Team productivity score is 0/100.</span>\"
      \"<span class=sf-dump-key>recommendation</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Consider team training sessions and performance improvement programs.</span>\"
      \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">medium</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>trends</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>leads_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>revenue_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>calls_growth</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>task_completion_growth</span>\" => <span class=sf-dump-num>100</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-238187353\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Cannot end a section without first starting one. at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php:94)
[previous exception] [object] (InvalidArgumentException(code: 0): Cannot end a section without first starting one. at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php:94)"}
