<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Document Details')); ?> - <?php echo e($document->name); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('documents.index')); ?>"><?php echo e(__('Documents')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e($document->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-button'); ?>
    <div class="float-end">
        <?php if($document->file_path): ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('download documents')): ?>
                <a href="<?php echo e(route('documents.download', $document)); ?>" class="btn btn-sm btn-primary">
                    <i class="ti ti-download"></i> <?php echo e(__('Download')); ?>

                </a>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit documents')): ?>
            <a href="#" class="btn btn-sm btn-info" onclick="commonModal('<?php echo e(route('documents.edit', $document)); ?>', '<?php echo e(__('Edit Document')); ?>')" data-size="xl" data-ajax-popup="true" data-title="<?php echo e(__('Edit Document')); ?>">
                <i class="ti ti-pencil"></i> <?php echo e(__('Edit')); ?>

            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Document Information -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Document Information')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Document Number:')); ?></strong>
                                <span><?php echo e($document->document_number); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Type:')); ?></strong>
                                <span class="badge bg-primary"><?php echo e(\App\Models\Document::$types[$document->type] ?? $document->type); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Category:')); ?></strong>
                                <span class="badge bg-secondary"><?php echo e(\App\Models\Document::$categories[$document->category] ?? $document->category); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Status:')); ?></strong>
                                <span class="badge bg-<?php echo e($document->status == 'published' ? 'success' : ($document->status == 'draft' ? 'warning' : 'secondary')); ?>">
                                    <?php echo e(\App\Models\Document::$statuses[$document->status] ?? $document->status); ?>

                                </span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Priority:')); ?></strong>
                                <span class="badge bg-<?php echo e($document->priority == 'high' ? 'danger' : ($document->priority == 'medium' ? 'warning' : 'info')); ?>">
                                    <?php echo e(\App\Models\Document::$priorities[$document->priority] ?? $document->priority); ?>

                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Visibility:')); ?></strong>
                                <span><?php echo e(\App\Models\Document::$visibilities[$document->visibility] ?? $document->visibility); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Version:')); ?></strong>
                                <span><?php echo e($document->version); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Created By:')); ?></strong>
                                <span><?php echo e($document->creator->name ?? 'Unknown'); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Created Date:')); ?></strong>
                                <span><?php echo e($document->created_at->format('M d, Y H:i')); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong><?php echo e(__('Last Updated:')); ?></strong>
                                <span><?php echo e($document->updated_at->format('M d, Y H:i')); ?></span>
                            </div>
                        </div>
                    </div>

                    <?php if($document->description): ?>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <strong><?php echo e(__('Description:')); ?></strong>
                                <p class="mt-2"><?php echo e($document->description); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Special Flags -->
                    <?php if($document->is_confidential || $document->is_compliance_document): ?>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <strong><?php echo e(__('Special Flags:')); ?></strong>
                                <div class="mt-2">
                                    <?php if($document->is_confidential): ?>
                                        <span class="badge bg-danger me-2"><?php echo e(__('Confidential')); ?></span>
                                    <?php endif; ?>
                                    <?php if($document->is_compliance_document): ?>
                                        <span class="badge bg-info me-2"><?php echo e(__('Compliance Document')); ?></span>
                                        <?php if($document->compliance_standard): ?>
                                            <small class="text-muted">(<?php echo e($document->compliance_standard); ?>)</small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Relationships -->
                    <?php if($document->project || $document->lead || $document->client): ?>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <strong><?php echo e(__('Related To:')); ?></strong>
                                <div class="mt-2">
                                    <?php if($document->project): ?>
                                        <div class="mb-1">
                                            <strong><?php echo e(__('Project:')); ?></strong>
                                            <a href="<?php echo e(route('projects.show', $document->project)); ?>" class="text-decoration-none">
                                                <?php echo e($document->project->name); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($document->lead): ?>
                                        <div class="mb-1">
                                            <strong><?php echo e(__('Lead:')); ?></strong>
                                            <a href="<?php echo e(route('leads.show', $document->lead)); ?>" class="text-decoration-none">
                                                <?php echo e($document->lead->name); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($document->client): ?>
                                        <div class="mb-1">
                                            <strong><?php echo e(__('Client:')); ?></strong>
                                            <a href="<?php echo e(route('clients.show', $document->client)); ?>" class="text-decoration-none">
                                                <?php echo e($document->client->name); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Tags -->
                    <?php if($document->tags && is_array($document->tags) && count($document->tags) > 0): ?>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <strong><?php echo e(__('Tags:')); ?></strong>
                                <div class="mt-2">
                                    <?php $__currentLoopData = $document->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-light text-dark me-1"><?php echo e($tag); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Important Dates -->
                    <?php if($document->review_date || $document->expiry_date): ?>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <strong><?php echo e(__('Important Dates:')); ?></strong>
                                <div class="mt-2">
                                    <?php if($document->review_date): ?>
                                        <div class="mb-1">
                                            <strong><?php echo e(__('Review Date:')); ?></strong>
                                            <span class="badge bg-<?php echo e($document->review_date->isPast() ? 'danger' : 'warning'); ?>">
                                                <?php echo e($document->review_date->format('M d, Y')); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($document->expiry_date): ?>
                                        <div class="mb-1">
                                            <strong><?php echo e(__('Expiry Date:')); ?></strong>
                                            <span class="badge bg-<?php echo e($document->expiry_date->isPast() ? 'danger' : 'warning'); ?>">
                                                <?php echo e($document->expiry_date->format('M d, Y')); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- File Information -->
            <?php if($document->file_path): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><?php echo e(__('File Information')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <strong><?php echo e(__('File Name:')); ?></strong>
                                    <span><?php echo e($document->file_name); ?></span>
                                </div>
                                <div class="info-item mb-3">
                                    <strong><?php echo e(__('File Size:')); ?></strong>
                                    <span><?php echo e($document->file_size_human); ?></span>
                                </div>
                                <div class="info-item mb-3">
                                    <strong><?php echo e(__('File Type:')); ?></strong>
                                    <span class="badge bg-info"><?php echo e(strtoupper($document->file_type)); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <strong><?php echo e(__('Downloads:')); ?></strong>
                                    <span><?php echo e($document->download_count); ?></span>
                                </div>
                                <div class="info-item mb-3">
                                    <strong><?php echo e(__('Views:')); ?></strong>
                                    <span><?php echo e($document->view_count); ?></span>
                                </div>
                                <?php if($document->last_accessed_at): ?>
                                    <div class="info-item mb-3">
                                        <strong><?php echo e(__('Last Accessed:')); ?></strong>
                                        <span><?php echo e($document->last_accessed_at->format('M d, Y H:i')); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-xl-4">
            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Statistics')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="mb-1"><?php echo e($stats['total_signatures']); ?></h4>
                                <p class="text-muted mb-0"><?php echo e(__('Total Signatures')); ?></p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="mb-1"><?php echo e($stats['completed_signatures']); ?></h4>
                                <p class="text-muted mb-0"><?php echo e(__('Completed')); ?></p>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="text-center">
                                <h4 class="mb-1"><?php echo e($stats['total_access_grants']); ?></h4>
                                <p class="text-muted mb-0"><?php echo e(__('Access Grants')); ?></p>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="text-center">
                                <h4 class="mb-1"><?php echo e($stats['recent_activities']); ?></h4>
                                <p class="text-muted mb-0"><?php echo e(__('Activities')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Signatures -->
            <?php if($document->requires_signature && $document->signatures->count() > 0): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><?php echo e(__('Signatures')); ?></h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $document->signatures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $signature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo e($signature->signer_name); ?></h6>
                                    <small class="text-muted"><?php echo e($signature->signer_email); ?></small>
                                </div>
                                <div>
                                    <span class="badge bg-<?php echo e($signature->status == 'signed' ? 'success' : ($signature->status == 'pending' ? 'warning' : 'danger')); ?>">
                                        <?php echo e(\App\Models\DocumentSignature::$statuses[$signature->status] ?? $signature->status); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Access Grants -->
            <?php if($document->accessGrants->count() > 0): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><?php echo e(__('Access Grants')); ?></h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $document->accessGrants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $access): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo e($access->user->name); ?></h6>
                                    <small class="text-muted"><?php echo e(\App\Models\DocumentAccess::$accessLevels[$access->access_level] ?? $access->access_level); ?></small>
                                </div>
                                <div>
                                    <?php if($access->expires_at): ?>
                                        <small class="text-muted"><?php echo e(__('Expires')); ?> <?php echo e($access->expires_at->format('M d')); ?></small>
                                    <?php else: ?>
                                        <small class="text-success"><?php echo e(__('Permanent')); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Activities -->
            <?php if($document->auditLogs->count() > 0): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><?php echo e(__('Recent Activities')); ?></h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $document->auditLogs->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="timeline-item mb-3">
                                <div class="timeline-content">
                                    <h6 class="mb-1"><?php echo e($activity->action_label); ?></h6>
                                    <p class="text-muted mb-1"><?php echo e($activity->description); ?></p>
                                    <small class="text-muted">
                                        <?php echo e($activity->user->name ?? __('System')); ?> • 
                                        <?php echo e($activity->created_at->diffForHumans()); ?>

                                    </small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    .document-info-card {
        border-left: 4px solid var(--bs-primary);
    }
    .document-stats .stat-item {
        text-align: center;
        padding: 1rem;
        border-radius: 8px;
        background: var(--bs-light);
    }
    .document-stats .stat-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--bs-primary);
    }
    .activity-timeline {
        position: relative;
        padding-left: 2rem;
    }
    .activity-timeline::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: var(--bs-border-color);
    }
    .activity-item {
        position: relative;
        margin-bottom: 1rem;
    }
    .activity-item::before {
        content: '';
        position: absolute;
        left: -1.75rem;
        top: 0.5rem;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--bs-primary);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-refresh activity timeline every 30 seconds
    setInterval(function() {
        // You can add AJAX call here to refresh activity timeline
        console.log('Activity timeline refresh interval');
    }, 30000);

    // Handle document download tracking
    $('.download-btn').on('click', function() {
        var documentId = $(this).data('document-id');
        // Track download event
        console.log('Document downloaded:', documentId);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/documents/show.blade.php ENDPATH**/ ?>