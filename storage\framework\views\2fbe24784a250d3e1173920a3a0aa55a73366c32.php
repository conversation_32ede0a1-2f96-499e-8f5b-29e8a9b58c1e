<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Property Analytics')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-breadcrumb'); ?>
    <?php echo e(__('Property Analytics')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-action'); ?>
    <div class="btn-group" role="group" aria-label="Analytics Actions">
        <a href="<?php echo e(route('properties.index')); ?>" class="btn btn-outline-secondary">
            <i data-feather="arrow-left"></i> <?php echo e(__('Back to Properties')); ?>

        </a>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export property data')): ?>
            <a href="<?php echo e(route('properties.analytics.export')); ?>" class="btn btn-outline-success">
                <i data-feather="download"></i> <?php echo e(__('Export Data')); ?>

            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar avtar-s bg-primary">
                                <i data-feather="home"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0"><?php echo e(__('Total Properties')); ?></h6>
                            <h4 class="mb-0"><?php echo e(number_format($totalProperties)); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar avtar-s bg-success">
                                <i data-feather="check-circle"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0"><?php echo e(__('Available')); ?></h6>
                            <h4 class="mb-0"><?php echo e(number_format($availableProperties)); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar avtar-s bg-danger">
                                <i data-feather="shopping-cart"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0"><?php echo e(__('Sold')); ?></h6>
                            <h4 class="mb-0"><?php echo e(number_format($soldProperties)); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar avtar-s bg-warning">
                                <i data-feather="clock"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0"><?php echo e(__('Reserved')); ?></h6>
                            <h4 class="mb-0"><?php echo e(number_format($reservedProperties)); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__('Property Type Distribution')); ?></h5>
                </div>
                <div class="card-body">
                    <canvas id="propertyTypeChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__('Monthly Sales')); ?> (<?php echo e(date('Y')); ?>)</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlySalesChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__('Financial Overview')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="avtar avtar-s bg-info">
                                        <i data-feather="dollar-sign"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0"><?php echo e(__('Average Price')); ?></h6>
                                    <h5 class="mb-0"><?php echo e(currency_format_with_sym($averagePrice)); ?></h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="avtar avtar-s bg-success">
                                        <i data-feather="trending-up"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0"><?php echo e(__('Total Value')); ?></h6>
                                    <h5 class="mb-0"><?php echo e(currency_format_with_sym($totalValue)); ?></h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo e(__('Quick Analytics')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view property performance')): ?>
                            <a href="<?php echo e(route('properties.analytics.performance')); ?>" class="btn btn-outline-primary">
                                <i data-feather="bar-chart-2"></i> <?php echo e(__('Performance Analytics')); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view occupancy reports')): ?>
                            <a href="<?php echo e(route('properties.analytics.occupancy')); ?>" class="btn btn-outline-info">
                                <i data-feather="pie-chart"></i> <?php echo e(__('Occupancy Reports')); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view pricing analytics')): ?>
                            <a href="<?php echo e(route('properties.analytics.pricing')); ?>" class="btn btn-outline-success">
                                <i data-feather="dollar-sign"></i> <?php echo e(__('Pricing Analytics')); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view lead analytics')): ?>
                            <a href="<?php echo e(route('properties.analytics.leads')); ?>" class="btn btn-outline-warning">
                                <i data-feather="users"></i> <?php echo e(__('Lead Analytics')); ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Property Type Distribution Chart
    const propertyTypeCtx = document.getElementById('propertyTypeChart').getContext('2d');
    const propertyTypeData = <?php echo json_encode($propertyTypes, 15, 512) ?>;
    
    new Chart(propertyTypeCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(propertyTypeData).map(type => type.charAt(0).toUpperCase() + type.slice(1)),
            datasets: [{
                data: Object.values(propertyTypeData),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Sales Chart
    const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
    const monthlySalesData = <?php echo json_encode($monthlySales, 15, 512) ?>;
    
    // Create array for all 12 months
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const salesData = months.map((month, index) => monthlySalesData[index + 1] || 0);
    
    new Chart(monthlySalesCtx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: '<?php echo e(__("Properties Sold")); ?>',
                data: salesData,
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/analytics/index.blade.php ENDPATH**/ ?>