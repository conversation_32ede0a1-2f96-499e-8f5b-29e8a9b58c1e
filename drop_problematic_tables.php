<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'calling_agent_crm',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    echo "🔍 Dropping problematic tables...\n";
    
    $problematicTables = [
        'lead_import_histories',
        'lead_scores'
    ];
    
    foreach ($problematicTables as $tableName) {
        $tables = $capsule->getConnection()->select("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($tables)) {
            echo "⚠️ Found {$tableName} table - dropping it...\n";
            $capsule->getConnection()->statement("DROP TABLE IF EXISTS {$tableName}");
            echo "✅ Dropped {$tableName} table\n";
        } else {
            echo "ℹ️ {$tableName} table does not exist\n";
        }
    }
    
    echo "✅ All problematic tables handled\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
