<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Edit Form')); ?> - <?php echo e($form->title); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('forms.index')); ?>"><?php echo e(__('Forms')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Edit Form')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="<?php echo e(route('forms.show', $form->id)); ?>" class="btn btn-sm btn-outline-primary">
            <i class="ti ti-eye"></i> <?php echo e(__('View Form')); ?>

        </a>
        <?php if($form->status === 'published'): ?>
            <a href="<?php echo e($form->public_url); ?>" target="_blank" class="btn btn-sm btn-success">
                <i class="ti ti-external-link"></i> <?php echo e(__('View Public')); ?>

            </a>
        <?php endif; ?>
        <a href="<?php echo e(route('forms.index')); ?>" class="btn btn-sm btn-secondary">
            <i class="ti ti-arrow-left"></i> <?php echo e(__('Back')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="mb-0"><?php echo e(__('Form Builder')); ?></h5>
                        </div>
                        <div class="col-6 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" id="preview-btn">
                                    <i class="ti ti-eye"></i> <?php echo e(__('Preview')); ?>

                                </button>
                                <button type="button" class="btn btn-sm btn-primary" id="save-form-btn">
                                    <i class="ti ti-device-floppy"></i> <?php echo e(__('Save')); ?>

                                </button>
                                <?php if($form->status === 'draft'): ?>
                                    <button type="button" class="btn btn-sm btn-success" id="publish-form-btn">
                                        <i class="ti ti-world"></i> <?php echo e(__('Publish')); ?>

                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="row g-0">
                        <!-- Form Builder Sidebar -->
                        <div class="col-md-3 border-end">
                            <div class="p-3">
                                <!-- Form Settings -->
                                <div class="mb-4">
                                    <h6 class="mb-3"><?php echo e(__('Form Settings')); ?></h6>
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Form Title')); ?></label>
                                        <input type="text" class="form-control" id="form-title" value="<?php echo e($form->title); ?>">
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Description')); ?></label>
                                        <textarea class="form-control" id="form-description" rows="3"><?php echo e($form->description); ?></textarea>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Status')); ?></label>
                                        <select class="form-control" id="form-status">
                                            <option value="draft" <?php echo e($form->status === 'draft' ? 'selected' : ''); ?>><?php echo e(__('Draft')); ?></option>
                                            <option value="published" <?php echo e($form->status === 'published' ? 'selected' : ''); ?>><?php echo e(__('Published')); ?></option>
                                            <option value="archived" <?php echo e($form->status === 'archived' ? 'selected' : ''); ?>><?php echo e(__('Archived')); ?></option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Field Types -->
                                <div class="mb-4">
                                    <h6 class="mb-3"><?php echo e(__('Field Types')); ?></h6>
                                    <div class="field-types">
                                        <div class="field-type-item" data-type="text">
                                            <i class="ti ti-forms"></i> <?php echo e(__('Text Input')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="email">
                                            <i class="ti ti-mail"></i> <?php echo e(__('Email')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="phone">
                                            <i class="ti ti-phone"></i> <?php echo e(__('Phone')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="textarea">
                                            <i class="ti ti-file-text"></i> <?php echo e(__('Textarea')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="select">
                                            <i class="ti ti-list"></i> <?php echo e(__('Dropdown')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="radio">
                                            <i class="ti ti-circle-dot"></i> <?php echo e(__('Radio Button')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="checkbox">
                                            <i class="ti ti-checkbox"></i> <?php echo e(__('Checkbox')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="date">
                                            <i class="ti ti-calendar"></i> <?php echo e(__('Date')); ?>

                                        </div>
                                        <div class="field-type-item" data-type="file">
                                            <i class="ti ti-upload"></i> <?php echo e(__('File Upload')); ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Builder Canvas -->
                        <div class="col-md-6">
                            <div class="p-3">
                                <div class="form-builder-canvas" id="form-canvas">
                                    <div class="form-header mb-4">
                                        <h4 id="canvas-form-title"><?php echo e($form->title); ?></h4>
                                        <p id="canvas-form-description" class="text-muted"><?php echo e($form->description); ?></p>
                                    </div>
                                    
                                    <div class="form-fields" id="form-fields">
                                        <?php if($form->fields->count() > 0): ?>
                                            <?php $__currentLoopData = $form->fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="form-field-item" data-field-id="<?php echo e($field->id); ?>" data-field-type="<?php echo e($field->field_type); ?>">
                                                    <div class="field-controls">
                                                        <button type="button" class="btn btn-sm btn-outline-primary edit-field">
                                                            <i class="ti ti-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-field">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </div>
                                                    <div class="field-preview">
                                                        <?php echo $field->generateHtml(); ?>

                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <div class="empty-form-message text-center py-5">
                                                <i class="ti ti-forms text-muted" style="font-size: 3rem;"></i>
                                                <h5 class="text-muted mt-3"><?php echo e(__('No fields added yet')); ?></h5>
                                                <p class="text-muted"><?php echo e(__('Drag field types from the sidebar to start building your form')); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Field Properties Panel -->
                        <div class="col-md-3 border-start">
                            <div class="p-3">
                                <div id="field-properties" style="display: none;">
                                    <h6 class="mb-3"><?php echo e(__('Field Properties')); ?></h6>
                                    
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Label')); ?></label>
                                        <input type="text" class="form-control" id="field-label">
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Field Name')); ?></label>
                                        <input type="text" class="form-control" id="field-name">
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Placeholder')); ?></label>
                                        <input type="text" class="form-control" id="field-placeholder">
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <label class="form-label"><?php echo e(__('Help Text')); ?></label>
                                        <textarea class="form-control" id="field-help-text" rows="2"></textarea>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="field-required">
                                        <label class="form-check-label" for="field-required">
                                            <?php echo e(__('Required Field')); ?>

                                        </label>
                                    </div>
                                    
                                    <div class="form-group mb-3" id="field-options-group" style="display: none;">
                                        <label class="form-label"><?php echo e(__('Options')); ?></label>
                                        <div id="field-options-list">
                                            <!-- Options will be added dynamically -->
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="add-option-btn">
                                            <i class="ti ti-plus"></i> <?php echo e(__('Add Option')); ?>

                                        </button>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" id="update-field-btn">
                                            <?php echo e(__('Update Field')); ?>

                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="cancel-edit-btn">
                                            <?php echo e(__('Cancel')); ?>

                                        </button>
                                    </div>
                                </div>
                                
                                <div id="no-field-selected" class="text-center py-5">
                                    <i class="ti ti-click text-muted" style="font-size: 2rem;"></i>
                                    <h6 class="text-muted mt-3"><?php echo e(__('No field selected')); ?></h6>
                                    <p class="text-muted small"><?php echo e(__('Click on a field to edit its properties')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo e(__('Form Preview')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="form-preview-content">
                        <!-- Preview content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<!-- jQuery UI CSS -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
<style>
.field-types {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.field-type-item {
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    background: #fff;
}

.field-type-item:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.form-builder-canvas {
    min-height: 600px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.form-fields {
    min-height: 400px;
    padding: 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: #ffffff;
    transition: all 0.3s ease;
    position: relative;
}

.form-fields.ui-droppable-hover {
    border-color: #28a745;
    background: #f8fff8;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.2);
}

.form-fields:empty {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.form-fields:empty::before {
    content: "Drop fields here to build your form";
    color: #6c757d;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
}

.form-fields:empty::after {
    content: "Drag field types from the sidebar";
    color: #adb5bd;
    font-size: 14px;
}

.form-field-item {
    position: relative;
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
}

.form-field-item:hover {
    border-color: #007bff;
}

.form-field-item.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.field-controls {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
}

.form-field-item:hover .field-controls {
    display: block;
}

.empty-form-message {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    transition: all 0.3s ease;
    background: #fafafa;
}

.empty-form-message.ui-droppable-hover {
    border-color: #28a745;
    background: #f8fff8;
    transform: scale(1.02);
}

.empty-form-message.ui-droppable-active {
    border-color: #007bff;
    background: #f0f8ff;
}

/* Drag and Drop Styles */
.field-type-item {
    cursor: move;
    transition: all 0.3s ease;
}

.field-type-item:hover {
    background: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.ui-draggable-dragging {
    z-index: 1000;
    transform: rotate(5deg);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.ui-droppable-hover {
    background: #e8f5e8;
    border: 2px dashed #28a745;
}

.ui-droppable-active {
    border-color: #007bff;
    background: #f0f8ff;
}

.form-field-item {
    cursor: move;
    position: relative;
}

.form-field-item:hover {
    background: #f8f9fa;
}

.ui-sortable-helper {
    background: white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    border: 1px solid #dee2e6;
}

.field-placeholder {
    height: 60px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    margin: 10px 0;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
<!-- jQuery UI JS -->
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script>
$(document).ready(function() {
    let currentForm = <?php echo json_encode($form, 15, 512) ?>;
    let selectedField = null;

    // Check if jQuery UI is loaded
    function checkJQueryUI() {
        if (typeof $.ui === 'undefined') {
            console.error('❌ jQuery UI is not loaded!');
            showNotification('Error', 'jQuery UI is required for drag & drop functionality', 'error');
            return false;
        }
        console.log('✅ jQuery UI is loaded, version:', $.ui.version);
        return true;
    }

    // Notification function
    function showNotification(title, message, type) {
        if (typeof notifier !== 'undefined') {
            const img = type === 'success' ?
                (typeof successImg !== 'undefined' ? successImg : '') :
                (typeof errorImg !== 'undefined' ? errorImg : '');
            notifier.show(title, message, type, img, 4000);
        } else {
            // Fallback to alert if notifier not available
            alert(title + ': ' + message);
        }
        console.log(`🔔 NOTIFICATION: ${type.toUpperCase()} - ${title}: ${message}`);
    }

    // Initialize form builder
    console.log('🚀 Initializing Form Builder...');

    // Check jQuery UI before initializing
    if (checkJQueryUI()) {
        initializeFormBuilder();
    } else {
        console.error('❌ Cannot initialize form builder without jQuery UI');
    }

    function initializeFormBuilder() {
        console.log('⚙️ Setting up drag & drop functionality...');

        // Step 1: Initialize draggable items first
        console.log('📦 Initializing draggable field types...');
        $('.field-type-item').draggable({
            helper: 'clone',
            revert: 'invalid',
            cursor: 'move',
            opacity: 0.8,
            zIndex: 1000,
            appendTo: 'body',
            containment: 'window',
            scroll: false,
            start: function(event, ui) {
                $(this).addClass('dragging');
                console.log('🚀 Started dragging field type:', $(this).data('type'));
                // Make helper more visible
                ui.helper.css({
                    'background': '#007bff',
                    'color': 'white',
                    'border': '2px solid #0056b3',
                    'box-shadow': '0 4px 8px rgba(0,0,0,0.3)',
                    'transform': 'rotate(3deg)'
                });
            },
            drag: function(event, ui) {
                // Optional: Add visual feedback during drag
            },
            stop: function(event, ui) {
                $(this).removeClass('dragging');
                console.log('🛑 Stopped dragging field type');
            }
        });

        // Step 2: Initialize sortable for existing fields
        console.log('🔄 Initializing sortable for form fields...');
        $('#form-fields').sortable({
            placeholder: 'field-placeholder',
            handle: '.form-field-item',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            update: function(event, ui) {
                updateFieldOrder();
            },
            start: function(event, ui) {
                ui.placeholder.height(ui.item.height());
            }
        });

        // Step 3: Initialize droppable areas last
        console.log('🎯 Initializing droppable areas...');
        makeDroppable();

        console.log('✅ Form builder initialization complete!');
    }
    
    // Make droppable areas
    function makeDroppable() {
        console.log('🔧 Setting up droppable areas...');

        // Safely destroy existing droppable instances
        try {
            $('#form-fields').each(function() {
                if ($(this).hasClass('ui-droppable')) {
                    $(this).droppable('destroy');
                    console.log('🗑️ Destroyed existing form-fields droppable');
                }
            });

            $('.empty-form-message').each(function() {
                if ($(this).hasClass('ui-droppable')) {
                    $(this).droppable('destroy');
                    console.log('🗑️ Destroyed existing empty-message droppable');
                }
            });
        } catch (e) {
            console.log('⚠️ No existing droppable to destroy:', e.message);
        }

        // Make form fields container droppable ONLY if it has fields or no empty message
        if ($('#form-fields .form-field-item').length > 0 || $('.empty-form-message').length === 0) {
            $('#form-fields').droppable({
                accept: '.field-type-item',
                tolerance: 'intersect',
                hoverClass: 'ui-droppable-hover',
                activeClass: 'ui-droppable-active',
                drop: function(event, ui) {
                    let fieldType = ui.draggable.data('type');
                    console.log('🎯 Dropping field type:', fieldType, 'into form-fields container');
                    addNewField(fieldType);
                },
                over: function(event, ui) {
                    console.log('🎯 Dragging over form-fields container');
                },
                out: function(event, ui) {
                    console.log('🎯 Dragging out of form-fields container');
                }
            });
            console.log('✅ Form-fields container made droppable');
        }

        // Make empty message area droppable ONLY if it exists AND form-fields is empty
        if ($('.empty-form-message').length && $('#form-fields .form-field-item').length === 0) {
            $('.empty-form-message').droppable({
                accept: '.field-type-item',
                tolerance: 'intersect',
                hoverClass: 'ui-droppable-hover',
                activeClass: 'ui-droppable-active',
                drop: function(event, ui) {
                    let fieldType = ui.draggable.data('type');
                    console.log('🎯 Dropping field type on empty area:', fieldType);
                    addNewField(fieldType);
                },
                over: function(event, ui) {
                    console.log('🎯 Dragging over empty message area');
                },
                out: function(event, ui) {
                    console.log('🎯 Dragging out of empty message area');
                }
            });
            console.log('✅ Empty message area made droppable');
        }
    }

    // Add new field
    function addNewField(fieldType) {
        // Generate unique ID with more precision to avoid duplicates
        let fieldId = 'field_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        let fieldHtml = generateFieldHtml(fieldType, fieldId);

        console.log('➕ Adding new field:', fieldType, 'with ID:', fieldId);

        // Remove empty message if it exists
        if ($('.empty-form-message').length) {
            $('.empty-form-message').remove();
        }

        // Add the field
        $('#form-fields').append(fieldHtml);

        // Re-initialize droppable areas after adding field (with delay to ensure DOM is updated)
        setTimeout(function() {
            makeDroppable();
        }, 100);

        // Select the new field
        selectField(fieldId);

        console.log('✅ Field added successfully');
    }
    
    // Generate field HTML
    function generateFieldHtml(fieldType, fieldId) {
        let label = getFieldTypeLabel(fieldType);
        let inputHtml = generateInputHtml(fieldType, fieldId);
        
        return `
            <div class="form-field-item" data-field-id="${fieldId}" data-field-type="${fieldType}">
                <div class="field-controls">
                    <button type="button" class="btn btn-sm btn-outline-primary edit-field">
                        <i class="ti ti-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-field">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
                <div class="field-preview">
                    <label class="form-label">${label}</label>
                    ${inputHtml}
                </div>
            </div>
        `;
    }
    
    // Get field type label
    function getFieldTypeLabel(fieldType) {
        const labels = {
            'text': 'Text Input',
            'email': 'Email Address',
            'phone': 'Phone Number',
            'textarea': 'Message',
            'select': 'Select Option',
            'radio': 'Choose One',
            'checkbox': 'Check All That Apply',
            'date': 'Date',
            'file': 'Upload File'
        };
        return labels[fieldType] || 'Field';
    }
    
    // Generate input HTML
    function generateInputHtml(fieldType, fieldId) {
        switch(fieldType) {
            case 'text':
            case 'email':
            case 'phone':
                return `<input type="${fieldType}" class="form-control" placeholder="Enter ${fieldType}">`;
            case 'textarea':
                return `<textarea class="form-control" rows="3" placeholder="Enter your message"></textarea>`;
            case 'select':
                return `<select class="form-control"><option>Option 1</option><option>Option 2</option></select>`;
            case 'radio':
                return `<div class="form-check"><input class="form-check-input" type="radio" name="${fieldId}"><label class="form-check-label">Option 1</label></div>`;
            case 'checkbox':
                return `<div class="form-check"><input class="form-check-input" type="checkbox"><label class="form-check-label">Option 1</label></div>`;
            case 'date':
                return `<input type="date" class="form-control">`;
            case 'file':
                return `<input type="file" class="form-control">`;
            default:
                return `<input type="text" class="form-control">`;
        }
    }
    
    // Field selection
    $(document).on('click', '.form-field-item', function() {
        selectField($(this).data('field-id'));
    });
    
    function selectField(fieldId) {
        $('.form-field-item').removeClass('selected');
        $(`.form-field-item[data-field-id="${fieldId}"]`).addClass('selected');
        selectedField = fieldId;
        showFieldProperties(fieldId);
    }
    
    // Update field order after sorting
    function updateFieldOrder() {
        console.log('Field order updated');
        // This function would update the field order in the form structure
        // For now, it's just a placeholder
    }

    // Show field properties
    function showFieldProperties(fieldId) {
        $('#no-field-selected').hide();
        $('#field-properties').show();

        // Load field data if it exists
        // This would be populated from the actual field data
        console.log('Showing properties for field:', fieldId);
    }
    
    // Edit field
    $(document).on('click', '.edit-field', function(e) {
        e.stopPropagation();
        let fieldItem = $(this).closest('.form-field-item');
        let fieldId = fieldItem.data('field-id');
        let fieldType = fieldItem.data('field-type');

        console.log('✏️ Editing field:', fieldId, 'Type:', fieldType);

        // Select the field and show properties
        selectField(fieldId);

        // Populate field properties form
        populateFieldProperties(fieldId, fieldType);
    });

    // Update field button
    $(document).on('click', '#update-field-btn', function(e) {
        e.preventDefault();

        if (!selectedField) {
            showNotification('Warning', 'Please select a field to update', 'warning');
            return;
        }

        console.log('🔄 Updating field:', selectedField);

        // Get field data from form
        let fieldData = {
            label: $('#field-label').val() || 'Field Label',
            placeholder: $('#field-placeholder').val() || '',
            required: $('#field-required').is(':checked'),
            helpText: $('#field-help-text').val() || ''
        };

        // Update the field in the DOM
        updateFieldInDOM(selectedField, fieldData);

        showNotification('Success', 'Field updated successfully!', 'success');

        console.log('✅ Field updated:', selectedField, fieldData);
    });

    // Cancel edit button
    $(document).on('click', '#cancel-edit-btn', function(e) {
        e.preventDefault();
        $('#field-properties').hide();
        $('#no-field-selected').show();
        $('.form-field-item').removeClass('selected');
        selectedField = null;
        console.log('❌ Field edit cancelled');
    });

    // Delete field
    $(document).on('click', '.delete-field', function(e) {
        e.stopPropagation();
        if (confirm('Are you sure you want to delete this field?')) {
            let fieldItem = $(this).closest('.form-field-item');
            let fieldId = fieldItem.data('field-id');

            console.log('🗑️ Deleting field:', fieldId);

            fieldItem.remove();
            $('#field-properties').hide();
            $('#no-field-selected').show();
            selectedField = null;

            if ($('#form-fields .form-field-item').length === 0) {
                $('#form-fields').html(`
                    <div class="empty-form-message text-center py-5">
                        <i class="ti ti-forms text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">No fields added yet</h5>
                        <p class="text-muted">Drag field types from the sidebar to start building your form</p>
                    </div>
                `);
                // Re-initialize droppable after adding empty message (with delay)
                setTimeout(function() {
                    makeDroppable();
                }, 100);
            }

            console.log('✅ Field deleted successfully');
        }
    });
    
    // Save form
    $('#save-form-btn').on('click', function() {
        console.log('🖱️ Save button clicked!');
        console.log('🔍 Button element:', this);
        console.log('🔍 Button text:', $(this).text());
        saveForm();
    });
    
    function saveForm() {
        console.log('💾 Starting form save...');

        // Check CSRF token
        let csrfToken = $('meta[name="csrf-token"]').attr('content');
        console.log('🔐 CSRF Token:', csrfToken ? csrfToken.substring(0, 20) + '...' : 'NOT FOUND');

        // Check route
        let updateUrl = '<?php echo e(route("forms.update", $form->id)); ?>';
        console.log('🌐 Update URL:', updateUrl);

        let formStructure = getFormStructure();
        console.log('📋 Form structure:', formStructure);

        let formData = {
            title: $('#form-title').val(),
            description: $('#form-description').val(),
            status: $('#form-status').val(),
            json_structure: JSON.stringify(formStructure)
        };

        console.log('📤 Sending form data:', formData);
        console.log('📊 Form data size:', JSON.stringify(formData).length, 'characters');

        $.ajax({
            url: '<?php echo e(route("forms.update", $form->id)); ?>',
            type: 'PUT',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                console.log('🚀 Sending AJAX request...');
                $('#save-form-btn').prop('disabled', true).text('Saving...');
            },
            success: function(response) {
                console.log('✅ AJAX Success:', response);
                $('#save-form-btn').prop('disabled', false).text('Save Form');

                if (response.status === 'success') {
                    showNotification('Success', response.messages || 'Form saved successfully!', 'success');
                } else {
                    showNotification('Error', response.messages || 'Failed to save form', 'error');
                }
            },
            error: function(xhr) {
                console.log('❌ AJAX Error:', xhr);
                console.log('❌ Response Text:', xhr.responseText);
                console.log('❌ Status:', xhr.status);

                $('#save-form-btn').prop('disabled', false).text('Save Form');

                var errorMessage = 'Something went wrong';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errors = xhr.responseJSON.errors;
                    errorMessage = Object.values(errors).flat().join(', ');
                } else if (xhr.responseText) {
                    errorMessage = 'Server error: ' + xhr.status;
                }
                showNotification('Error', errorMessage, 'error');
            }
        });
    }

    // Populate field properties form
    function populateFieldProperties(fieldId, fieldType) {
        let fieldItem = $(`.form-field-item[data-field-id="${fieldId}"]`);
        let currentLabel = fieldItem.find('label').text().replace(' *', '');
        let currentPlaceholder = fieldItem.find('input, textarea, select').attr('placeholder') || '';

        // Populate the form fields
        $('#field-label').val(currentLabel);
        $('#field-placeholder').val(currentPlaceholder);
        $('#field-required').prop('checked', fieldItem.find('.required').length > 0);
        $('#field-help-text').val(''); // Default for now

        console.log('📝 Populated field properties for:', fieldId);
    }

    // Update field in DOM
    function updateFieldInDOM(fieldId, fieldData) {
        let fieldItem = $(`.form-field-item[data-field-id="${fieldId}"]`);

        // Update label
        fieldItem.find('label').text(fieldData.label);

        // Update placeholder
        let input = fieldItem.find('input, textarea, select');
        if (fieldData.placeholder) {
            input.attr('placeholder', fieldData.placeholder);
        }

        // Update required indicator
        let label = fieldItem.find('label');
        if (fieldData.required) {
            if (!label.find('.required').length) {
                label.append(' <span class="required text-danger">*</span>');
            }
        } else {
            label.find('.required').remove();
        }

        console.log('🔄 Updated field in DOM:', fieldId, fieldData);
    }

    // Get form structure
    function getFormStructure() {
        console.log('📋 Building form structure...');

        let structure = {
            form: {
                title: $('#form-title').val(),
                description: $('#form-description').val(),
                settings: {}
            },
            fields: []
        };

        console.log('📝 Form title:', structure.form.title);
        console.log('📝 Form description:', structure.form.description);

        let fieldCount = $('#form-fields .form-field-item').length;
        console.log('🔢 Found', fieldCount, 'fields in form');

        $('#form-fields .form-field-item').each(function(index) {
            let fieldItem = $(this);
            let fieldData = {
                id: fieldItem.data('field-id'),
                type: fieldItem.data('field-type'),
                label: fieldItem.find('label').text().replace(' *', ''), // Remove required indicator
                order: index + 1,
                placeholder: fieldItem.find('input, textarea, select').attr('placeholder') || '',
                required: fieldItem.find('.required').length > 0
            };

            console.log('📄 Field', (index + 1) + ':', fieldData);
            structure.fields.push(fieldData);
        });

        console.log('✅ Form structure complete:', structure);
        return structure;
    }
    
    // Update form title and description in canvas
    $('#form-title').on('input', function() {
        $('#canvas-form-title').text($(this).val());
    });
    
    $('#form-description').on('input', function() {
        $('#canvas-form-description').text($(this).val());
    });
    
    // Preview form
    $('#preview-btn').on('click', function() {
        $('#previewModal').modal('show');
        loadFormPreview();
    });
    
    function loadFormPreview() {
        $('#form-preview-content').html('<div class="text-center"><i class="ti ti-loader fa-spin"></i> Loading preview...</div>');
        
        // Generate preview HTML
        setTimeout(function() {
            let previewHtml = generateFormPreview();
            $('#form-preview-content').html(previewHtml);
        }, 500);
    }
    
    function generateFormPreview() {
        let html = `<form>`;
        html += `<h4>${$('#form-title').val()}</h4>`;
        if ($('#form-description').val()) {
            html += `<p class="text-muted">${$('#form-description').val()}</p>`;
        }
        
        $('#form-fields .form-field-item').each(function() {
            html += `<div class="mb-3">`;
            html += $(this).find('.field-preview').html();
            html += `</div>`;
        });
        
        html += `<button type="submit" class="btn btn-primary">Submit</button>`;
        html += `</form>`;
        
        return html;
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/forms/edit.blade.php ENDPATH**/ ?>