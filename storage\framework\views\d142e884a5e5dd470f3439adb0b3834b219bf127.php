<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Property Management')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><?php echo e(__('Properties')); ?></h5>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create properties')): ?>
                            <div class="col-auto">
                                <a href="<?php echo e(route('properties.add')); ?>" class="btn btn-secondary">
                                    <i class="ti ti-circle-plus align-text-bottom"></i> <?php echo e(__('Add Property')); ?>

                                </a>
                            </div>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import properties')): ?>
                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                                <i class="ti ti-upload me-1"></i><?php echo e(__('Import')); ?>

                            </button>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export properties')): ?>
                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#exportModal">
                                <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

                            </button>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view property dashboard')): ?>
                            <a href="<?php echo e(route('properties.dashboard')); ?>" class="btn btn-primary">
                                <i class="ti ti-chart-line me-1"></i><?php echo e(__('Dashboard')); ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="GET" action="<?php echo e(route('properties.index')); ?>">
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <label class="form-label"><?php echo e(__('Search')); ?></label>
                                        <input type="text" name="search" class="form-control"
                                            value="<?php echo e(request('search')); ?>" placeholder="<?php echo e(__('Name, code, address...')); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label"><?php echo e(__('Type')); ?></label>
                                        <select name="type" class="form-select">
                                            <option value=""><?php echo e(__('All Types')); ?></option>
                                            <?php $__currentLoopData = \App\Models\Property::$types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(request('type') == $key ? 'selected' : ''); ?>>
                                                    <?php echo e($value); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label"><?php echo e(__('Status')); ?></label>
                                        <select name="status" class="form-select">
                                            <option value=""><?php echo e(__('All Status')); ?></option>
                                            <?php $__currentLoopData = \App\Models\Property::$statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(request('status') == $key ? 'selected' : ''); ?>>
                                                    <?php echo e($value); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label"><?php echo e(__('City')); ?></label>
                                        <select name="city" class="form-select">
                                            <option value=""><?php echo e(__('All Cities')); ?></option>
                                            <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($city); ?>" <?php echo e(request('city') == $city ? 'selected' : ''); ?>>
                                                    <?php echo e($city); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label"><?php echo e(__('Project')); ?></label>
                                        <select name="project_id" class="form-select">
                                            <option value=""><?php echo e(__('All Projects')); ?></option>
                                            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($project->id); ?>" <?php echo e(request('project_id') == $project->id ? 'selected' : ''); ?>>
                                                    <?php echo e($project->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-outline-primary">
                                                <i class="ti ti-search me-1"></i><?php echo e(__('Filter')); ?>

                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <?php if($properties->count() > 0): ?>
                        <!-- Bulk Actions -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" id="selectAll" class="form-check-input me-2">
                                <label for="selectAll" class="form-check-label"><?php echo e(__('Select All')); ?></label>
                            </div>
                            <div class="bulk-actions" style="display: none;">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulk manage properties')): ?>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkStatusModal">
                                        <i class="ti ti-edit me-1"></i><?php echo e(__('Bulk Status')); ?>

                                    </button>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('assign property agents')): ?>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#bulkAssignModal">
                                        <i class="ti ti-user-plus me-1"></i><?php echo e(__('Bulk Assign')); ?>

                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="30">
                                            <input type="checkbox" class="form-check-input" id="selectAllTable">
                                        </th>
                                        <th><?php echo e(__('Property Code')); ?></th>
                                        <th><?php echo e(__('Name')); ?></th>
                                        <th><?php echo e(__('Type')); ?></th>
                                        <th><?php echo e(__('Project')); ?></th>
                                        <th><?php echo e(__('City')); ?></th>
                                        <th><?php echo e(__('Price')); ?></th>
                                        <th><?php echo e(__('Status')); ?></th>
                                        <th><?php echo e(__('Agent')); ?></th>
                                        <th><?php echo e(__('Created')); ?></th>
                                        <th width="120"><?php echo e(__('Actions')); ?></th>
                                    </tr>
                                </thead>
                                    <?php $__currentLoopData = $properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input property-checkbox" value="<?php echo e($property->id); ?>">
                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('properties.show', $property->id)); ?>" class="btn btn-outline-primary btn-sm">
                                                    <?php echo e($property->property_code); ?>

                                                </a>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if($property->photos->first()): ?>
                                                        <img src="<?php echo e($property->photos->first()->thumbnail_url); ?>"
                                                             class="rounded me-2" width="40" height="40" alt="Property">
                                                    <?php endif; ?>
                                                    <div>
                                                        <a href="<?php echo e(route('properties.show', $property)); ?>" class="text-decoration-none fw-medium">
                                                            <?php echo e($property->name); ?>

                                                        </a>
                                                        <br><small class="text-muted"><?php echo e(Str::limit($property->address, 40)); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo e(\App\Models\Property::$types[$property->type] ?? $property->type); ?></span>
                                            </td>
                                            <td>
                                                <?php echo e($property->project->name ?? '-'); ?>

                                                <?php if($property->project && $property->project->code): ?>
                                                    <br><small class="text-muted"><?php echo e($property->project->code); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($property->city); ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo e($property->formatted_price); ?></strong>
                                                    <?php if($property->price_per_sqft): ?>
                                                        <br><small class="text-muted"><?php echo e(currency_format_with_sym($property->price_per_sqft)); ?>/sqft</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($property->status_color); ?>">
                                                    <?php echo e(\App\Models\Property::$statuses[$property->status] ?? ucfirst($property->status)); ?>

                                                </span>
                                                <?php if($property->is_featured): ?>
                                                    <br><span class="badge bg-warning mt-1"><?php echo e(__('Featured')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($property->assignedAgent): ?>
                                                    <span class="text-success"><?php echo e($property->assignedAgent->name); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($property->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show properties')): ?>
                                                        <a href="<?php echo e(route('properties.show', $property)); ?>" class="btn btn-sm btn-outline-primary" title="<?php echo e(__('View')); ?>">
                                                            <i class="ti ti-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit properties')): ?>
                                                        <a href="#" class="btn btn-sm btn-outline-secondary customModal" data-size="xl"
                                                            data-url="<?php echo e(route('properties.edit', $property)); ?>" data-title="<?php echo e(__('Edit Property')); ?>" title="<?php echo e(__('Edit')); ?>">
                                                            <i class="ti ti-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete properties')): ?>
                                                        <form method="POST" action="<?php echo e(route('properties.destroy', $property)); ?>" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('<?php echo e(__('Are you sure?')); ?>')" title="<?php echo e(__('Delete')); ?>">
                                                                <i class="ti ti-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <?php echo e(__('Showing')); ?> <?php echo e($properties->firstItem()); ?> <?php echo e(__('to')); ?> <?php echo e($properties->lastItem()); ?>

                                <?php echo e(__('of')); ?> <?php echo e($properties->total()); ?> <?php echo e(__('results')); ?>

                            </div>
                            <?php echo e($properties->appends(request()->query())->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="ti ti-building display-1 text-muted"></i>
                            <h5 class="mt-3"><?php echo e(__('No properties found')); ?></h5>
                            <p class="text-muted"><?php echo e(__('Start by creating your first property or importing from Excel.')); ?></p>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create properties')): ?>
                                <a href="<?php echo e(route('properties.add')); ?>" class="btn btn-primary">
                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Property')); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import properties')): ?>
        <div class="modal fade" id="importModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('properties.import')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title"><?php echo e(__('Import Properties')); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="project_id" class="form-label"><?php echo e(__('Default Project')); ?></label>
                                <select name="project_id" id="project_id" class="form-select" required>
                                    <option value=""><?php echo e(__('Select Project')); ?></option>
                                    <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($project->id); ?>"><?php echo e($project->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="file" class="form-label"><?php echo e(__('Excel File')); ?></label>
                                <input type="file" name="file" id="file" class="form-control" accept=".xlsx,.xls,.csv" required>
                                <div class="form-text"><?php echo e(__('Supported formats: Excel (.xlsx, .xls) and CSV')); ?></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Import')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Export Modal -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export properties')): ?>
        <div class="modal fade" id="exportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('properties.export')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title"><?php echo e(__('Export Properties')); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="format" class="form-label"><?php echo e(__('Export Format')); ?></label>
                                <select name="format" id="format" class="form-select" required>
                                    <option value="csv"><?php echo e(__('CSV')); ?></option>
                                    <option value="excel"><?php echo e(__('Excel')); ?></option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><?php echo e(__('Include Fields')); ?></label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="include_photos" id="include_photos" checked>
                                    <label class="form-check-label" for="include_photos"><?php echo e(__('Photo URLs')); ?></label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="include_pricing" id="include_pricing" checked>
                                    <label class="form-check-label" for="include_pricing"><?php echo e(__('Pricing Details')); ?></label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Export')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Bulk Status Modal -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulk manage properties')): ?>
        <div class="modal fade" id="bulkStatusModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('properties.bulk-status')); ?>" method="POST" id="bulkStatusForm">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title"><?php echo e(__('Bulk Update Status')); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="status" class="form-label"><?php echo e(__('New Status')); ?></label>
                                <select name="status" id="status" class="form-select" required>
                                    <option value=""><?php echo e(__('Select Status')); ?></option>
                                    <?php $__currentLoopData = \App\Models\Property::$statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div id="selectedPropertiesCountStatus"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Update')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Bulk Assign Modal -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('assign property agents')): ?>
        <div class="modal fade" id="bulkAssignModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('properties.bulk-assign')); ?>" method="POST" id="bulkAssignForm">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title"><?php echo e(__('Bulk Assign Properties')); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="assigned_agent_id" class="form-label"><?php echo e(__('Assign to Agent')); ?></label>
                                <select name="assigned_agent_id" id="assigned_agent_id" class="form-select" required>
                                    <option value=""><?php echo e(__('Select Agent')); ?></option>
                                    <?php $__currentLoopData = $agents ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($agent->id); ?>"><?php echo e($agent->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div id="selectedPropertiesCount"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Assign')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Bulk selection functionality
    document.addEventListener('DOMContentLoaded', function() {
        const selectAll = document.getElementById('selectAll');
        const selectAllTable = document.getElementById('selectAllTable');
        const propertyCheckboxes = document.querySelectorAll('.property-checkbox');
        const bulkActions = document.querySelector('.bulk-actions');

        function updateBulkActions() {
            const checkedBoxes = document.querySelectorAll('.property-checkbox:checked');
            if (checkedBoxes.length > 0) {
                bulkActions.style.display = 'block';
                const countElement1 = document.getElementById('selectedPropertiesCount');
                const countElement2 = document.getElementById('selectedPropertiesCountStatus');
                if (countElement1) countElement1.textContent = `${checkedBoxes.length} properties selected`;
                if (countElement2) countElement2.textContent = `${checkedBoxes.length} properties selected`;
            } else {
                bulkActions.style.display = 'none';
            }
        }

        if (selectAll) {
            selectAll.addEventListener('change', function() {
                propertyCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
        }

        if (selectAllTable) {
            selectAllTable.addEventListener('change', function() {
                propertyCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
        }

        propertyCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });

        // Handle bulk forms
        const bulkAssignForm = document.getElementById('bulkAssignForm');
        if (bulkAssignForm) {
            bulkAssignForm.addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('.property-checkbox:checked');
                checkedBoxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'property_ids[]';
                    input.value = checkbox.value;
                    this.appendChild(input);
                });
            });
        }

        const bulkStatusForm = document.getElementById('bulkStatusForm');
        if (bulkStatusForm) {
            bulkStatusForm.addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('.property-checkbox:checked');
                checkedBoxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'property_ids[]';
                    input.value = checkbox.value;
                    this.appendChild(input);
                });
            });
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/index.blade.php ENDPATH**/ ?>