<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task_comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained('tasks')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            $table->text('comment');
            $table->enum('type', ['comment', 'status_change', 'assignment_change', 'system'])->default('comment');
            
            // For system comments, store additional metadata
            $table->json('metadata')->nullable();
            
            $table->boolean('is_internal')->default(false); // Internal comments vs client-visible
            $table->boolean('is_pinned')->default(false);   // Pin important comments
            
            $table->index(['task_id']);
            $table->index(['user_id']);
            $table->index(['type']);
            $table->index(['created_at']);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task_comments');
    }
};
