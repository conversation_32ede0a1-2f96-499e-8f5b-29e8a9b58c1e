<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Documents')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Documents')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-button'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create documents')): ?>
            <a href="#" class="btn btn-sm btn-primary customModal" data-size="lg"
                data-url="<?php echo e(route('documents.create')); ?>" data-title="<?php echo e(__('Create Document')); ?>">
                <i class="ti ti-plus"></i> <?php echo e(__('Create Document')); ?>

            </a>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import document data')): ?>
            <button type="button" class="btn btn-sm btn-info me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                <i class="ti ti-upload me-1"></i><?php echo e(__('Import')); ?>

            </button>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export document data')): ?>
            <a href="<?php echo e(route('documents.export', request()->query())); ?>" class="btn btn-sm btn-success me-2">
                <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

            </a>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view document dashboard')): ?>
            <a href="<?php echo e(route('documents.dashboard')); ?>" class="btn btn-sm btn-secondary">
                <i class="ti ti-chart-bar"></i> <?php echo e(__('Dashboard')); ?>

            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <form method="GET" action="<?php echo e(route('documents.index')); ?>" id="filter-form">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <input type="text" name="search" class="form-control" placeholder="<?php echo e(__('Search documents...')); ?>" value="<?php echo e(request('search')); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <select name="type" class="form-control">
                                                    <option value=""><?php echo e(__('All Types')); ?></option>
                                                    <?php $__currentLoopData = \App\Models\Document::$types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($key); ?>" <?php echo e(request('type') == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <select name="category" class="form-control">
                                                    <option value=""><?php echo e(__('All Categories')); ?></option>
                                                    <?php $__currentLoopData = \App\Models\Document::$categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($key); ?>" <?php echo e(request('category') == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <select name="status" class="form-control">
                                                    <option value=""><?php echo e(__('All Statuses')); ?></option>
                                                    <?php $__currentLoopData = \App\Models\Document::$statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($key); ?>" <?php echo e(request('status') == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <select name="project_id" class="form-control">
                                                    <option value=""><?php echo e(__('All Projects')); ?></option>
                                                    <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($project->id); ?>" <?php echo e(request('project_id') == $project->id ? 'selected' : ''); ?>><?php echo e($project->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button type="submit" class="btn btn-primary"><?php echo e(__('Filter')); ?></button>
                                        </div>
                                        <div class="col-md-2">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create documents')): ?>
                                                <a href="#" class="btn btn-success customModal" data-size="lg"
                                                    data-url="<?php echo e(route('documents.create')); ?>" data-title="<?php echo e(__('Create Document')); ?>">
                                                    <i class="ti ti-plus"></i> <?php echo e(__('Create Document')); ?>

                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Export and Actions -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export document data')): ?>
                                    <a href="<?php echo e(route('documents.export', request()->query())); ?>" class="btn btn-sm btn-success">
                                        <i class="ti ti-download"></i> <?php echo e(__('Export')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <select name="per_page" class="form-control form-control-sm" onchange="changePerPage(this.value)">
                                        <option value="15" <?php echo e(request('per_page') == 15 ? 'selected' : ''); ?>>15 <?php echo e(__('per page')); ?></option>
                                        <option value="25" <?php echo e(request('per_page') == 25 ? 'selected' : ''); ?>>25 <?php echo e(__('per page')); ?></option>
                                        <option value="50" <?php echo e(request('per_page') == 50 ? 'selected' : ''); ?>>50 <?php echo e(__('per page')); ?></option>
                                        <option value="100" <?php echo e(request('per_page') == 100 ? 'selected' : ''); ?>>100 <?php echo e(__('per page')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Documents Table -->
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Document')); ?></th>
                                    <th><?php echo e(__('Type')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Project')); ?></th>
                                    <th><?php echo e(__('File')); ?></th>
                                    <th><?php echo e(__('Signatures')); ?></th>
                                    <th><?php echo e(__('Created')); ?></th>
                                    <th width="200px"><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <?php if($document->is_confidential): ?>
                                                        <span class="badge bg-danger"><?php echo e(__('Confidential')); ?></span>
                                                    <?php endif; ?>
                                                    <?php if($document->is_compliance_document): ?>
                                                        <span class="badge bg-info"><?php echo e(__('Compliance')); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e($document->name); ?></h6>
                                                    <small class="text-muted"><?php echo e($document->document_number); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e(\App\Models\Document::$types[$document->type] ?? $document->type); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo e($document->status == 'published' ? 'success' : ($document->status == 'draft' ? 'warning' : 'secondary')); ?>">
                                                <?php echo e(\App\Models\Document::$statuses[$document->status] ?? $document->status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($document->project): ?>
                                                <a href="<?php echo e(route('projects.show', $document->project)); ?>" class="text-decoration-none">
                                                    <?php echo e($document->project->name); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('No Project')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($document->file_path): ?>
                                                <div class="d-flex align-items-center">
                                                    <i class="ti ti-file me-1"></i>
                                                    <div>
                                                        <small><?php echo e($document->file_name); ?></small><br>
                                                        <small class="text-muted"><?php echo e($document->file_size_human); ?></small>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('No File')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($document->requires_signature): ?>
                                                <span class="badge bg-<?php echo e($document->signature_status == 'complete' ? 'success' : ($document->signature_status == 'pending' ? 'warning' : 'secondary')); ?>">
                                                    <?php echo e(\App\Models\Document::$signatureStatuses[$document->signature_status] ?? $document->signature_status); ?>

                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('Not Required')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div>
                                                <small><?php echo e($document->created_at->format('M d, Y')); ?></small><br>
                                                <small class="text-muted"><?php echo e(__('by')); ?> <?php echo e($document->creator->name ?? 'Unknown'); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view documents')): ?>
                                                <div class="action-btn bg-warning ms-2">
                                                    <a href="<?php echo e(route('documents.show', $document)); ?>" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('View')); ?>">
                                                        <i class="ti ti-eye text-white"></i>
                                                    </a>
                                                </div>
                                            <?php endif; ?>

                                            <?php if($document->file_path): ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('download documents')): ?>
                                                    <div class="action-btn bg-info ms-2">
                                                        <a href="<?php echo e(route('documents.download', $document)); ?>" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('Download')); ?>">
                                                            <i class="ti ti-download text-white"></i>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>

                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit documents')): ?>
                                                <div class="action-btn bg-info ms-2">
                                                    <a href="#" class="mx-3 btn btn-sm align-items-center customModal" data-size="lg"
                                                        data-url="<?php echo e(route('documents.edit', $document)); ?>" data-title="<?php echo e(__('Edit Document')); ?>" data-bs-toggle="tooltip" title="<?php echo e(__('Edit')); ?>">
                                                        <i class="ti ti-pencil text-white"></i>
                                                    </a>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete documents')): ?>
                                                <div class="action-btn bg-danger ms-2">
                                                    <?php echo Form::open(['method' => 'DELETE', 'route' => ['documents.destroy', $document], 'id' => 'delete-form-' . $document->id, 'style' => 'display: inline']); ?>

                                                    <a href="#" class="mx-3 btn btn-sm align-items-center bs-pass-para" data-bs-toggle="tooltip" title="<?php echo e(__('Delete')); ?>" data-original-title="<?php echo e(__('Delete')); ?>" data-confirm="<?php echo e(__('Are You Sure?')); ?>" data-text="<?php echo e(__('This action can not be undone. Do you want to continue?')); ?>" data-confirm-yes="delete-form-<?php echo e($document->id); ?>">
                                                        <i class="ti ti-trash text-white text-white"></i>
                                                    </a>
                                                    <?php echo Form::close(); ?>

                                                </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-5">
                                            <i class="ti ti-files display-1 text-muted"></i>
                                            <h5 class="mt-3"><?php echo e(__('No documents found')); ?></h5>
                                            <p class="text-muted"><?php echo e(__('Start by creating your first document.')); ?></p>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create documents')): ?>
                                                <a href="#" class="btn btn-primary customModal" data-size="lg"
                                                    data-url="<?php echo e(route('documents.create')); ?>" data-title="<?php echo e(__('Create Document')); ?>">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Document')); ?>

                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import document data')): ?>
        <div class="modal fade" id="importModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('documents.import')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title"><?php echo e(__('Import Documents')); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="file" class="form-label"><?php echo e(__('Excel File')); ?></label>
                                <input type="file" name="file" id="file" class="form-control" accept=".xlsx,.xls,.csv" required>
                                <div class="form-text"><?php echo e(__('Supported formats: Excel (.xlsx, .xls) and CSV')); ?></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Import')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Pagination -->
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <?php echo e(__('Showing')); ?> <?php echo e($documents->firstItem() ?? 0); ?> <?php echo e(__('to')); ?> <?php echo e($documents->lastItem() ?? 0); ?> <?php echo e(__('of')); ?> <?php echo e($documents->total()); ?> <?php echo e(__('results')); ?>

                </div>
                <div>
                    <?php echo e($documents->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        function changePerPage(perPage) {
            const url = new URL(window.location);
            url.searchParams.set('per_page', perPage);
            window.location = url;
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/documents/index.blade.php ENDPATH**/ ?>