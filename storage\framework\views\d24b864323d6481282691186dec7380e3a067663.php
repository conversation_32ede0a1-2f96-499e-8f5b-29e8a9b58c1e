<?php echo e(Form::model($property, ['route' => ['properties.update-unit-details', $property->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data'])); ?>

<div class="modal-body">
    <div class="row">
        <!-- Area & Dimensions Section -->
        <div class="col-lg-12">
            <h6 class="mb-3"><?php echo e(__('Area & Dimensions')); ?></h6>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('carpet_area', __('Carpet Area (sq ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('carpet_area', null, ['class' => 'form-control', 'step' => '0.01', 'min' => '1', 'placeholder' => __('Enter carpet area')])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('built_up_area', __('Built-up Area (sq ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('built_up_area', null, ['class' => 'form-control', 'step' => '0.01', 'min' => '1', 'placeholder' => __('Enter built-up area')])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('super_area', __('Super Area (sq ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('super_area', null, ['class' => 'form-control', 'step' => '0.01', 'min' => '1', 'placeholder' => __('Enter super area')])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('length', __('Length (ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('length', null, ['class' => 'form-control', 'step' => '0.01', 'min' => '1', 'placeholder' => __('Length')])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('width', __('Width (ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('width', null, ['class' => 'form-control', 'step' => '0.01', 'min' => '1', 'placeholder' => __('Width')])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('facing', __('Facing Direction'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('facing', [
                    '' => __('Select facing'),
                    'north' => __('North'),
                    'south' => __('South'),
                    'east' => __('East'),
                    'west' => __('West'),
                    'north_east' => __('North-East'),
                    'north_west' => __('North-West'),
                    'south_east' => __('South-East'),
                    'south_west' => __('South-West')
                ], null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('floor_number', __('Floor Number'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('floor_number', null, ['class' => 'form-control', 'min' => '0', 'placeholder' => __('Floor number')])); ?>

            </div>
        </div>

        <!-- Room Configuration Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Room Configuration')); ?></h6>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('bedrooms', __('Bedrooms'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('bedrooms', null, ['class' => 'form-control', 'min' => '0', 'max' => '20', 'placeholder' => __('Number of bedrooms')])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('bathrooms', __('Bathrooms'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('bathrooms', null, ['class' => 'form-control', 'min' => '0', 'max' => '20', 'placeholder' => __('Number of bathrooms')])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('balconies', __('Balconies'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('balconies', null, ['class' => 'form-control', 'min' => '0', 'max' => '10', 'placeholder' => __('Number of balconies')])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('parking_spaces', __('Parking Spaces'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('parking_spaces', null, ['class' => 'form-control', 'min' => '0', 'max' => '10', 'placeholder' => __('Parking spaces')])); ?>

            </div>
        </div>

        <!-- Property Features Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Property Features')); ?></h6>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('furnishing', __('Furnishing Status'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('furnishing', [
                    '' => __('Select furnishing status'),
                    'unfurnished' => __('Unfurnished'),
                    'semi_furnished' => __('Semi-Furnished'),
                    'fully_furnished' => __('Fully Furnished')
                ], null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('age_of_property', __('Age of Property'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('age_of_property', [
                    '' => __('Select property age'),
                    'under_construction' => __('Under Construction'),
                    'new_launch' => __('New Launch'),
                    '0-1' => __('0-1 Years'),
                    '1-5' => __('1-5 Years'),
                    '5-10' => __('5-10 Years'),
                    '10-15' => __('10-15 Years'),
                    '15+' => __('15+ Years')
                ], null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('total_floors', __('Total Floors in Building'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('total_floors', null, ['class' => 'form-control', 'min' => '1', 'max' => '200', 'placeholder' => __('Total floors')])); ?>

            </div>
        </div>

        <!-- Amenities Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Amenities')); ?></h6>
        </div>

        <?php
            $currentAmenities = is_string($property->amenities) ? json_decode($property->amenities, true) : ($property->amenities ?? []);
        ?>

        <div class="col-lg-3 col-md-6">
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'gym', in_array('gym', $currentAmenities), ['class' => 'form-check-input', 'id' => 'gym'])); ?>

                <?php echo e(Form::label('gym', __('Gym'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'swimming_pool', in_array('swimming_pool', $currentAmenities), ['class' => 'form-check-input', 'id' => 'swimming_pool'])); ?>

                <?php echo e(Form::label('swimming_pool', __('Swimming Pool'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'garden', in_array('garden', $currentAmenities), ['class' => 'form-check-input', 'id' => 'garden'])); ?>

                <?php echo e(Form::label('garden', __('Garden'), ['class' => 'form-check-label'])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'security', in_array('security', $currentAmenities), ['class' => 'form-check-input', 'id' => 'security'])); ?>

                <?php echo e(Form::label('security', __('24/7 Security'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'elevator', in_array('elevator', $currentAmenities), ['class' => 'form-check-input', 'id' => 'elevator'])); ?>

                <?php echo e(Form::label('elevator', __('Elevator'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'power_backup', in_array('power_backup', $currentAmenities), ['class' => 'form-check-input', 'id' => 'power_backup'])); ?>

                <?php echo e(Form::label('power_backup', __('Power Backup'), ['class' => 'form-check-label'])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'clubhouse', in_array('clubhouse', $currentAmenities), ['class' => 'form-check-input', 'id' => 'clubhouse'])); ?>

                <?php echo e(Form::label('clubhouse', __('Clubhouse'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'playground', in_array('playground', $currentAmenities), ['class' => 'form-check-input', 'id' => 'playground'])); ?>

                <?php echo e(Form::label('playground', __('Playground'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'parking', in_array('parking', $currentAmenities), ['class' => 'form-check-input', 'id' => 'parking'])); ?>

                <?php echo e(Form::label('parking', __('Covered Parking'), ['class' => 'form-check-label'])); ?>

            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'water_supply', in_array('water_supply', $currentAmenities), ['class' => 'form-check-input', 'id' => 'water_supply'])); ?>

                <?php echo e(Form::label('water_supply', __('24/7 Water Supply'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'internet', in_array('internet', $currentAmenities), ['class' => 'form-check-input', 'id' => 'internet'])); ?>

                <?php echo e(Form::label('internet', __('Internet Ready'), ['class' => 'form-check-label'])); ?>

            </div>
            <div class="form-check mb-2">
                <?php echo e(Form::checkbox('amenities[]', 'air_conditioning', in_array('air_conditioning', $currentAmenities), ['class' => 'form-check-input', 'id' => 'air_conditioning'])); ?>

                <?php echo e(Form::label('air_conditioning', __('Air Conditioning'), ['class' => 'form-check-label'])); ?>

            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Update Unit Details')); ?>" class="btn btn-primary">
</div>
<?php echo e(Form::close()); ?>

<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/edit-unit-details.blade.php ENDPATH**/ ?>