<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Models\LeadActivity;
use App\Models\User;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Imports\MappedLeadsImport;
use Carbon\Carbon;

class LeadController extends Controller
{
    public function index(Request $request)
    {
        if (!Gate::check('manage leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get user and tenant info
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Start with base query and disable global scope for owner users
        if ($user->type == 'owner') {
            $query = Lead::withoutGlobalScope('tenant')->with(['assignedAgent', 'project', 'site', 'channelPartner']);
            // Owner sees all leads in their organization (created by them or their agents)
            $query->where(function($q) use ($user, $tenantId) {
                $q->where('lead_owner_id', $user->id)
                  ->orWhere('created_by', $user->id)
                  ->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                      $subQ->where('parent_id', $tenantId);
                  });
            });
        } else {
            $query = Lead::with(['assignedAgent', 'project', 'site', 'channelPartner']);

            // Apply user-based filtering for non-owner users
            if ($user->type == 'agent') {
                $query->where('assigned_to', $user->id);
            } elseif ($user->type == 'channel partner') {
                $query->where('channel_partner_id', $user->id);
            }
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        $leads = $query->orderBy('created_at', 'desc')
            ->paginate(20);

        // Get additional data for filters
        $projects = Project::active()->get(); // Get all active projects
        $agents = User::where('type', 'agent')->where('parent_id', parentId())->get();

        return view('leads.index', compact('leads', 'projects', 'agents'));
    }

    public function create()
    {
        if (!Gate::check('create leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get dropdown data
        $projects = Project::active()->pluck('name', 'id')->toArray();
        $agents = User::where('type', 'agent')->where('parent_id', parentId())->pluck('name', 'id')->toArray();
        $channelPartners = User::where('type', 'channel partner')->where('parent_id', parentId())->pluck('name', 'id')->toArray();

        // Status and priority options
        $statuses = [
            'new' => __('New'),
            'contacted' => __('Contacted'),
            'qualified' => __('Qualified'),
            'proposal' => __('Proposal'),
            'negotiation' => __('Negotiation'),
            'closed_won' => __('Closed Won'),
            'closed_lost' => __('Closed Lost')
        ];

        $priorities = [
            'low' => __('Low'),
            'medium' => __('Medium'),
            'high' => __('High')
        ];

        return view('leads.create', compact('projects', 'agents', 'channelPartners', 'statuses', 'priorities'));
    }

    public function store(Request $request)
    {
        if (!Gate::check('create leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'status' => 'required|string',
            'priority' => 'required|string',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        $lead = new Lead();
        $lead->fill($request->all());
        $lead->tenant_id = Auth::user()->parent_id ?? Auth::user()->id;
        $lead->save();

        return redirect()->route('leads.index')->with('success', __('Lead successfully created.'));
    }

    public function show(Lead $lead)
    {
        if (!Gate::check('show leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        return view('leads.show', compact('lead'));
    }

    public function edit(Lead $lead)
    {
        if (!Gate::check('edit leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        // Get dropdown data
        $projects = Project::active()->pluck('name', 'id')->toArray();
        $agents = User::where('type', 'agent')->where('parent_id', parentId())->pluck('name', 'id')->toArray();
        $channelPartners = User::where('type', 'channel partner')->where('parent_id', parentId())->pluck('name', 'id')->toArray();

        // Status and priority options
        $statuses = [
            'new' => __('New'),
            'contacted' => __('Contacted'),
            'qualified' => __('Qualified'),
            'proposal' => __('Proposal'),
            'negotiation' => __('Negotiation'),
            'closed_won' => __('Closed Won'),
            'closed_lost' => __('Closed Lost')
        ];

        $priorities = [
            'low' => __('Low'),
            'medium' => __('Medium'),
            'high' => __('High')
        ];

        return view('leads.edit', compact('lead', 'projects', 'agents', 'channelPartners', 'statuses', 'priorities'));
    }

    public function update(Request $request, Lead $lead)
    {
        if (!Gate::check('edit leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'status' => 'required|string',
            'priority' => 'required|string',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        $lead->fill($request->all());
        $lead->save();

        return redirect()->route('leads.index')->with('success', __('Lead successfully updated.'));
    }

    public function destroy(Lead $lead)
    {
        if (!Gate::check('delete leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $lead->delete();

        return redirect()->route('leads.index')->with('success', __('Lead successfully deleted.'));
    }

    public function assign(Request $request, Lead $lead)
    {
        if (!Gate::check('assign leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'assigned_agent_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        $lead->assigned_agent_id = $request->assigned_agent_id;
        $lead->assigned_at = now();
        $lead->save();

        return redirect()->back()->with('success', __('Lead successfully assigned.'));
    }

    public function convert(Request $request, Lead $lead)
    {
        if (!Gate::check('convert leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $lead->status = 'converted';
        $lead->converted_at = now();
        $lead->save();

        return redirect()->back()->with('success', __('Lead successfully converted.'));
    }

    public function bulkAssign(Request $request)
    {
        if (!Gate::check('bulk manage leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'lead_ids' => 'required|array',
            'assigned_agent_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        Lead::whereIn('id', $request->lead_ids)->update([
            'assigned_agent_id' => $request->assigned_agent_id,
            'assigned_at' => now(),
        ]);

        return redirect()->back()->with('success', __('Leads successfully assigned.'));
    }

    public function bulkUpdateStatus(Request $request)
    {
        if (!Gate::check('bulk manage leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'lead_ids' => 'required|array',
            'status' => 'required|string',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        Lead::whereIn('id', $request->lead_ids)->update([
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', __('Lead status successfully updated.'));
    }

    public function bulkDelete(Request $request)
    {
        if (!Gate::check('delete leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'lead_ids' => 'required|array',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        Lead::whereIn('id', $request->lead_ids)->delete();

        return redirect()->back()->with('success', __('Leads successfully deleted.'));
    }

    public function import(Request $request)
    {
        if (!Gate::check('import leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xlsx,xls|max:2048',
        ]);

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        try {
            // Basic CSV import logic - can be enhanced later
            $file = $request->file('file');
            $path = $file->store('imports');

            // For now, just return success - actual import logic can be implemented later
            return redirect()->back()->with('success', __('Import functionality will be implemented soon.'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', __('Import failed: ') . $e->getMessage());
        }
    }

    public function export(Request $request)
    {
        if (!Gate::check('export leads')) {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

        try {
            // Get user and tenant info
            $user = Auth::user();
            $tenantId = $user->parent_id ?? $user->id;

            // Apply same filtering logic as index method
            if ($user->type == 'owner') {
                $query = Lead::withoutGlobalScope('tenant')->with(['assignedAgent', 'project', 'site', 'channelPartner']);
                // Owner sees all leads in their organization
                $query->where(function($q) use ($user, $tenantId) {
                    $q->where('lead_owner_id', $user->id)
                      ->orWhere('created_by', $user->id)
                      ->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                          $subQ->where('parent_id', $tenantId);
                      });
                });
            } else {
                $query = Lead::with(['assignedAgent', 'project', 'site', 'channelPartner']);

                // Apply user-based filtering for non-owner users
                if ($user->type == 'agent') {
                    $query->where('assigned_to', $user->id);
                } elseif ($user->type == 'channel partner') {
                    $query->where('channel_partner_id', $user->id);
                }
            }

            // Apply filters if provided
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('priority')) {
                $query->where('priority', $request->priority);
            }

            if ($request->filled('project_id')) {
                $query->where('project_id', $request->project_id);
            }

            $leads = $query->get();

            // Create CSV content
            $csvContent = "Name,Phone,Email,Status,Priority,Project,Assigned Agent,City,State,Created At\n";

            foreach ($leads as $lead) {
                $csvContent .= sprintf(
                    '"%s","%s","%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                    $lead->name,
                    $lead->phone,
                    $lead->email ?? '',
                    ucfirst($lead->status),
                    ucfirst($lead->priority),
                    $lead->project->name ?? '',
                    $lead->assignedAgent->name ?? '',
                    $lead->city ?? '',
                    $lead->state ?? '',
                    $lead->created_at->format('Y-m-d H:i:s')
                );
            }

            $filename = 'leads_export_' . date('Y-m-d_H-i-s') . '.csv';

            return response($csvContent)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', __('Export failed: ') . $e->getMessage());
        }
    }

    /**
     * Show the import wizard page
     */
    public function importWizard()
    {
        // DEEP DEBUG: Log method entry
        Log::info('🚀 IMPORT WIZARD: Method called', [
            'timestamp' => now(),
            'user_id' => Auth::id(),
            'user_type' => Auth::user()->type ?? 'N/A',
            'user_name' => Auth::user()->name ?? 'N/A',
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId()
        ]);

        // DEEP DEBUG: Permission system check
        try {
            $importWizardPermission = \Spatie\Permission\Models\Permission::firstOrCreate([
                'name' => 'import-wizard',
                'guard_name' => 'web'
            ]);

            $ownerRole = \Spatie\Permission\Models\Role::where('name', 'owner')->first();
            if ($ownerRole && !$ownerRole->hasPermissionTo('import-wizard')) {
                $ownerRole->givePermissionTo('import-wizard');
            }

            Log::info('🔐 IMPORT WIZARD: Permission setup completed', [
                'permission_exists' => !!$importWizardPermission,
                'owner_role_exists' => !!$ownerRole,
                'permission_assigned' => $ownerRole ? $ownerRole->hasPermissionTo('import-wizard') : false
            ]);
        } catch (\Exception $e) {
            Log::error('❌ IMPORT WIZARD: Permission setup failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // DEEP DEBUG: Permission validation
        $hasImportPermission = Gate::check('import leads');
        $hasImportWizardPermission = Gate::check('import-wizard');
        $isOwner = Auth::user()->type === 'owner';

        Log::info('🔍 IMPORT WIZARD: Permission check results', [
            'has_import_permission' => $hasImportPermission,
            'has_import_wizard_permission' => $hasImportWizardPermission,
            'is_owner' => $isOwner,
            'user_type' => Auth::user()->type,
            'user_id' => Auth::id()
        ]);

        if (!$hasImportPermission && !$hasImportWizardPermission && !$isOwner) {
            Log::warning('🚫 IMPORT WIZARD: Access denied', [
                'user_id' => Auth::id(),
                'reason' => 'Insufficient permissions'
            ]);
            return redirect()->back()->with('error', __('Permission Denied. You need "import leads" permission to access this page.'));
        }

        try {
            // DEEP DEBUG: Project loading
            $tenant_id = Auth::user()->parent_id ?? Auth::user()->id;
            Log::info('📊 IMPORT WIZARD: Loading projects', [
                'tenant_id' => $tenant_id,
                'user_parent_id' => Auth::user()->parent_id
            ]);

            // Get projects using created_by relationship
            $tenantUserIds = \App\Models\User::where(function($q) use ($tenant_id) {
                $q->where('id', $tenant_id)->orWhere('parent_id', $tenant_id);
            })->pluck('id');

            $projects = Project::whereIn('created_by', $tenantUserIds)->select('id', 'name')->get();

            Log::info('✅ IMPORT WIZARD: Projects loaded successfully', [
                'projects_count' => $projects->count(),
                'projects' => $projects->toArray()
            ]);

            return view('leads.import-wizard', compact('projects'));
        } catch (\Exception $e) {
            Log::error('❌ IMPORT WIZARD: Critical error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            $projects = collect([]);
            return view('leads.import-wizard', compact('projects'));
        }
    }

    /**
     * Analyze uploaded file and detect columns
     */
    public function analyzeImportFile(Request $request)
    {
        // DEEP DEBUG: Method entry with full context
        Log::info('📊 ANALYZE FILE: Method called', [
            'timestamp' => now(),
            'user_id' => Auth::id(),
            'user_type' => Auth::user()->type ?? 'N/A',
            'has_file' => $request->hasFile('file'),
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'request_headers' => $request->headers->all(),
            'request_data_keys' => array_keys($request->all()),
            'session_id' => session()->getId(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // DEEP DEBUG: Permission validation
        $hasPermission = Gate::check('import leads');
        Log::info('🔐 ANALYZE FILE: Permission check', [
            'has_import_permission' => $hasPermission,
            'user_id' => Auth::id(),
            'user_type' => Auth::user()->type
        ]);

        if (!$hasPermission) {
            Log::warning('🚫 ANALYZE FILE: Permission denied', [
                'user_id' => Auth::id(),
                'attempted_action' => 'file_analysis'
            ]);
            return response()->json(['success' => false, 'message' => __('Permission Denied.')], 403);
        }

        // DEEP DEBUG: Request validation
        try {
            Log::info('✅ ANALYZE FILE: Starting validation', [
                'validation_rules' => ['file' => 'required|file|mimes:csv,xlsx,xls|max:10240']
            ]);

            $request->validate([
                'file' => 'required|file|mimes:csv,xlsx,xls|max:10240'
            ]);

            Log::info('✅ ANALYZE FILE: Validation passed');
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('❌ ANALYZE FILE: Validation failed', [
                'errors' => $e->errors(),
                'input_keys' => array_keys($request->all()),
                'has_file' => $request->hasFile('file')
            ]);
            throw $e;
        }

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();

            // DEEP DEBUG: File analysis
            Log::info('📁 ANALYZE FILE: File details', [
                'original_name' => $file->getClientOriginalName(),
                'extension' => $extension,
                'size_bytes' => $file->getSize(),
                'size_mb' => round($file->getSize() / 1024 / 1024, 2),
                'mime_type' => $file->getMimeType(),
                'path' => $file->getPathname(),
                'is_valid' => $file->isValid(),
                'error' => $file->getError(),
                'client_original_extension' => $file->getClientOriginalExtension(),
                'guess_extension' => $file->guessExtension()
            ]);

            // Read first few rows to detect columns and get sample data
            if (in_array($extension, ['xlsx', 'xls'])) {
                Log::info('Analyzing Excel file');
                $data = $this->analyzeExcelFile($file);
            } else {
                Log::info('Analyzing CSV file');
                $data = $this->analyzeCsvFile($file);
            }

            Log::info('File analysis completed', [
                'columns_found' => count($data['columns']),
                'preview_rows' => count($data['preview']),
                'total_rows' => $data['total_rows']
            ]);

            return response()->json([
                'success' => true,
                'columns' => $data['columns'],
                'preview' => $data['preview'],
                'total_rows' => $data['total_rows']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error analyzing file: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute the import with column mappings
     */
    public function executeImport(Request $request)
    {
        Log::info('executeImport method called', [
            'user_id' => Auth::id(),
            'has_file' => $request->hasFile('file'),
            'mappings' => $request->input('mappings'),
            'project_id' => $request->input('project_id')
        ]);

        if (!Gate::check('import leads')) {
            Log::warning('Permission denied for import execution', ['user_id' => Auth::id()]);
            return response()->json(['success' => false, 'message' => __('Permission Denied.')], 403);
        }

        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx,xls|max:10240',
            'mappings' => 'required|json',
            'project_id' => 'nullable|exists:projects,id',
            'duplicate_handling' => 'required|in:skip,update',
            'auto_assign' => 'boolean',
            'validate_data' => 'boolean',
            'send_notifications' => 'boolean'
        ]);

        try {
            $mappings = json_decode($request->input('mappings'), true);

            Log::info('Import execution parameters', [
                'file_name' => $request->file('file')->getClientOriginalName(),
                'mappings_decoded' => $mappings,
                'project_id' => $request->input('project_id')
            ]);

            // Validate required mappings
            if (!isset($mappings['name']) || !isset($mappings['phone'])) {
                return response()->json([
                    'success' => false,
                    'message' => __('Name and Phone mappings are required')
                ], 400);
            }

            // Import settings
            $importSettings = [
                'skip_duplicates' => $request->input('duplicate_handling') === 'skip',
                'update_duplicates' => $request->input('duplicate_handling') === 'update',
                'auto_assign' => $request->boolean('auto_assign'),
                'validate_data' => $request->boolean('validate_data'),
                'send_notifications' => $request->boolean('send_notifications'),
                'column_mappings' => $mappings
            ];

            // Create custom import class with mappings
            $import = new MappedLeadsImport(
                Auth::user()->parent_id ?? Auth::user()->id,
                $request->input('project_id'),
                $importSettings
            );

            // Execute import
            Excel::import($import, $request->file('file'));

            return response()->json([
                'success' => true,
                'message' => __('Import completed successfully'),
                'results' => [
                    'imported' => $import->getImportedCount(),
                    'duplicates' => $import->getDuplicateCount(),
                    'errors' => $import->getErrorCount(),
                    'total' => $import->getTotalProcessed()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Import failed: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Analyze CSV file structure
     */
    private function analyzeCsvFile($file)
    {
        $handle = fopen($file->getPathname(), 'r');
        $headers = fgetcsv($handle);

        // Get sample data
        $sampleRows = [];
        for ($i = 0; $i < 10; $i++) {
            $row = fgetcsv($handle);
            if ($row === false) break;
            $sampleRows[] = $row;
        }

        $totalRows = 0;
        while (fgetcsv($handle) !== false) {
            $totalRows++;
        }
        $totalRows += count($sampleRows);

        fclose($handle);

        // Process columns
        $columns = [];
        foreach ($headers as $index => $header) {
            if (!empty($header)) {
                $sampleData = [];
                foreach ($sampleRows as $row) {
                    if (!empty($row[$index])) {
                        $sampleData[] = $row[$index];
                    }
                }

                $columns[] = [
                    'name' => trim($header),
                    'sample' => implode(', ', array_slice($sampleData, 0, 3))
                ];
            }
        }

        // Convert data for preview
        $preview = [];
        foreach ($sampleRows as $row) {
            $previewRow = [];
            foreach ($headers as $index => $header) {
                if (!empty($header)) {
                    $previewRow[trim($header)] = $row[$index] ?? '';
                }
            }
            $preview[] = $previewRow;
        }

        return [
            'columns' => $columns,
            'preview' => $preview,
            'total_rows' => $totalRows
        ];
    }

    /**
     * Analyze Excel file structure
     */
    private function analyzeExcelFile($file)
    {
        Log::info('Starting Excel file analysis', ['file_path' => $file->getPathname()]);

        try {
            $reader = IOFactory::createReader('Xlsx');
            $reader->setReadDataOnly(true);
            $reader->setReadEmptyCells(false);

            $spreadsheet = $reader->load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();

            $data = $worksheet->toArray(null, true, true, true);
            $headers = array_shift($data);

            Log::info('Excel file loaded successfully', [
                'headers' => $headers,
                'data_rows' => count($data)
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading Excel file', [
                'error' => $e->getMessage(),
                'file' => $file->getPathname()
            ]);
            throw $e;
        }

        // Clean headers and get sample data
        $columns = [];
        foreach ($headers as $key => $header) {
            if (!empty($header)) {
                $sampleData = [];
                for ($i = 0; $i < min(3, count($data)); $i++) {
                    if (!empty($data[$i][$key])) {
                        $sampleData[] = $data[$i][$key];
                    }
                }

                $columns[] = [
                    'name' => trim($header),
                    'sample' => implode(', ', $sampleData)
                ];
            }
        }

        // Convert data for preview
        $preview = [];
        for ($i = 0; $i < min(10, count($data)); $i++) {
            $row = [];
            foreach ($headers as $key => $header) {
                if (!empty($header)) {
                    $row[trim($header)] = $data[$i][$key] ?? '';
                }
            }
            $preview[] = $row;
        }

        return [
            'columns' => $columns,
            'preview' => $preview,
            'total_rows' => count($data)
        ];
    }

    /**
     * Get import templates for the current user
     */
    public function getImportTemplates(Request $request)
    {
        Log::info('📋 TEMPLATES: Get templates called', [
            'user_id' => Auth::id(),
            'user_type' => Auth::user()->type
        ]);

        if (!Gate::check('import leads')) {
            return response()->json(['success' => false, 'message' => __('Permission Denied.')], 403);
        }

        try {
            $tenant_id = Auth::user()->parent_id ?? Auth::user()->id;

            // For now, return empty array as we'll implement database storage later
            // This can be enhanced to store templates in database
            return response()->json([
                'success' => true,
                'templates' => []
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get import templates', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => __('Failed to load templates')
            ], 500);
        }
    }

    /**
     * Save import template
     */
    public function saveImportTemplate(Request $request)
    {
        Log::info('💾 TEMPLATES: Save template called', [
            'user_id' => Auth::id(),
            'template_name' => $request->input('name')
        ]);

        if (!Gate::check('import leads')) {
            return response()->json(['success' => false, 'message' => __('Permission Denied.')], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'mappings' => 'required|json',
            'excel_columns' => 'nullable|json'
        ]);

        try {
            $tenant_id = Auth::user()->parent_id ?? Auth::user()->id;

            // For now, just return success
            // This can be enhanced to store in database
            Log::info('💾 TEMPLATES: Template saved successfully', [
                'template_name' => $request->input('name'),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Template saved successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to save import template', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => __('Failed to save template')
            ], 500);
        }
    }

    /**
     * Delete import template
     */
    public function deleteImportTemplate(Request $request, $id)
    {
        Log::info('🗑️ TEMPLATES: Delete template called', [
            'user_id' => Auth::id(),
            'template_id' => $id
        ]);

        if (!Gate::check('import leads')) {
            return response()->json(['success' => false, 'message' => __('Permission Denied.')], 403);
        }

        try {
            // For now, just return success
            // This can be enhanced to delete from database
            Log::info('🗑️ TEMPLATES: Template deleted successfully', [
                'template_id' => $id,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Template deleted successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete import template', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => __('Failed to delete template')
            ], 500);
        }
    }
}
