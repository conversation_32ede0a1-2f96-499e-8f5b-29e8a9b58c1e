@extends('layouts.admin')

@section('page-title')
    {{ __('Task Dashboard') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Task Dashboard') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        @can('create tasks')
            <a href="#" data-url="{{ route('tasks.create') }}" data-size="lg" data-ajax-popup="true" 
               data-title="{{ __('Create New Task') }}" class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i> {{ __('Create Task') }}
            </a>
        @endcan
    </div>
@endsection

@section('content')
    <!-- Task Statistics -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Total Tasks') }}</h6>
                            <h3 class="text-primary">{{ $stats['total'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-primary-light ti ti-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('In Progress') }}</h6>
                            <h3 class="text-warning">{{ $stats['in_progress'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-warning-light ti ti-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Overdue') }}</h6>
                            <h3 class="text-danger">{{ $stats['overdue'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-danger-light ti ti-alert-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Completed') }}</h6>
                            <h3 class="text-success">{{ $stats['completed'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-success-light ti ti-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Due Today') }}</h6>
                            <h3 class="text-info">{{ $stats['due_today'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-info-light ti ti-calendar-event"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('High Priority') }}</h6>
                            <h3 class="text-dark">{{ $stats['high_priority'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-dark-light ti ti-flag"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Assigned to Me') }}</h6>
                            <h3 class="text-secondary">{{ $stats['assigned_to_me'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-secondary-light ti ti-user"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Created by Me') }}</h6>
                            <h3 class="text-success">{{ $stats['created_by_me'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-success-light ti ti-plus"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Overdue Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Overdue Tasks') }}</h5>
                </div>
                <div class="card-body">
                    @if($overdueTasks->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('Task') }}</th>
                                        <th>{{ __('Assigned To') }}</th>
                                        <th>{{ __('Due Date') }}</th>
                                        <th>{{ __('Action') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($overdueTasks as $task)
                                        <tr>
                                            <td>
                                                <strong>{{ $task->title }}</strong>
                                                <br><span class="badge bg-{{ $task->priority == 'urgent' ? 'dark' : ($task->priority == 'high' ? 'danger' : 'warning') }}">
                                                    {{ $task->priority_label }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($task->assignedTo)
                                                    {{ $task->assignedTo->name }}
                                                @else
                                                    <span class="text-muted">{{ __('Unassigned') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="text-danger">
                                                    {{ $task->due_date->format('M d, Y') }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('tasks.show', $task->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="ti ti-check-circle text-success" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2">{{ __('No overdue tasks!') }}</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Tasks Due Today -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Due Today') }}</h5>
                </div>
                <div class="card-body">
                    @if($todayTasks->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('Task') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Assigned To') }}</th>
                                        <th>{{ __('Action') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($todayTasks as $task)
                                        <tr>
                                            <td>
                                                <strong>{{ $task->title }}</strong>
                                                <br><span class="badge bg-{{ $task->priority == 'urgent' ? 'dark' : ($task->priority == 'high' ? 'danger' : 'warning') }}">
                                                    {{ $task->priority_label }}
                                                </span>
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'not_started' => 'secondary',
                                                        'in_progress' => 'warning',
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusColors[$task->status] ?? 'secondary' }}">
                                                    {{ $task->status_label }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($task->assignedTo)
                                                    {{ $task->assignedTo->name }}
                                                @else
                                                    <span class="text-muted">{{ __('Unassigned') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('tasks.show', $task->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="ti ti-calendar-check text-info" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2">{{ __('No tasks due today') }}</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5>{{ __('Recent Tasks') }}</h5>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('tasks.index') }}" class="btn btn-sm btn-outline-primary">
                                {{ __('View All') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($recentTasks->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('Task') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Created') }}</th>
                                        <th>{{ __('Action') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentTasks as $task)
                                        <tr>
                                            <td>
                                                <strong>{{ $task->title }}</strong>
                                                @if($task->lead)
                                                    <br><small class="text-primary">{{ __('Lead') }}: {{ $task->lead->name }}</small>
                                                @elseif($task->project)
                                                    <br><small class="text-info">{{ __('Project') }}: {{ $task->project->name }}</small>
                                                @elseif($task->property)
                                                    <br><small class="text-success">{{ __('Property') }}: {{ $task->property->title }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'not_started' => 'secondary',
                                                        'in_progress' => 'warning',
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusColors[$task->status] ?? 'secondary' }}">
                                                    {{ $task->status_label }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ $task->created_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                <a href="{{ route('tasks.show', $task->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="ti ti-clipboard-list text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2">{{ __('No recent tasks') }}</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Upcoming Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Upcoming Tasks (Next 7 Days)') }}</h5>
                </div>
                <div class="card-body">
                    @if($upcomingTasks->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('Task') }}</th>
                                        <th>{{ __('Due Date') }}</th>
                                        <th>{{ __('Assigned To') }}</th>
                                        <th>{{ __('Action') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($upcomingTasks as $task)
                                        <tr>
                                            <td>
                                                <strong>{{ $task->title }}</strong>
                                                <br><span class="badge bg-{{ $task->priority == 'urgent' ? 'dark' : ($task->priority == 'high' ? 'danger' : 'warning') }}">
                                                    {{ $task->priority_label }}
                                                </span>
                                            </td>
                                            <td>
                                                {{ $task->due_date->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $task->due_date->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                @if($task->assignedTo)
                                                    {{ $task->assignedTo->name }}
                                                @else
                                                    <span class="text-muted">{{ __('Unassigned') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('tasks.show', $task->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="ti ti-calendar-time text-info" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-2">{{ __('No upcoming tasks') }}</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
