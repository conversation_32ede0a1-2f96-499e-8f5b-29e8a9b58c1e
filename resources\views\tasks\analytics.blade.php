@extends('layouts.app')

@section('page-title')
    {{ __('Task Analytics') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('tasks.index') }}">{{ __('Tasks') }}</a></li>
    <li class="breadcrumb-item" aria-current="page">{{ __('Analytics') }}</li>
@endsection

@push('script-page')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusData = @json($analytics['tasks_by_status']);
    const statusLabels = statusData.map(item => item.status.replace('_', ' ').toUpperCase());
    const statusCounts = statusData.map(item => item.count);
    
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: statusCounts,
                backgroundColor: ['#17a2b8', '#ffc107', '#28a745', '#dc3545', '#6c757d'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Priority Chart
    const priorityCtx = document.getElementById('priorityChart').getContext('2d');
    const priorityData = @json($analytics['tasks_by_priority']);
    const priorityLabels = priorityData.map(item => item.priority.toUpperCase());
    const priorityCounts = priorityData.map(item => item.count);
    
    new Chart(priorityCtx, {
        type: 'bar',
        data: {
            labels: priorityLabels,
            datasets: [{
                label: 'Tasks',
                data: priorityCounts,
                backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#dc3545'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    const categoryData = @json($analytics['tasks_by_category']);
    const categoryLabels = categoryData.map(item => item.category.replace('_', ' ').toUpperCase());
    const categoryCounts = categoryData.map(item => item.count);
    
    new Chart(categoryCtx, {
        type: 'pie',
        data: {
            labels: categoryLabels,
            datasets: [{
                data: categoryCounts,
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endpush

@section('content')
<div class="row">
    <!-- Summary Cards -->
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avtar bg-light-primary">
                            <i class="ti ti-list f-24"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="mb-1">{{ __('Total Tasks') }}</p>
                        <h4 class="mb-0">{{ $analytics['total_tasks'] }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avtar bg-light-success">
                            <i class="ti ti-check f-24"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="mb-1">{{ __('Completed') }}</p>
                        <h4 class="mb-0">{{ $analytics['completed_tasks'] }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avtar bg-light-danger">
                            <i class="ti ti-alert-triangle f-24"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="mb-1">{{ __('Overdue') }}</p>
                        <h4 class="mb-0">{{ $analytics['overdue_tasks'] }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avtar bg-light-info">
                            <i class="ti ti-percentage f-24"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="mb-1">{{ __('Completion Rate') }}</p>
                        <h4 class="mb-0">{{ $analytics['completion_rate'] }}%</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Charts Row -->
    <div class="col-lg-4 col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>{{ __('Tasks by Status') }}</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>{{ __('Tasks by Priority') }}</h5>
            </div>
            <div class="card-body">
                <canvas id="priorityChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>{{ __('Tasks by Category') }}</h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Performance Metrics -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5>{{ __('Performance Metrics') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-primary">{{ $analytics['average_completion_time'] }}</h3>
                            <p class="mb-0">{{ __('Avg. Completion Time (Hours)') }}</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-success">{{ $analytics['completion_rate'] }}%</h3>
                            <p class="mb-0">{{ __('Success Rate') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5>{{ __('Quick Actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('tasks.index') }}" class="btn btn-outline-primary">
                        <i class="ti ti-list"></i> {{ __('View All Tasks') }}
                    </a>
                    <a href="{{ route('tasks.calendar') }}" class="btn btn-outline-info">
                        <i class="ti ti-calendar"></i> {{ __('Calendar View') }}
                    </a>
                    @can('create tasks')
                    <a href="#" class="btn btn-primary customModal" 
                       data-size="lg" 
                       data-url="{{ route('tasks.create') }}" 
                       data-title="{{ __('Create New Task') }}">
                        <i class="ti ti-plus"></i> {{ __('Create Task') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
