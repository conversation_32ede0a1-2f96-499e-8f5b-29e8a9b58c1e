<?php echo e(Form::open(['route' => 'documents.store', 'method' => 'POST', 'enctype' => 'multipart/form-data'])); ?>

<div class="modal-body">
    <div class="row">
        <!-- Basic Information -->
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('name', __('Document Name'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('name', null, ['class' => 'form-control', 'required' => true, 'placeholder' => __('Enter document name')])); ?>

            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('type', __('Document Type'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('type', \App\Models\Document::$types, null, ['class' => 'form-control', 'required' => true, 'placeholder' => __('Select document type')])); ?>

            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('category', __('Category'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('category', \App\Models\Document::$categories, null, ['class' => 'form-control', 'required' => true, 'placeholder' => __('Select category')])); ?>

            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('priority', __('Priority'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('priority', \App\Models\Document::$priorities, 'medium', ['class' => 'form-control', 'required' => true])); ?>

            </div>
        </div>
        <div class="col-md-12">
            <div class="form-group">
                <?php echo e(Form::label('description', __('Description'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('description', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter document description')])); ?>

            </div>
        </div>

        <!-- File Upload -->
        <div class="col-md-12">
            <div class="form-group">
                <?php echo e(Form::label('file', __('Upload File'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::file('file', ['class' => 'form-control', 'accept' => '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif'])); ?>

                <small class="text-muted"><?php echo e(__('Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, PNG, GIF. Max size: 50MB')); ?></small>
            </div>
        </div>

        <!-- Relationships -->
        <div class="col-md-4">
            <div class="form-group">
                <?php echo e(Form::label('project_id', __('Project'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('project_id', ['' => __('Select Project')] + $projects->pluck('name', 'id')->toArray(), $preselected['project_id'] ?? null, ['class' => 'form-control'])); ?>

            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <?php echo e(Form::label('lead_id', __('Lead'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('lead_id', ['' => __('Select Lead')] + $leads->pluck('name', 'id')->toArray(), $preselected['lead_id'] ?? null, ['class' => 'form-control'])); ?>

            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <?php echo e(Form::label('client_id', __('Client'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('client_id', ['' => __('Select Client')] + $clients->pluck('name', 'id')->toArray(), $preselected['client_id'] ?? null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <!-- Access Control -->
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('visibility', __('Visibility'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('visibility', \App\Models\Document::$visibilities, 'private', ['class' => 'form-control', 'required' => true])); ?>

            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('shared_with', __('Share With Users'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('shared_with[]', $users->pluck('name', 'id')->toArray(), null, ['class' => 'form-control', 'multiple' => true])); ?>

                <small class="text-muted"><?php echo e(__('Select users to share this document with (Hold Ctrl/Cmd to select multiple)')); ?></small>
            </div>
        </div>

        <!-- Signature Settings -->
        <div class="col-md-12">
            <div class="form-group">
                <div class="form-check">
                    <?php echo e(Form::checkbox('requires_signature', 1, false, ['class' => 'form-check-input', 'id' => 'requires_signature'])); ?>

                    <?php echo e(Form::label('requires_signature', __('Requires Signature'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>

        <div id="signature-settings" style="display: none;">
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo e(Form::label('signature_required_from', __('Signature Required From'), ['class' => 'form-label'])); ?>

                    <?php echo e(Form::select('signature_required_from[]', $users->pluck('name', 'id')->toArray(), null, ['class' => 'form-control', 'multiple' => true])); ?>

                    <small class="text-muted"><?php echo e(__('Hold Ctrl/Cmd to select multiple users')); ?></small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo e(Form::label('signature_deadline', __('Signature Deadline'), ['class' => 'form-label'])); ?>

                    <?php echo e(Form::date('signature_deadline', null, ['class' => 'form-control', 'min' => date('Y-m-d')])); ?>

                </div>
            </div>
        </div>

        <!-- Compliance Settings -->
        <div class="col-md-6">
            <div class="form-group">
                <div class="form-check">
                    <?php echo e(Form::checkbox('is_confidential', 1, false, ['class' => 'form-check-input', 'id' => 'is_confidential'])); ?>

                    <?php echo e(Form::label('is_confidential', __('Mark as Confidential'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <div class="form-check">
                    <?php echo e(Form::checkbox('is_compliance_document', 1, false, ['class' => 'form-check-input', 'id' => 'is_compliance_document'])); ?>

                    <?php echo e(Form::label('is_compliance_document', __('Compliance Document'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>

        <div id="compliance-settings" style="display: none;">
            <div class="col-md-12">
                <div class="form-group">
                    <?php echo e(Form::label('compliance_standard', __('Compliance Standard'), ['class' => 'form-label'])); ?>

                    <?php echo e(Form::text('compliance_standard', null, ['class' => 'form-control', 'placeholder' => __('e.g., ISO 9001, SOX, GDPR')])); ?>

                </div>
            </div>
        </div>

        <!-- Dates -->
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('review_date', __('Review Date'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::date('review_date', null, ['class' => 'form-control'])); ?>

                <small class="text-muted"><?php echo e(__('Date when this document should be reviewed')); ?></small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('expiry_date', __('Expiry Date'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::date('expiry_date', null, ['class' => 'form-control'])); ?>

                <small class="text-muted"><?php echo e(__('Date when this document expires')); ?></small>
            </div>
        </div>

        <!-- Tags -->
        <div class="col-md-12">
            <div class="form-group">
                <?php echo e(Form::label('tags', __('Tags'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('tags', null, ['class' => 'form-control', 'placeholder' => __('Enter tags separated by commas')])); ?>

                <small class="text-muted"><?php echo e(__('Add tags to help organize and find this document')); ?></small>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Create')); ?>" class="btn btn-primary">
</div>
<?php echo e(Form::close()); ?>


<?php $__env->startPush('css-page'); ?>
<style>
    .form-section {
        background: var(--bs-white);
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid var(--bs-border-color);
    }
    .form-section h6 {
        color: var(--bs-primary);
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--bs-primary);
    }
    .file-upload-area {
        border: 2px dashed var(--bs-border-color);
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }
    .file-upload-area:hover {
        border-color: var(--bs-primary);
        background: var(--bs-light);
    }
    .file-upload-area.dragover {
        border-color: var(--bs-success);
        background: var(--bs-success-bg-subtle);
    }
    .form-control:focus, .form-select:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
    .required-field::after {
        content: ' *';
        color: var(--bs-danger);
    }
    .form-check-input:checked {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
$(document).ready(function() {
    // Toggle signature settings
    document.getElementById('requires_signature').addEventListener('change', function() {
        const signatureSettings = document.getElementById('signature-settings');
        if (this.checked) {
            signatureSettings.style.display = 'block';
        } else {
            signatureSettings.style.display = 'none';
        }
    });

    // Toggle compliance settings
    document.getElementById('is_compliance_document').addEventListener('change', function() {
        const complianceSettings = document.getElementById('compliance-settings');
        if (this.checked) {
            complianceSettings.style.display = 'block';
        } else {
            complianceSettings.style.display = 'none';
        }
    });

    // Initialize Select2 for multi-select fields
    $('#shared_with, #signature_required_from').select2({
        placeholder: "<?php echo e(__('Select users')); ?>",
        allowClear: true
    });

    // Initialize form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $(this).find('[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            if (typeof notifier !== 'undefined' && typeof errorImg !== 'undefined') {
                notifier.show('Error!', 'Please fill in all required fields.', 'error', errorImg, 4000);
            }
        }
    });

    // File input change handler
    $('input[type="file"]').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        if (fileName) {
            $(this).siblings('.file-name').text(fileName);
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    console.log('Document create form loaded');
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/documents/create.blade.php ENDPATH**/ ?>