<?php

namespace App\Http\Controllers;
use App\Models\Contact;
use App\Models\Custom;
use App\Models\Expense;
use App\Models\FAQ;
use App\Models\HomePage;
use App\Models\InvoicePayment;
use App\Models\NoticeBoard;
use App\Models\PackageTransaction;
use App\Models\Page;
use App\Models\Subscription;
use App\Models\Support;
use App\Models\User;
use App\Models\Lead;
use App\Models\CallLog;
use App\Models\FollowUp;
use App\Models\Task;
use App\Models\Project;
use App\Models\Property;
use App\Models\Document;
use App\Models\CustomForm;
use App\Models\AgentPerformanceMetric;
use App\Models\LeadActivity;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        if (\Auth::check()) {
            if (\Auth::user()->type == 'super admin') {
                $result['totalOrganization'] = User::where('type', 'owner')->count();
                $result['totalSubscription'] = Subscription::count();
                $result['totalTransaction'] = PackageTransaction::count();
                $result['totalIncome'] = PackageTransaction::sum('amount');
                $result['totalNote'] = NoticeBoard::where('parent_id', parentId())->count();
                $result['totalContact'] = Contact::where('parent_id', parentId())->count();

                $result['organizationByMonth'] = $this->organizationByMonth();
                $result['paymentByMonth'] = $this->paymentByMonth();

                return view('dashboard.super_admin', compact('result'));
            } else {
                // Get comprehensive dashboard data
                $result = $this->getComprehensiveDashboardData();
                return view('dashboard.index', compact('result'));
            }
        } else {
            if (!file_exists(setup())) {
                header('location:install');
                die;
            } else {

                $landingPage=getSettingsValByName('landing_page');
                if($landingPage=='on'){
                    $subscriptions=Subscription::get();
                    $menus = Page::where('enabled',1)->get();
                    $FAQs = FAQ::where('enabled',1)->get();
                    return view('layouts.landing',compact('subscriptions', 'menus', 'FAQs'));
                }else{
                    return redirect()->route('login');
                }
            }

        }

    }

    public function organizationByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $organization = [];
        while ($currentdate <= $end) {
            $organization['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $organization['data'][] = User::where('type', 'owner')->whereMonth('created_at', $month)->whereYear('created_at', $year)->count();
            $currentdate = strtotime('+1 month', $currentdate);
        }


        return $organization;

    }

    public function paymentByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $payment = [];
        while ($currentdate <= $end) {
            $payment['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $payment['data'][] = PackageTransaction::whereMonth('created_at', $month)->whereYear('created_at', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $payment;

    }

    public function incomeByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $payment = [];
        while ($currentdate <= $end) {
            $payment['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $payment['income'][] = InvoicePayment::where('parent_id', parentId())->whereMonth('payment_date', $month)->whereYear('payment_date', $year)->sum('amount');
            $payment['expense'][] = Expense::where('parent_id', parentId())->whereMonth('date', $month)->whereYear('date', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $payment;

    }

    public function incomeExpense()
    {
        $startMonth = strtotime(date('Y-01'));
        $endMonth = strtotime(date('Y-12'));
        $currentdate = $startMonth;
        $report = [];
        while ($currentdate <= $endMonth) {
            $report['label'][] = date('M-Y', $currentdate);
            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $report['income'][] = InvoicePayment::where('parent_id', parentId())->whereMonth('payment_date', $month)->whereYear('payment_date', $year)->sum('amount');
            $report['expense'][] = Expense::where('parent_id', parentId())->whereMonth('date', $month)->whereYear('date', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $report;

    }

    /**
     * Get comprehensive dashboard data for all modules - ENHANCED VERSION
     */
    private function getComprehensiveDashboardData()
    {
        $tenantId = parentId();
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        $result = [];

        // Basic system stats - ENHANCED
        $result['totalUser'] = User::where('parent_id', $tenantId)->count();
        $result['totalClient'] = User::where('parent_id', $tenantId)->where('type', 'client')->count();
        $result['totalIncome'] = InvoicePayment::where('parent_id', $tenantId)->sum('amount');
        $result['totalExpense'] = Expense::where('parent_id', $tenantId)->sum('amount');
        $result['settings'] = settings();
        $result['incomeExpense'] = $this->incomeExpense();

        // Lead Management Stats - ENHANCED with more metrics
        $result['leads'] = [
            'total' => Lead::count(),
            'new_today' => Lead::whereDate('created_at', $today)->count(),
            'converted' => Lead::where('status', 'converted')->count(),
            'hot_leads' => Lead::where('temperature', 'hot')->count(),
            'contacted' => Lead::where('status', 'contacted')->count(),
            'interested' => Lead::where('status', 'interested')->count(),
            'callback_scheduled' => Lead::where('status', 'callback_scheduled')->count(),
            'site_visit_scheduled' => Lead::where('status', 'site_visit_scheduled')->count(),
            'lost' => Lead::where('status', 'lost')->count(),
            'new_this_week' => Lead::where('created_at', '>=', $thisWeek)->count(),
            'new_this_month' => Lead::where('created_at', '>=', $thisMonth)->count(),
            'conversion_rate' => $this->calculateLeadConversionRate(),
            'avg_response_time' => $this->calculateAverageResponseTime(),
        ];

        // Calling System Stats - ENHANCED
        $result['calling'] = [
            'total_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->count(),
            'calls_today' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->whereDate('call_started_at', $today)->count(),
            'connected_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('call_status', 'completed')->count(),
            'avg_call_duration' => round(CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->avg('duration_seconds') ?? 0),
            'failed_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('call_status', 'failed')->count(),
            'calls_this_week' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('call_started_at', '>=', $thisWeek)->count(),
            'success_rate' => $this->calculateCallSuccessRate($tenantId),
            'peak_calling_hours' => $this->getPeakCallingHours($tenantId),
        ];

        // Project Management Stats - ENHANCED
        $result['projects'] = [
            'total' => Project::count(),
            'active' => Project::where('status', 'active')->count(),
            'completed' => Project::where('status', 'completed')->count(),
            'on_hold' => Project::where('status', 'on_hold')->count(),
            'planning' => Project::where('status', 'planning')->count(),
            'overdue' => Project::where('end_date', '<', $today)->whereNotIn('status', ['completed', 'cancelled'])->count(),
            'completion_rate' => $this->calculateProjectCompletionRate(),
            'avg_project_duration' => $this->calculateAverageProjectDuration(),
        ];

        // Property Management Stats - ENHANCED
        $result['properties'] = [
            'total' => Property::count(),
            'available' => Property::where('status', 'available')->count(),
            'sold' => Property::where('status', 'sold')->count(),
            'reserved' => Property::where('status', 'reserved')->count(),
            'under_construction' => Property::where('status', 'under_construction')->count(),
            'sales_rate' => $this->calculatePropertySalesRate(),
            'avg_price' => Property::where('status', 'sold')->avg('base_price'),
            'inventory_turnover' => $this->calculateInventoryTurnover(),
        ];

        // Document Management Stats - ENHANCED
        $result['documents'] = [
            'total' => Document::count(),
            'recent' => Document::whereDate('created_at', '>=', $thisWeek)->count(),
            'shared' => Document::where('visibility', '!=', 'private')->count(),
            'by_type' => $this->getDocumentsByType(),
            'storage_usage' => $this->calculateStorageUsage(),
        ];

        // Follow-up Stats - ENHANCED
        $result['followups'] = [
            'total' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->count(),
            'pending' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'pending')->count(),
            'overdue' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'pending')->where('scheduled_at', '<', now())->count(),
            'completed_today' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'completed')->whereDate('updated_at', $today)->count(),
            'completion_rate' => $this->calculateFollowUpCompletionRate($tenantId),
            'avg_response_time' => $this->calculateFollowUpResponseTime($tenantId),
        ];

        // Agent Performance Stats - ENHANCED
        $result['agents'] = [
            'total' => User::where('parent_id', $tenantId)->where('type', 'agent')->count(),
            'active_today' => User::where('parent_id', $tenantId)->where('type', 'agent')
                ->whereHas('callLogs', function($q) use ($today) {
                    $q->whereDate('call_started_at', $today);
                })->count(),
            'top_performer' => $this->getTopPerformer($tenantId),
            'performance_metrics' => $this->getAgentPerformanceMetrics($tenantId),
            'productivity_score' => $this->calculateTeamProductivityScore($tenantId),
        ];

        // Recent Activities - Keep existing
        $result['recent_activities'] = $this->getRecentActivities($tenantId);

        // Task Management Stats - ENHANCED
        $user = Auth::user();
        if ($user->type == 'owner') {
            $taskBaseQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $taskBaseQuery = Task::query(); // Uses global scope
        }

        $result['tasks'] = [
            'total' => (clone $taskBaseQuery)->count(),
            'not_started' => (clone $taskBaseQuery)->where('status', 'not_started')->count(),
            'in_progress' => (clone $taskBaseQuery)->where('status', 'in_progress')->count(),
            'completed' => (clone $taskBaseQuery)->where('status', 'completed')->count(),
            'cancelled' => (clone $taskBaseQuery)->where('status', 'cancelled')->count(),
            'overdue' => (clone $taskBaseQuery)->where('due_date', '<', now())
                                              ->whereNotIn('status', ['completed', 'cancelled'])
                                              ->count(),
            'due_today' => (clone $taskBaseQuery)->whereDate('due_date', $today)
                                                ->whereNotIn('status', ['completed', 'cancelled'])
                                                ->count(),
            'due_this_week' => (clone $taskBaseQuery)->whereBetween('due_date', [$thisWeek, Carbon::now()->endOfWeek()])
                                                     ->whereNotIn('status', ['completed', 'cancelled'])
                                                     ->count(),
            'high_priority' => (clone $taskBaseQuery)->whereIn('priority', ['high', 'urgent'])
                                                     ->whereNotIn('status', ['completed', 'cancelled'])
                                                     ->count(),
            'assigned_to_me' => (clone $taskBaseQuery)->where('assigned_to', $user->id)->count(),
            'completion_rate' => $this->calculateTaskCompletionRate($tenantId),
            'productivity_trends' => $this->getTaskProductivityTrends($tenantId),
        ];

        // Chart Data - ENHANCED
        $result['charts'] = $this->getChartData($tenantId, $today, $thisWeek, $thisMonth);

        // NEW: Advanced Analytics
        $result['analytics'] = $this->getAdvancedAnalytics($tenantId, $today, $thisWeek, $thisMonth, $lastMonth);
        
        // NEW: Performance Insights
        $result['insights'] = $this->generatePerformanceInsights($result);
        
        // NEW: Trend Analysis
        $result['trends'] = $this->calculateTrendAnalysis($tenantId, $today, $lastMonth);

        return $result;
    }

    private function getTopPerformer($tenantId)
    {
        $topAgent = AgentPerformanceMetric::where('tenant_id', $tenantId)
            ->where('metric_date', '>=', Carbon::now()->startOfMonth())
            ->select('agent_id', DB::raw('SUM(leads_converted) as total_conversions'))
            ->groupBy('agent_id')
            ->orderBy('total_conversions', 'desc')
            ->with('agent')
            ->first();

        return $topAgent ? [
            'name' => $topAgent->agent->name,
            'conversions' => $topAgent->total_conversions
        ] : null;
    }

    private function getRecentActivities($tenantId)
    {
        $activities = [];

        // Recent leads
        $recentLeads = Lead::whereHas('assignedTo', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->with(['assignedTo:id,name', 'createdBy:id,name'])
          ->orderBy('created_at', 'desc')
          ->limit(5)
          ->get();

        foreach ($recentLeads as $lead) {
            $activities[] = [
                'type' => 'lead',
                'title' => 'New Lead: ' . $lead->name,
                'description' => 'Assigned to ' . ($lead->assignedTo->name ?? 'Unassigned'),
                'time' => $lead->created_at,
                'icon' => 'ti-user-plus',
                'color' => 'primary'
            ];
        }

        // Recent calls
        $recentCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->with(['user:id,name', 'lead:id,name'])
          ->orderBy('call_started_at', 'desc')
          ->limit(5)
          ->get();

        foreach ($recentCalls as $call) {
            $activities[] = [
                'type' => 'call',
                'title' => 'Call: ' . ($call->lead->name ?? $call->contact_name),
                'description' => 'By ' . $call->user->name . ' - ' . ucfirst($call->call_outcome),
                'time' => $call->call_started_at,
                'icon' => 'ti-phone',
                'color' => $call->call_outcome === 'connected' ? 'success' : 'warning'
            ];
        }

        // Recent follow-ups
        $recentFollowUps = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->with(['lead:id,name', 'agent:id,name'])
          ->where('status', 'completed')
          ->orderBy('updated_at', 'desc')
          ->limit(5)
          ->get();

        foreach ($recentFollowUps as $followUp) {
            $activities[] = [
                'type' => 'followup',
                'title' => 'Follow-up: ' . $followUp->title,
                'description' => 'For ' . $followUp->lead->name . ' by ' . $followUp->agent->name,
                'time' => $followUp->updated_at,
                'icon' => 'ti-calendar-check',
                'color' => 'info'
            ];
        }

        // Sort by time and return top 10
        usort($activities, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, 10);
    }

    private function getChartData($tenantId, $today, $thisWeek, $thisMonth)
    {
        // Lead conversion funnel
        $leadFunnel = [
            'new' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'new')->count(),
            'contacted' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'contacted')->count(),
            'qualified' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'qualified')->count(),
            'proposal' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'proposal')->count(),
            'converted' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'converted')->count(),
        ];

        // Call performance trends (last 7 days)
        $callTrends = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $callTrends[] = [
                'date' => $date->format('M d'),
                'calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->whereDate('call_started_at', $date)->count(),
                'connected' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->whereDate('call_started_at', $date)->where('call_outcome', 'connected')->count(),
            ];
        }

        // Agent performance comparison
        $agentPerformance = User::where('parent_id', $tenantId)
            ->where('type', 'agent')
            ->withCount([
                'callLogs as total_calls' => function($q) use ($thisMonth) {
                    $q->where('call_started_at', '>=', $thisMonth);
                },
                'callLogs as connected_calls' => function($q) use ($thisMonth) {
                    $q->where('call_started_at', '>=', $thisMonth)
                      ->where('call_outcome', 'connected');
                }
            ])
            ->limit(10)
            ->get()
            ->map(function($agent) {
                return [
                    'name' => $agent->name,
                    'total_calls' => $agent->total_calls,
                    'connected_calls' => $agent->connected_calls,
                    'conversion_rate' => $agent->total_calls > 0 ? round(($agent->connected_calls / $agent->total_calls) * 100, 1) : 0
                ];
            });

        return [
            'lead_funnel' => $leadFunnel,
            'call_trends' => $callTrends,
            'agent_performance' => $agentPerformance
        ];
    }

    // NEW ENHANCED METHODS BELOW

    /**
     * Calculate lead conversion rate
     */
    private function calculateLeadConversionRate()
    {
        $totalLeads = Lead::count();
        $convertedLeads = Lead::where('status', 'converted')->count();
        
        return $totalLeads > 0 ? round(($convertedLeads / $totalLeads) * 100, 2) : 0;
    }

    /**
     * Calculate average response time for leads
     */
    private function calculateAverageResponseTime()
    {
        // Check if the column exists, if not return 0 or use alternative method
        try {
            $avgResponseTime = Lead::whereNotNull('first_contacted_at')
                ->whereNotNull('created_at')
                ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, first_contacted_at)) as avg_hours')
                ->first();
            
            return $avgResponseTime ? round($avgResponseTime->avg_hours, 1) : 0;
        } catch (\Exception $e) {
            // If first_contacted_at doesn't exist, calculate using created_at and updated_at as fallback
            try {
                $avgResponseTime = Lead::where('status', '!=', 'new')
                    ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, updated_at)) as avg_hours')
                    ->first();
                
                return $avgResponseTime ? round($avgResponseTime->avg_hours, 1) : 0;
            } catch (\Exception $e2) {
                return 0; // Return 0 if any error occurs
            }
        }
    }

    /**
     * Calculate call success rate
     */
    private function calculateCallSuccessRate($tenantId)
    {
        $totalCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->count();
        
        $successfulCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->where('call_status', 'completed')->count();
        
        return $totalCalls > 0 ? round(($successfulCalls / $totalCalls) * 100, 2) : 0;
    }

    /**
     * Get peak calling hours
     */
    private function getPeakCallingHours($tenantId)
    {
        return CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->select(DB::raw('HOUR(call_started_at) as hour'), DB::raw('COUNT(*) as call_count'))
          ->groupBy('hour')
          ->orderBy('call_count', 'desc')
          ->limit(3)
          ->pluck('hour')
          ->toArray();
    }

    /**
     * Calculate project completion rate
     */
    private function calculateProjectCompletionRate()
    {
        $totalProjects = Project::count();
        $completedProjects = Project::where('status', 'completed')->count();
        
        return $totalProjects > 0 ? round(($completedProjects / $totalProjects) * 100, 2) : 0;
    }

    /**
     * Calculate average project duration
     */
    private function calculateAverageProjectDuration()
    {
        try {
            $avgDuration = Project::where('status', 'completed')
                ->whereNotNull('start_date')
                ->whereNotNull('end_date')
                ->selectRaw('AVG(DATEDIFF(end_date, start_date)) as avg_days')
                ->first();
            
            return $avgDuration ? round($avgDuration->avg_days, 1) : 0;
        } catch (\Exception $e) {
            // If start_date/end_date columns don't exist, use created_at and updated_at
            try {
                $avgDuration = Project::where('status', 'completed')
                    ->selectRaw('AVG(DATEDIFF(updated_at, created_at)) as avg_days')
                    ->first();
                
                return $avgDuration ? round($avgDuration->avg_days, 1) : 0;
            } catch (\Exception $e2) {
                return 0;
            }
        }
    }

    /**
     * Calculate property sales rate
     */
    private function calculatePropertySalesRate()
    {
        try {
            $totalProperties = Property::count();
            $soldProperties = Property::where('status', 'sold')->count();
            
            return $totalProperties > 0 ? round(($soldProperties / $totalProperties) * 100, 2) : 0;
        } catch (\Exception $e) {
            // If Property model doesn't exist or has issues, return 0
            return 0;
        }
    }

    /**
     * Calculate inventory turnover
     */
    private function calculateInventoryTurnover()
    {
        try {
            $soldThisYear = Property::where('status', 'sold')
                ->whereYear('updated_at', Carbon::now()->year)
                ->count();
            $avgInventory = Property::count();
            
            return $avgInventory > 0 ? round($soldThisYear / $avgInventory, 2) : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get documents by type
     */
    private function getDocumentsByType()
    {
        return Document::select('type', DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();
    }

    /**
     * Calculate storage usage
     */
    private function calculateStorageUsage()
    {
        try {
            $totalSize = Document::sum('file_size') ?: 0;
            return round($totalSize / (1024 * 1024), 2); // Convert to MB
        } catch (\Exception $e) {
            // If file_size column doesn't exist, return 0
            return 0;
        }
    }

    /**
     * Calculate follow-up completion rate
     */
    private function calculateFollowUpCompletionRate($tenantId)
    {
        $totalFollowUps = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->count();
        
        $completedFollowUps = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->where('status', 'completed')->count();
        
        return $totalFollowUps > 0 ? round(($completedFollowUps / $totalFollowUps) * 100, 2) : 0;
    }

    /**
     * Calculate follow-up response time
     */
    private function calculateFollowUpResponseTime($tenantId)
    {
        try {
            $avgResponseTime = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'completed')
              ->whereNotNull('completed_at')
              ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, scheduled_at, completed_at)) as avg_hours')
              ->first();
            
            return $avgResponseTime ? round($avgResponseTime->avg_hours, 1) : 0;
        } catch (\Exception $e) {
            // Fallback: Use updated_at as completion time
            try {
                $avgResponseTime = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->where('status', 'completed')
                  ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, scheduled_at, updated_at)) as avg_hours')
                  ->first();
                
                return $avgResponseTime ? round($avgResponseTime->avg_hours, 1) : 0;
            } catch (\Exception $e2) {
                return 0;
            }
        }
    }

    /**
     * Get agent performance metrics
     */
    private function getAgentPerformanceMetrics($tenantId)
    {
        return User::where('parent_id', $tenantId)
            ->where('type', 'agent')
            ->with(['callLogs' => function($q) {
                $q->where('call_started_at', '>=', Carbon::now()->startOfMonth());
            }])
            ->get()
            ->map(function($agent) {
                $calls = $agent->callLogs;
                $totalCalls = $calls->count();
                $connectedCalls = $calls->where('call_outcome', 'connected')->count();
                
                return [
                    'agent_id' => $agent->id,
                    'name' => $agent->name,
                    'total_calls' => $totalCalls,
                    'connected_calls' => $connectedCalls,
                    'success_rate' => $totalCalls > 0 ? round(($connectedCalls / $totalCalls) * 100, 1) : 0,
                    'avg_call_duration' => $calls->avg('duration_seconds') ?? 0,
                    'leads_assigned' => Lead::where('assigned_to', $agent->id)->count(),
                    'leads_converted' => Lead::where('assigned_to', $agent->id)->where('status', 'converted')->count(),
                ];
            })->toArray();
    }

    /**
     * Calculate team productivity score
     */
    private function calculateTeamProductivityScore($tenantId)
    {
        $metrics = $this->getAgentPerformanceMetrics($tenantId);
        
        if (empty($metrics)) {
            return 0;
        }

        $totalScore = 0;
        $agentCount = count($metrics);

        foreach ($metrics as $metric) {
            $callScore = min($metric['success_rate'] / 100, 1) * 30; // Max 30 points
            $conversionScore = $metric['leads_assigned'] > 0 ? 
                min(($metric['leads_converted'] / $metric['leads_assigned']) * 100 / 20, 1) * 40 : 0; // Max 40 points
            $activityScore = min($metric['total_calls'] / 50, 1) * 30; // Max 30 points for 50+ calls
            
            $totalScore += $callScore + $conversionScore + $activityScore;
        }

        return round($totalScore / $agentCount, 1);
    }

    /**
     * Calculate task completion rate
     */
    private function calculateTaskCompletionRate($tenantId)
    {
        $user = Auth::user();
        
        if ($user->type == 'owner') {
            $taskQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $taskQuery = Task::query();
        }

        $totalTasks = $taskQuery->count();
        $completedTasks = (clone $taskQuery)->where('status', 'completed')->count();
        
        return $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;
    }

    /**
     * Get task productivity trends
     */
    private function getTaskProductivityTrends($tenantId)
    {
        $trends = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $user = Auth::user();
            
            if ($user->type == 'owner') {
                $taskQuery = Task::withoutGlobalScope('tenant')
                    ->where(function($q) use ($user, $tenantId) {
                        $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                            $subQ->where(function($query) use ($tenantId) {
                                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                            });
                        });
                    });
            } else {
                $taskQuery = Task::query();
            }
            
            $completed = (clone $taskQuery)->where('status', 'completed')
                ->whereDate('updated_at', $date)->count();
                
            $created = (clone $taskQuery)->whereDate('created_at', $date)->count();
            
            $trends[] = [
                'date' => $date->format('M d'),
                'completed' => $completed,
                'created' => $created
            ];
        }
        
        return $trends;
    }

    /**
     * Get advanced analytics
     */
    private function getAdvancedAnalytics($tenantId, $today, $thisWeek, $thisMonth, $lastMonth)
    {
        return [
            'revenue_analytics' => [
                'monthly_growth' => $this->calculateMonthlyRevenueGrowth($tenantId, $thisMonth, $lastMonth),
                'profit_margin' => $this->calculateProfitMargin($tenantId),
                'revenue_per_client' => $this->calculateRevenuePerClient($tenantId),
                'revenue_forecast' => $this->forecastRevenue($tenantId),
            ],
            'lead_analytics' => [
                'source_performance' => $this->getLeadSourcePerformance(),
                'conversion_funnel' => $this->getDetailedConversionFunnel(),
                'lead_scoring' => $this->getLeadScoringMetrics(),
                'seasonal_trends' => $this->getSeasonalLeadTrends(),
            ],
            'operational_analytics' => [
                'resource_utilization' => $this->calculateResourceUtilization($tenantId),
                'efficiency_metrics' => $this->getEfficiencyMetrics($tenantId),
                'bottleneck_analysis' => $this->identifyBottlenecks($tenantId),
                'capacity_planning' => $this->getCapacityMetrics($tenantId),
            ],
            'performance_analytics' => [
                'team_performance' => $this->getTeamPerformanceMetrics($tenantId),
                'individual_performance' => $this->getIndividualPerformanceMetrics($tenantId),
                'performance_trends' => $this->getPerformanceTrends($tenantId),
                'benchmark_comparison' => $this->getBenchmarkComparison($tenantId),
            ]
        ];
    }

    /**
     * Generate performance insights
     */
    private function generatePerformanceInsights($result)
    {
        $insights = [];

        // Lead conversion insights
        if (isset($result['leads']['conversion_rate'])) {
            if ($result['leads']['conversion_rate'] > 15) {
                $insights[] = [
                    'type' => 'success',
                    'category' => 'leads',
                    'title' => 'Excellent Lead Conversion',
                    'message' => "Your conversion rate of {$result['leads']['conversion_rate']}% is above industry average!",
                    'recommendation' => 'Continue current lead nurturing strategies and consider scaling up.',
                    'priority' => 'low'
                ];
            } elseif ($result['leads']['conversion_rate'] < 5) {
                $insights[] = [
                    'type' => 'warning',
                    'category' => 'leads',
                    'title' => 'Low Conversion Rate',
                    'message' => "Your conversion rate of {$result['leads']['conversion_rate']}% needs improvement.",
                    'recommendation' => 'Review lead qualification process and follow-up strategies.',
                    'priority' => 'high'
                ];
            }
        }

        // Task management insights
        if (isset($result['tasks']['overdue']) && $result['tasks']['total'] > 0) {
            $overduePercentage = round(($result['tasks']['overdue'] / $result['tasks']['total']) * 100, 1);
            
            if ($overduePercentage > 20) {
                $insights[] = [
                    'type' => 'danger',
                    'category' => 'tasks',
                    'title' => 'High Overdue Tasks',
                    'message' => "{$overduePercentage}% of tasks are overdue.",
                    'recommendation' => 'Review workload distribution and adjust deadlines. Consider additional resources.',
                    'priority' => 'high'
                ];
            }
        }

        // Financial insights
        $revenue = $result['totalIncome'] ?? 0;
        $expenses = $result['totalExpense'] ?? 0;
        
        if ($revenue > 0) {
            $profitMargin = round((($revenue - $expenses) / $revenue) * 100, 1);
            
            if ($profitMargin > 20) {
                $insights[] = [
                    'type' => 'success',
                    'category' => 'financial',
                    'title' => 'Healthy Profit Margin',
                    'message' => "Profit margin of {$profitMargin}% indicates strong financial health.",
                    'recommendation' => 'Consider reinvestment opportunities for growth or expansion.',
                    'priority' => 'low'
                ];
            } elseif ($profitMargin < 5) {
                $insights[] = [
                    'type' => 'warning',
                    'category' => 'financial',
                    'title' => 'Low Profit Margin',
                    'message' => "Profit margin of {$profitMargin}% requires attention.",
                    'recommendation' => 'Review pricing strategy and identify cost optimization opportunities.',
                    'priority' => 'medium'
                ];
            }
        }

        // Calling system insights
        if (isset($result['calling']['success_rate'])) {
            if ($result['calling']['success_rate'] < 30) {
                $insights[] = [
                    'type' => 'warning',
                    'category' => 'calling',
                    'title' => 'Low Call Success Rate',
                    'message' => "Call success rate of {$result['calling']['success_rate']}% is below optimal.",
                    'recommendation' => 'Provide additional training on call techniques and lead qualification.',
                    'priority' => 'medium'
                ];
            }
        }

        // Agent performance insights
        if (isset($result['agents']['productivity_score'])) {
            if ($result['agents']['productivity_score'] < 50) {
                $insights[] = [
                    'type' => 'info',
                    'category' => 'team',
                    'title' => 'Team Productivity Opportunity',
                    'message' => "Team productivity score is {$result['agents']['productivity_score']}/100.",
                    'recommendation' => 'Consider team training sessions and performance improvement programs.',
                    'priority' => 'medium'
                ];
            }
        }

        return $insights;
    }

    /**
     * Calculate trend analysis
     */
    private function calculateTrendAnalysis($tenantId, $today, $lastMonth)
    {
        $trends = [];

        // Lead trends
        $currentMonthLeads = Lead::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
        $lastMonthLeads = Lead::whereBetween('created_at', [$lastMonth, $lastMonth->copy()->endOfMonth()])->count();
        $trends['leads_growth'] = $this->calculateGrowthPercentage($currentMonthLeads, $lastMonthLeads);

        // Revenue trends
        $currentMonthRevenue = InvoicePayment::where('parent_id', $tenantId)
            ->where('payment_date', '>=', Carbon::now()->startOfMonth())->sum('amount');
        $lastMonthRevenue = InvoicePayment::where('parent_id', $tenantId)
            ->whereBetween('payment_date', [$lastMonth, $lastMonth->copy()->endOfMonth()])->sum('amount');
        $trends['revenue_growth'] = $this->calculateGrowthPercentage($currentMonthRevenue, $lastMonthRevenue);

        // Call trends
        $currentMonthCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->where('call_started_at', '>=', Carbon::now()->startOfMonth())->count();
        
        $lastMonthCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->whereBetween('call_started_at', [$lastMonth, $lastMonth->copy()->endOfMonth()])->count();
        
        $trends['calls_growth'] = $this->calculateGrowthPercentage($currentMonthCalls, $lastMonthCalls);

        // Task completion trends
        $user = Auth::user();
        if ($user->type == 'owner') {
            $taskQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $taskQuery = Task::query();
        }

        $currentMonthTasks = (clone $taskQuery)->where('status', 'completed')
            ->where('updated_at', '>=', Carbon::now()->startOfMonth())->count();
        $lastMonthTasks = (clone $taskQuery)->where('status', 'completed')
            ->whereBetween('updated_at', [$lastMonth, $lastMonth->copy()->endOfMonth()])->count();
        $trends['task_completion_growth'] = $this->calculateGrowthPercentage($currentMonthTasks, $lastMonthTasks);

        return $trends;
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 2);
    }

    /**
     * Calculate monthly revenue growth
     */
    private function calculateMonthlyRevenueGrowth($tenantId, $thisMonth, $lastMonth)
    {
        $currentRevenue = InvoicePayment::where('parent_id', $tenantId)
            ->where('payment_date', '>=', $thisMonth)->sum('amount');
        
        $previousRevenue = InvoicePayment::where('parent_id', $tenantId)
            ->whereBetween('payment_date', [$lastMonth, $lastMonth->copy()->endOfMonth()])->sum('amount');
        
        return $this->calculateGrowthPercentage($currentRevenue, $previousRevenue);
    }

    /**
     * Calculate profit margin
     */
    private function calculateProfitMargin($tenantId)
    {
        $totalIncome = InvoicePayment::where('parent_id', $tenantId)->sum('amount');
        $totalExpense = Expense::where('parent_id', $tenantId)->sum('amount');
        
        return $totalIncome > 0 ? round((($totalIncome - $totalExpense) / $totalIncome) * 100, 2) : 0;
    }

    /**
     * Calculate revenue per client
     */
    private function calculateRevenuePerClient($tenantId)
    {
        $totalRevenue = InvoicePayment::where('parent_id', $tenantId)->sum('amount');
        $totalClients = User::where('parent_id', $tenantId)->where('type', 'client')->count();
        
        return $totalClients > 0 ? round($totalRevenue / $totalClients, 2) : 0;
    }

    /**
     * Forecast revenue (simple linear projection)
     */
    private function forecastRevenue($tenantId)
    {
        $monthlyRevenues = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i)->startOfMonth();
            $revenue = InvoicePayment::where('parent_id', $tenantId)
                ->whereBetween('payment_date', [$date, $date->copy()->endOfMonth()])
                ->sum('amount');
            $monthlyRevenues[] = $revenue;
        }
        
        if (count($monthlyRevenues) >= 3) {
            $avgGrowth = 0;
            for ($i = 1; $i < count($monthlyRevenues); $i++) {
                if ($monthlyRevenues[$i-1] > 0) {
                    $avgGrowth += ($monthlyRevenues[$i] - $monthlyRevenues[$i-1]) / $monthlyRevenues[$i-1];
                }
            }
            $avgGrowth = $avgGrowth / (count($monthlyRevenues) - 1);
            
            $lastRevenue = end($monthlyRevenues);
            return round($lastRevenue * (1 + $avgGrowth), 2);
        }
        
        return 0;
    }

    /**
     * Get lead source performance
     */
    private function getLeadSourcePerformance()
    {
        return Lead::select('source', 
                DB::raw('COUNT(*) as total_leads'),
                DB::raw('SUM(CASE WHEN status = "converted" THEN 1 ELSE 0 END) as converted_leads'),
                DB::raw('ROUND(AVG(CASE WHEN status = "converted" THEN 1 ELSE 0 END) * 100, 2) as conversion_rate')
            )
            ->groupBy('source')
            ->orderBy('conversion_rate', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Get detailed conversion funnel
     */
    private function getDetailedConversionFunnel()
    {
        return [
            'total_leads' => Lead::count(),
            'contacted' => Lead::where('status', 'contacted')->count(),
            'interested' => Lead::where('status', 'interested')->count(),
            'qualified' => Lead::where('status', 'qualified')->count(),
            'proposal_sent' => Lead::where('status', 'proposal')->count(),
            'negotiation' => Lead::where('status', 'negotiation')->count(),
            'converted' => Lead::where('status', 'converted')->count(),
            'lost' => Lead::where('status', 'lost')->count(),
        ];
    }

    /**
     * Get lead scoring metrics
     */
    private function getLeadScoringMetrics()
    {
        try {
            return [
                'hot_leads' => Lead::where('temperature', 'hot')->count(),
                'warm_leads' => Lead::where('temperature', 'warm')->count(),
                'cold_leads' => Lead::where('temperature', 'cold')->count(),
                'avg_score' => Lead::avg('score') ?? 0,
                'high_score_conversion' => Lead::where('score', '>', 80)->where('status', 'converted')->count(),
            ];
        } catch (\Exception $e) {
            // If temperature or score columns don't exist, return default values
            return [
                'hot_leads' => Lead::where('priority', 'high')->count(),
                'warm_leads' => Lead::where('priority', 'medium')->count(),
                'cold_leads' => Lead::where('priority', 'low')->count(),
                'avg_score' => 0,
                'high_score_conversion' => 0,
            ];
        }
    }

    /**
     * Get seasonal lead trends (simplified)
     */
    private function getSeasonalLeadTrends()
    {
        $trends = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $trends[] = [
                'month' => $date->format('M Y'),
                'leads' => Lead::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count()
            ];
        }
        
        return $trends;
    }

    /**
     * Calculate resource utilization
     */
    private function calculateResourceUtilization($tenantId)
    {
        $totalAgents = User::where('parent_id', $tenantId)->where('type', 'agent')->count();
        $activeAgents = User::where('parent_id', $tenantId)->where('type', 'agent')
            ->whereHas('callLogs', function($q) {
                $q->where('call_started_at', '>=', Carbon::now()->startOfWeek());
            })->count();
        
        return $totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100, 2) : 0;
    }

    /**
     * Get efficiency metrics
     */
    private function getEfficiencyMetrics($tenantId)
    {
        return [
            'calls_per_lead' => $this->calculateCallsPerLead($tenantId),
            'avg_deal_cycle' => $this->calculateAverageDealCycle(),
            'follow_up_efficiency' => $this->calculateFollowUpEfficiency($tenantId),
            'response_time_efficiency' => $this->calculateResponseTimeEfficiency(),
        ];
    }

    /**
     * Calculate calls per lead
     */
    private function calculateCallsPerLead($tenantId)
    {
        $totalCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->count();
        
        $totalLeads = Lead::count();
        
        return $totalLeads > 0 ? round($totalCalls / $totalLeads, 2) : 0;
    }

    /**
     * Calculate average deal cycle
     */
    private function calculateAverageDealCycle()
    {
        try {
            // First try with converted_at column
            $avgCycle = Lead::where('status', 'converted')
                ->whereNotNull('converted_at')
                ->selectRaw('AVG(DATEDIFF(converted_at, created_at)) as avg_days')
                ->first();
            
            if ($avgCycle && $avgCycle->avg_days) {
                return round($avgCycle->avg_days, 1);
            }
        } catch (\Exception $e) {
            // If converted_at doesn't exist, use updated_at as fallback
            try {
                $avgCycle = Lead::where('status', 'converted')
                    ->selectRaw('AVG(DATEDIFF(updated_at, created_at)) as avg_days')
                    ->first();
                
                return $avgCycle ? round($avgCycle->avg_days, 1) : 0;
            } catch (\Exception $e2) {
                return 0;
            }
        }
        
        return 0;
    }

    /**
     * Calculate follow-up efficiency
     */
    private function calculateFollowUpEfficiency($tenantId)
    {
        try {
            $completedOnTime = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'completed')
              ->whereRaw('completed_at <= scheduled_at')
              ->count();
            
            $totalCompleted = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'completed')->count();
            
            return $totalCompleted > 0 ? round(($completedOnTime / $totalCompleted) * 100, 2) : 0;
        } catch (\Exception $e) {
            // Fallback: Calculate based on updated_at vs scheduled_at
            try {
                $completedOnTime = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->where('status', 'completed')
                  ->whereRaw('updated_at <= scheduled_at')
                  ->count();
                
                $totalCompleted = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->where('status', 'completed')->count();
                
                return $totalCompleted > 0 ? round(($completedOnTime / $totalCompleted) * 100, 2) : 0;
            } catch (\Exception $e2) {
                return 0;
            }
        }
    }

    /**
     * Calculate response time efficiency
     */
    private function calculateResponseTimeEfficiency()
    {
        try {
            // Try with first_contacted_at column
            $quickResponses = Lead::whereNotNull('first_contacted_at')
                ->whereRaw('TIMESTAMPDIFF(HOUR, created_at, first_contacted_at) <= 24')
                ->count();
            
            $totalResponded = Lead::whereNotNull('first_contacted_at')->count();
            
            if ($totalResponded > 0) {
                return round(($quickResponses / $totalResponded) * 100, 2);
            }
        } catch (\Exception $e) {
            // Fallback: Use leads that were updated within 24 hours and status changed
            try {
                $quickResponses = Lead::where('status', '!=', 'new')
                    ->whereRaw('TIMESTAMPDIFF(HOUR, created_at, updated_at) <= 24')
                    ->count();
                
                $totalResponded = Lead::where('status', '!=', 'new')->count();
                
                return $totalResponded > 0 ? round(($quickResponses / $totalResponded) * 100, 2) : 0;
            } catch (\Exception $e2) {
                return 0;
            }
        }
        
        return 0;
    }

    /**
     * Identify bottlenecks
     */
    private function identifyBottlenecks($tenantId)
    {
        $bottlenecks = [];
        
        // Lead conversion bottleneck
        $conversionRate = $this->calculateLeadConversionRate();
        if ($conversionRate < 10) {
            $bottlenecks[] = [
                'area' => 'Lead Conversion',
                'severity' => 'high',
                'description' => 'Low conversion rate indicates issues in lead qualification or follow-up process'
            ];
        }
        
        // Call efficiency bottleneck
        $callSuccessRate = $this->calculateCallSuccessRate($tenantId);
        if ($callSuccessRate < 40) {
            $bottlenecks[] = [
                'area' => 'Call Efficiency',
                'severity' => 'medium',
                'description' => 'Low call success rate suggests need for better calling strategies'
            ];
        }
        
        // Task management bottleneck
        $user = Auth::user();
        if ($user->type == 'owner') {
            $taskQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $taskQuery = Task::query();
        }
        
        $overduePercentage = $taskQuery->count() > 0 ? 
            ($taskQuery->where('due_date', '<', now())->whereNotIn('status', ['completed', 'cancelled'])->count() / $taskQuery->count()) * 100 : 0;
            
        if ($overduePercentage > 25) {
            $bottlenecks[] = [
                'area' => 'Task Management',
                'severity' => 'high',
                'description' => 'High percentage of overdue tasks indicates workload or planning issues'
            ];
        }
        
        return $bottlenecks;
    }

    /**
     * Get capacity metrics
     */
    private function getCapacityMetrics($tenantId)
    {
        return [
            'agent_utilization' => $this->calculateResourceUtilization($tenantId),
            'current_workload' => $this->getCurrentWorkload($tenantId),
            'capacity_forecast' => $this->forecastCapacityNeeds($tenantId),
            'optimal_team_size' => $this->calculateOptimalTeamSize($tenantId),
        ];
    }

    /**
     * Get current workload
     */
    private function getCurrentWorkload($tenantId)
    {
        $totalAgents = User::where('parent_id', $tenantId)->where('type', 'agent')->count();
        $totalLeads = Lead::where('status', '!=', 'converted')->where('status', '!=', 'lost')->count();
        
        return $totalAgents > 0 ? round($totalLeads / $totalAgents, 1) : 0;
    }

    /**
     * Forecast capacity needs (simplified)
     */
    private function forecastCapacityNeeds($tenantId)
    {
        $currentWorkload = $this->getCurrentWorkload($tenantId);
        $leadGrowthRate = 0.1; // Assume 10% monthly growth
        
        return round($currentWorkload * (1 + $leadGrowthRate), 1);
    }

    /**
     * Calculate optimal team size
     */
    private function calculateOptimalTeamSize($tenantId)
    {
        $totalLeads = Lead::count();
        $optimalLeadsPerAgent = 25; // Industry standard
        
        return max(1, ceil($totalLeads / $optimalLeadsPerAgent));
    }

    /**
     * Get team performance metrics
     */
    private function getTeamPerformanceMetrics($tenantId)
    {
        return [
            'overall_productivity' => $this->calculateTeamProductivityScore($tenantId),
            'collaboration_score' => $this->calculateCollaborationScore($tenantId),
            'training_effectiveness' => $this->calculateTrainingEffectiveness($tenantId),
            'retention_rate' => $this->calculateAgentRetentionRate($tenantId),
        ];
    }

    /**
     * Calculate collaboration score (simplified)
     */
    private function calculateCollaborationScore($tenantId)
    {
        // Simple metric based on shared tasks and documents
        $sharedTasks = Task::where('assigned_to', '!=', null)
            ->whereHas('createdBy', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->count();
            
        $totalTasks = Task::whereHas('createdBy', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->count();
        
        return $totalTasks > 0 ? round(($sharedTasks / $totalTasks) * 100, 2) : 0;
    }

    /**
     * Calculate training effectiveness (simplified)
     */
    private function calculateTrainingEffectiveness($tenantId)
    {
        // Based on improvement in call success rates over time
        $currentMonth = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->where('call_started_at', '>=', Carbon::now()->startOfMonth());
        
        $lastMonth = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->whereBetween('call_started_at', [
            Carbon::now()->subMonth()->startOfMonth(),
            Carbon::now()->subMonth()->endOfMonth()
        ]);
        
        $currentSuccessRate = $currentMonth->where('call_outcome', 'connected')->count() / 
            max($currentMonth->count(), 1) * 100;
            
        $lastSuccessRate = $lastMonth->where('call_outcome', 'connected')->count() / 
            max($lastMonth->count(), 1) * 100;
        
        return round($currentSuccessRate - $lastSuccessRate, 2);
    }

    /**
     * Calculate agent retention rate
     */
    private function calculateAgentRetentionRate($tenantId)
    {
        $totalAgentsStartOfYear = User::where('parent_id', $tenantId)
            ->where('type', 'agent')
            ->where('created_at', '<=', Carbon::now()->startOfYear())
            ->where('is_active', '1')
            ->count();
            
        $agentsStillActive = User::where('parent_id', $tenantId)
            ->where('type', 'agent')
            ->where('created_at', '<=', Carbon::now()->startOfYear())
            ->where('is_active', '1')
            ->count();
        
        return $totalAgentsStartOfYear > 0 ? 
            round(($agentsStillActive / $totalAgentsStartOfYear) * 100, 2) : 100;
    }

    /**
     * Get individual performance metrics
     */
    private function getIndividualPerformanceMetrics($tenantId)
    {
        
        return User::where('parent_id', $tenantId)
            ->where('type', 'agent')
            ->where('is_active', '1')
            ->with(['callLogs', 'assignedLeads' => function($q) {
                $q->where('assigned_at', '>=', Carbon::now()->startOfYear());
            }])
            ->get()
            ->map(function($agent) {
                $monthlyMetrics = $this->calculateAgentMonthlyMetrics($agent);
                
                return [
                    'agent_id' => $agent->id,
                    'name' => $agent->name,
                    'performance_score' => $this->calculateIndividualPerformanceScore($agent),
                    'monthly_metrics' => $monthlyMetrics,
                    'strengths' => $this->identifyAgentStrengths($agent),
                    'improvement_areas' => $this->identifyImprovementAreas($agent),
                ];
            })->toArray();
    }

    /**
     * Calculate agent monthly metrics
     */
    private function calculateAgentMonthlyMetrics($agent)
    {
        $thisMonth = Carbon::now()->startOfMonth();
        
        $calls = $agent->callLogs()->where('call_started_at', '>=', $thisMonth)->get();
        $leads = $agent->assignedLeads()->where('assigned_at', '>=', $thisMonth)->get();
        
        return [
            'total_calls' => $calls->count(),
            'connected_calls' => $calls->where('call_outcome', 'connected')->count(),
            'avg_call_duration' => $calls->avg('duration_seconds') ?? 0,
            'leads_assigned' => $leads->count(),
            'leads_converted' => $leads->where('status', 'converted')->count(),
            'follow_ups_completed' => FollowUp::where('agent_id', $agent->id)
                ->where('status', 'completed')
                ->where('updated_at', '>=', $thisMonth)
                ->count(),
        ];
    }

    /**
     * Calculate individual performance score
     */
    private function calculateIndividualPerformanceScore($agent)
    {
        $metrics = $this->calculateAgentMonthlyMetrics($agent);
        
        // Call performance (40 points)
        $callScore = $metrics['total_calls'] > 0 ? 
            min(($metrics['connected_calls'] / $metrics['total_calls']) * 40, 40) : 0;
        
        // Conversion performance (40 points)
        $conversionScore = $metrics['leads_assigned'] > 0 ? 
            min(($metrics['leads_converted'] / $metrics['leads_assigned']) * 40, 40) : 0;
        
        // Activity score (20 points)
        $activityScore = min($metrics['total_calls'] / 100 * 20, 20); // 100 calls = full score
        
        return round($callScore + $conversionScore + $activityScore, 1);
    }

    /**
     * Identify agent strengths
     */
    private function identifyAgentStrengths($agent)
    {
        $strengths = [];
        $metrics = $this->calculateAgentMonthlyMetrics($agent);
        
        if ($metrics['total_calls'] > 0) {
            $successRate = ($metrics['connected_calls'] / $metrics['total_calls']) * 100;
            if ($successRate > 60) {
                $strengths[] = 'High call success rate';
            }
        }
        
        if ($metrics['leads_assigned'] > 0) {
            $conversionRate = ($metrics['leads_converted'] / $metrics['leads_assigned']) * 100;
            if ($conversionRate > 15) {
                $strengths[] = 'Excellent lead conversion';
            }
        }
        
        if ($metrics['avg_call_duration'] > 300) { // 5 minutes
            $strengths[] = 'Good call engagement';
        }
        
        return $strengths;
    }

    /**
     * Identify improvement areas
     */
    private function identifyImprovementAreas($agent)
    {
        $areas = [];
        $metrics = $this->calculateAgentMonthlyMetrics($agent);
        
        if ($metrics['total_calls'] > 0) {
            $successRate = ($metrics['connected_calls'] / $metrics['total_calls']) * 100;
            if ($successRate < 30) {
                $areas[] = 'Call connection rate needs improvement';
            }
        }
        
        if ($metrics['leads_assigned'] > 0) {
            $conversionRate = ($metrics['leads_converted'] / $metrics['leads_assigned']) * 100;
            if ($conversionRate < 5) {
                $areas[] = 'Lead conversion techniques';
            }
        }
        
        if ($metrics['total_calls'] < 20) {
            $areas[] = 'Increase calling activity';
        }
        
        return $areas;
    }

    /**
     * Get performance trends
     */
    private function getPerformanceTrends($tenantId)
    {
        $trends = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            
            $calls = CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->whereYear('call_started_at', $month->year)
              ->whereMonth('call_started_at', $month->month);
            
            $leads = Lead::whereYear('created_at', $month->year)
                        ->whereMonth('created_at', $month->month);
            
            $trends[] = [
                'month' => $month->format('M Y'),
                'calls_made' => $calls->count(),
                'calls_connected' => $calls->where('call_outcome', 'connected')->count(),
                'leads_generated' => $leads->count(),
                'leads_converted' => $leads->where('status', 'converted')->count(),
                'revenue' => InvoicePayment::where('parent_id', $tenantId)
                    ->whereYear('payment_date', $month->year)
                    ->whereMonth('payment_date', $month->month)
                    ->sum('amount'),
            ];
        }
        
        return $trends;
    }

    /**
     * Get benchmark comparison
     */
    private function getBenchmarkComparison($tenantId)
    {
        $currentMetrics = [
            'lead_conversion_rate' => $this->calculateLeadConversionRate(),
            'call_success_rate' => $this->calculateCallSuccessRate($tenantId),
            'avg_deal_cycle' => $this->calculateAverageDealCycle(),
            'profit_margin' => $this->calculateProfitMargin($tenantId),
        ];
        
        // Industry benchmarks (these would typically come from external data)
        $industryBenchmarks = [
            'lead_conversion_rate' => 12.5,
            'call_success_rate' => 45.0,
            'avg_deal_cycle' => 30.0,
            'profit_margin' => 18.0,
        ];
        
        $comparison = [];
        foreach ($currentMetrics as $metric => $value) {
            $benchmark = $industryBenchmarks[$metric] ?? 0;
            $comparison[$metric] = [
                'current' => $value,
                'benchmark' => $benchmark,
                'performance' => $benchmark > 0 ? round(($value / $benchmark) * 100, 1) : 0,
                'status' => $value >= $benchmark ? 'above' : 'below'
            ];
        }
        
        return $comparison;
    }
}