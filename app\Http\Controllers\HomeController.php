<?php

namespace App\Http\Controllers;
use App\Models\Contact;
use App\Models\Custom;
use App\Models\Expense;
use App\Models\FAQ;
use App\Models\HomePage;
use App\Models\InvoicePayment;
use App\Models\NoticeBoard;
use App\Models\PackageTransaction;
use App\Models\Page;
use App\Models\Subscription;
use App\Models\Support;
use App\Models\User;
use App\Models\Lead;
use App\Models\CallLog;
use App\Models\FollowUp;
use App\Models\Task;
use App\Models\Project;
use App\Models\Property;
use App\Models\Document;
use App\Models\CustomForm;
use App\Models\AgentPerformanceMetric;
use App\Models\LeadActivity;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        if (\Auth::check()) {
            if (\Auth::user()->type == 'super admin') {
                $result['totalOrganization'] = User::where('type', 'owner')->count();
                $result['totalSubscription'] = Subscription::count();
                $result['totalTransaction'] = PackageTransaction::count();
                $result['totalIncome'] = PackageTransaction::sum('amount');
                $result['totalNote'] = NoticeBoard::where('parent_id', parentId())->count();
                $result['totalContact'] = Contact::where('parent_id', parentId())->count();

                $result['organizationByMonth'] = $this->organizationByMonth();
                $result['paymentByMonth'] = $this->paymentByMonth();

                return view('dashboard.super_admin', compact('result'));
            } else {
                // Get comprehensive dashboard data
                $result = $this->getComprehensiveDashboardData();
                return view('dashboard.index', compact('result'));
            }
        } else {
            if (!file_exists(setup())) {
                header('location:install');
                die;
            } else {

                $landingPage=getSettingsValByName('landing_page');
                if($landingPage=='on'){
                    $subscriptions=Subscription::get();
                    $menus = Page::where('enabled',1)->get();
                    $FAQs = FAQ::where('enabled',1)->get();
                    return view('layouts.landing',compact('subscriptions', 'menus', 'FAQs'));
                }else{
                    return redirect()->route('login');
                }
            }

        }

    }

    public function organizationByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $organization = [];
        while ($currentdate <= $end) {
            $organization['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $organization['data'][] = User::where('type', 'owner')->whereMonth('created_at', $month)->whereYear('created_at', $year)->count();
            $currentdate = strtotime('+1 month', $currentdate);
        }


        return $organization;

    }

    public function paymentByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $payment = [];
        while ($currentdate <= $end) {
            $payment['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $payment['data'][] = PackageTransaction::whereMonth('created_at', $month)->whereYear('created_at', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $payment;

    }

    public function incomeByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $payment = [];
        while ($currentdate <= $end) {
            $payment['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $payment['income'][] = InvoicePayment::where('parent_id', parentId())->whereMonth('payment_date', $month)->whereYear('payment_date', $year)->sum('amount');
            $payment['expense'][] = Expense::where('parent_id', parentId())->whereMonth('date', $month)->whereYear('date', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $payment;

    }

    public function incomeExpense()
    {
        $startMonth = strtotime(date('Y-01'));
        $endMonth = strtotime(date('Y-12'));
        $currentdate = $startMonth;
        $report = [];
        while ($currentdate <= $endMonth) {
            $report['label'][] = date('M-Y', $currentdate);
            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $report['income'][] = InvoicePayment::where('parent_id', parentId())->whereMonth('payment_date', $month)->whereYear('payment_date', $year)->sum('amount');
            $report['expense'][] = Expense::where('parent_id', parentId())->whereMonth('date', $month)->whereYear('date', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $report;

    }

    /**
     * Get comprehensive dashboard data for all modules
     */
    private function getComprehensiveDashboardData()
    {
        $tenantId = parentId();
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        $result = [];

        // Basic system stats
        $result['totalUser'] = User::where('parent_id', $tenantId)->count();
        $result['totalClient'] = User::where('parent_id', $tenantId)->where('type', 'client')->count();
        $result['totalIncome'] = InvoicePayment::where('parent_id', $tenantId)->sum('amount');
        $result['totalExpense'] = Expense::where('parent_id', $tenantId)->sum('amount');
        $result['settings'] = settings();
        $result['incomeExpense'] = $this->incomeExpense();

        // Lead Management Stats - Leads have global scope for tenant isolation
        $result['leads'] = [
            'total' => Lead::count(),
            'new_today' => Lead::whereDate('created_at', $today)->count(),
            'converted' => Lead::where('status', 'converted')->count(),
            'hot_leads' => Lead::where('temperature', 'hot')->count(),
        ];

        // Calling System Stats
        $result['calling'] = [
            'total_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->count(),
            'calls_today' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->whereDate('call_started_at', $today)->count(),
            'connected_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('call_status', 'completed')->count(),
            'avg_call_duration' => round(CallLog::whereHas('user', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->avg('duration_seconds') ?? 0),
        ];

        // Project Management Stats - Projects have global scope for tenant isolation
        $result['projects'] = [
            'total' => Project::count(),
            'active' => Project::where('status', 'active')->count(),
            'completed' => Project::where('status', 'completed')->count(),
            'on_hold' => Project::where('status', 'on_hold')->count(),
        ];

        // Property Management Stats - Properties have global scope for tenant isolation
        $result['properties'] = [
            'total' => Property::count(),
            'available' => Property::where('status', 'available')->count(),
            'sold' => Property::where('status', 'sold')->count(),
            'reserved' => Property::where('status', 'reserved')->count(),
        ];

        // Document Management Stats - Documents have global scope for tenant isolation
        $result['documents'] = [
            'total' => Document::count(),
            'recent' => Document::whereDate('created_at', '>=', $thisWeek)->count(),
            'shared' => Document::where('visibility', '!=', 'private')->count(),
        ];

        // Follow-up Stats
        $result['followups'] = [
            'total' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->count(),
            'pending' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'pending')->count(),
            'overdue' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'pending')->where('scheduled_at', '<', now())->count(),
            'completed_today' => FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'completed')->whereDate('updated_at', $today)->count(),
        ];

        // Agent Performance Stats
        $result['agents'] = [
            'total' => User::where('parent_id', $tenantId)->where('type', 'agent')->count(),
            'active_today' => User::where('parent_id', $tenantId)->where('type', 'agent')
                ->whereHas('callLogs', function($q) use ($today) {
                    $q->whereDate('call_started_at', $today);
                })->count(),
            'top_performer' => $this->getTopPerformer($tenantId),
        ];

        // Recent Activities
        $result['recent_activities'] = $this->getRecentActivities($tenantId);

        // Task Management Stats
        $user = Auth::user();
        if ($user->type == 'owner') {
            $taskBaseQuery = Task::withoutGlobalScope('tenant')
                ->where(function($q) use ($user, $tenantId) {
                    $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                        $subQ->where(function($query) use ($tenantId) {
                            $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                        });
                    });
                });
        } else {
            $taskBaseQuery = Task::query(); // Uses global scope
        }

        $result['tasks'] = [
            'total' => (clone $taskBaseQuery)->count(),
            'not_started' => (clone $taskBaseQuery)->where('status', 'not_started')->count(),
            'in_progress' => (clone $taskBaseQuery)->where('status', 'in_progress')->count(),
            'completed' => (clone $taskBaseQuery)->where('status', 'completed')->count(),
            'overdue' => (clone $taskBaseQuery)->where('due_date', '<', now())
                                              ->whereNotIn('status', ['completed', 'cancelled'])
                                              ->count(),
            'due_today' => (clone $taskBaseQuery)->whereDate('due_date', $today)
                                                ->whereNotIn('status', ['completed', 'cancelled'])
                                                ->count(),
            'high_priority' => (clone $taskBaseQuery)->whereIn('priority', ['high', 'urgent'])
                                                     ->whereNotIn('status', ['completed', 'cancelled'])
                                                     ->count(),
            'assigned_to_me' => (clone $taskBaseQuery)->where('assigned_to', $user->id)->count(),
        ];

        // Chart Data
        $result['charts'] = $this->getChartData($tenantId, $today, $thisWeek, $thisMonth);

        return $result;
    }

    private function getTopPerformer($tenantId)
    {
        $topAgent = AgentPerformanceMetric::where('tenant_id', $tenantId)
            ->where('metric_date', '>=', Carbon::now()->startOfMonth())
            ->select('agent_id', DB::raw('SUM(leads_converted) as total_conversions'))
            ->groupBy('agent_id')
            ->orderBy('total_conversions', 'desc')
            ->with('agent')
            ->first();

        return $topAgent ? [
            'name' => $topAgent->agent->name,
            'conversions' => $topAgent->total_conversions
        ] : null;
    }

    private function getRecentActivities($tenantId)
    {
        $activities = [];

        // Recent leads
        $recentLeads = Lead::whereHas('assignedTo', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->with(['assignedTo:id,name', 'createdBy:id,name'])
          ->orderBy('created_at', 'desc')
          ->limit(5)
          ->get();

        foreach ($recentLeads as $lead) {
            $activities[] = [
                'type' => 'lead',
                'title' => 'New Lead: ' . $lead->name,
                'description' => 'Assigned to ' . ($lead->assignedTo->name ?? 'Unassigned'),
                'time' => $lead->created_at,
                'icon' => 'ti-user-plus',
                'color' => 'primary'
            ];
        }

        // Recent calls
        $recentCalls = CallLog::whereHas('user', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->with(['user:id,name', 'lead:id,name'])
          ->orderBy('call_started_at', 'desc')
          ->limit(5)
          ->get();

        foreach ($recentCalls as $call) {
            $activities[] = [
                'type' => 'call',
                'title' => 'Call: ' . ($call->lead->name ?? $call->contact_name),
                'description' => 'By ' . $call->user->name . ' - ' . ucfirst($call->call_outcome),
                'time' => $call->call_started_at,
                'icon' => 'ti-phone',
                'color' => $call->call_outcome === 'connected' ? 'success' : 'warning'
            ];
        }

        // Recent follow-ups
        $recentFollowUps = FollowUp::whereHas('lead.assignedTo', function($q) use ($tenantId) {
            $q->where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
            });
        })->with(['lead:id,name', 'agent:id,name'])
          ->where('status', 'completed')
          ->orderBy('updated_at', 'desc')
          ->limit(5)
          ->get();

        foreach ($recentFollowUps as $followUp) {
            $activities[] = [
                'type' => 'followup',
                'title' => 'Follow-up: ' . $followUp->title,
                'description' => 'For ' . $followUp->lead->name . ' by ' . $followUp->agent->name,
                'time' => $followUp->updated_at,
                'icon' => 'ti-calendar-check',
                'color' => 'info'
            ];
        }

        // Sort by time and return top 10
        usort($activities, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, 10);
    }

    private function getChartData($tenantId, $today, $thisWeek, $thisMonth)
    {
        // Lead conversion funnel
        $leadFunnel = [
            'new' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'new')->count(),
            'contacted' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'contacted')->count(),
            'qualified' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'qualified')->count(),
            'proposal' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('stage', 'proposal')->count(),
            'converted' => Lead::whereHas('assignedTo', function($q) use ($tenantId) {
                $q->where(function($query) use ($tenantId) {
                    $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                });
            })->where('status', 'converted')->count(),
        ];

        // Call performance trends (last 7 days)
        $callTrends = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $callTrends[] = [
                'date' => $date->format('M d'),
                'calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->whereDate('call_started_at', $date)->count(),
                'connected' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->whereDate('call_started_at', $date)->where('call_outcome', 'connected')->count(),
            ];
        }

        // Agent performance comparison
        $agentPerformance = User::where('parent_id', $tenantId)
            ->where('type', 'agent')
            ->withCount([
                'callLogs as total_calls' => function($q) use ($thisMonth) {
                    $q->where('call_started_at', '>=', $thisMonth);
                },
                'callLogs as connected_calls' => function($q) use ($thisMonth) {
                    $q->where('call_started_at', '>=', $thisMonth)
                      ->where('call_outcome', 'connected');
                }
            ])
            ->limit(10)
            ->get()
            ->map(function($agent) {
                return [
                    'name' => $agent->name,
                    'total_calls' => $agent->total_calls,
                    'connected_calls' => $agent->connected_calls,
                    'conversion_rate' => $agent->total_calls > 0 ? round(($agent->connected_calls / $agent->total_calls) * 100, 1) : 0
                ];
            });

        return [
            'lead_funnel' => $leadFunnel,
            'call_trends' => $callTrends,
            'agent_performance' => $agentPerformance
        ];
    }
}
