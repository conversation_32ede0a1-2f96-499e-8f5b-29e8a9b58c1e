{{ Form::open(['url' => 'tasks', 'method' => 'post', 'enctype' => 'multipart/form-data']) }}
<div class="modal-body">
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <!-- Task Title -->
                        <div class="form-group col-md-12">
                            {{ Form::label('title', __('Task Title'), ['class' => 'form-label']) }}
                            {{ Form::text('title', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter task title')]) }}
                        </div>

                        <!-- Task Description -->
                        <div class="form-group col-md-12">
                            {{ Form::label('description', __('Description'), ['class' => 'form-label']) }}
                            {{ Form::textarea('description', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter task description')]) }}
                        </div>

                        <!-- Category and Priority -->
                        <div class="form-group col-md-6">
                            {{ Form::label('category', __('Category'), ['class' => 'form-label']) }}
                            {{ Form::select('category', \App\Models\Task::$categories, null, ['class' => 'form-control select2', 'required' => 'required']) }}
                        </div>

                        <div class="form-group col-md-6">
                            {{ Form::label('priority', __('Priority'), ['class' => 'form-label']) }}
                            {{ Form::select('priority', \App\Models\Task::$priorities, 'medium', ['class' => 'form-control select2', 'required' => 'required']) }}
                        </div>

                        <!-- Due Date and Estimated Hours -->
                        <div class="form-group col-md-6">
                            {{ Form::label('due_date', __('Due Date'), ['class' => 'form-label']) }}
                            {{ Form::datetime('due_date', null, ['class' => 'form-control', 'placeholder' => __('Select due date')]) }}
                        </div>

                        <div class="form-group col-md-6">
                            {{ Form::label('estimated_hours', __('Estimated Hours'), ['class' => 'form-label']) }}
                            {{ Form::number('estimated_hours', null, ['class' => 'form-control', 'min' => 1, 'placeholder' => __('Enter estimated hours')]) }}
                        </div>

                        <!-- Assigned To -->
                        <div class="form-group col-md-12">
                            {{ Form::label('assigned_to', __('Assign To'), ['class' => 'form-label']) }}
                            {{ Form::select('assigned_to', $users->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select user to assign')]) }}
                        </div>

                        <!-- Related Entity Selection -->
                        <div class="form-group col-md-12">
                            <label class="form-label">{{ __('Link to Related Entity') }}</label>
                            <div class="row">
                                <div class="col-md-4">
                                    {{ Form::label('lead_id', __('Lead'), ['class' => 'form-label']) }}
                                    {{ Form::select('lead_id', $leads->pluck('name', 'id'), $relatedType == 'lead' ? $relatedEntity->id ?? null : null, ['class' => 'form-control select2', 'placeholder' => __('Select lead')]) }}
                                </div>
                                <div class="col-md-4">
                                    {{ Form::label('project_id', __('Project'), ['class' => 'form-label']) }}
                                    {{ Form::select('project_id', $projects->pluck('name', 'id'), $relatedType == 'project' ? $relatedEntity->id ?? null : null, ['class' => 'form-control select2', 'placeholder' => __('Select project')]) }}
                                </div>
                                <div class="col-md-4">
                                    {{ Form::label('property_id', __('Property'), ['class' => 'form-label']) }}
                                    {{ Form::select('property_id', $properties->pluck('title', 'id'), $relatedType == 'property' ? $relatedEntity->id ?? null : null, ['class' => 'form-control select2', 'placeholder' => __('Select property')]) }}
                                </div>
                            </div>
                            <small class="text-muted">{{ __('You can link this task to a specific lead, project, or property (optional)') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" onclick="location.href = '{{ route('tasks.index') }}';" class="btn btn-light">
    <input type="submit" value="{{ __('Create') }}" class="btn btn-primary">
</div>
{{ Form::close() }}

<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            placeholder: function() {
                return $(this).data('placeholder');
            },
            allowClear: true,
            width: '100%'
        });

        // Clear other related entity selections when one is selected
        $('#lead_id').change(function() {
            if ($(this).val()) {
                $('#project_id, #property_id').val(null).trigger('change');
            }
        });

        $('#project_id').change(function() {
            if ($(this).val()) {
                $('#lead_id, #property_id').val(null).trigger('change');
            }
        });

        $('#property_id').change(function() {
            if ($(this).val()) {
                $('#lead_id, #project_id').val(null).trigger('change');
            }
        });

        // Auto-set category based on related entity
        $('#lead_id').change(function() {
            if ($(this).val()) {
                $('#category').val('lead_task').trigger('change');
            }
        });

        $('#project_id').change(function() {
            if ($(this).val()) {
                $('#category').val('project_task').trigger('change');
            }
        });

        $('#property_id').change(function() {
            if ($(this).val()) {
                $('#category').val('property_task').trigger('change');
            }
        });
    });
</script>
