<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Property Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('properties.index')); ?>"><?php echo e(__('Properties')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Dashboard')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="<?php echo e(route('properties.create')); ?>" data-url="<?php echo e(route('properties.create')); ?>" data-ajax-popup="true" 
           data-size="xl" data-title="<?php echo e(__('Create Property')); ?>" data-bs-toggle="tooltip" title="<?php echo e(__('Create')); ?>" 
           class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i>
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-primary">
                                    <i class="ti ti-home"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Total Properties')); ?></small>
                                    <h6 class="m-0"><?php echo e($totalProperties); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-success">
                                    <i class="ti ti-check"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Available')); ?></small>
                                    <h6 class="m-0"><?php echo e($availableProperties); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-danger">
                                    <i class="ti ti-x"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Sold')); ?></small>
                                    <h6 class="m-0"><?php echo e($soldProperties); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-warning">
                                    <i class="ti ti-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Reserved')); ?></small>
                                    <h6 class="m-0"><?php echo e($reservedProperties); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Properties -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Recent Properties')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Property')); ?></th>
                                    <th><?php echo e(__('Type')); ?></th>
                                    <th><?php echo e(__('Project')); ?></th>
                                    <th><?php echo e(__('Price')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Agent')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $recentProperties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo e(route('properties.show', $property->id)); ?>" class="text-decoration-none">
                                                <?php echo e($property->name); ?>

                                            </a>
                                            <br>
                                            <small class="text-muted"><?php echo e($property->city); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e(\App\Models\Property::$types[$property->type] ?? $property->type); ?></span>
                                        </td>
                                        <td><?php echo e($property->project->name ?? 'N/A'); ?></td>
                                        <td><?php echo e($property->formatted_price); ?></td>
                                        <td>
                                            <?php if($property->status == 'available'): ?>
                                                <span class="badge bg-success"><?php echo e(\App\Models\Property::$statuses[$property->status]); ?></span>
                                            <?php elseif($property->status == 'sold'): ?>
                                                <span class="badge bg-danger"><?php echo e(\App\Models\Property::$statuses[$property->status]); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><?php echo e(\App\Models\Property::$statuses[$property->status]); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($property->assignedAgent->name ?? 'Unassigned'); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="text-center"><?php echo e(__('No properties found')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Properties by Type -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Properties by Type')); ?></h5>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $propertiesByType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo e(\App\Models\Property::$types[$type] ?? $type); ?></span>
                            <span class="badge bg-primary"><?php echo e($count); ?></span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-muted"><?php echo e(__('No data available')); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Properties by City -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><?php echo e(__('Top Cities')); ?></h5>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $propertiesByCity; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo e($city); ?></span>
                            <span class="badge bg-info"><?php echo e($count); ?></span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-muted"><?php echo e(__('No data available')); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Quick Actions')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="<?php echo e(route('properties.create')); ?>" class="btn btn-primary w-100 mb-2">
                                <i class="ti ti-plus me-2"></i><?php echo e(__('Add Property')); ?>

                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(route('properties.index')); ?>" class="btn btn-outline-primary w-100 mb-2">
                                <i class="ti ti-list me-2"></i><?php echo e(__('View All Properties')); ?>

                            </a>
                        </div>
                        <?php if(Gate::check('import properties')): ?>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100 mb-2" data-bs-toggle="modal" data-bs-target="#importModal">
                                <i class="ti ti-upload me-2"></i><?php echo e(__('Import Properties')); ?>

                            </button>
                        </div>
                        <?php endif; ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/dashboard.blade.php ENDPATH**/ ?>