<?php
    $admin_logo = getSettingsValByName('company_logo');
    $ids = parentId();
    $authUser = \App\Models\User::find($ids);
    $subscription = \App\Models\Subscription::find($authUser->subscription);
    $routeName = \Request::route()->getName();
    $pricing_feature_settings = getSettingsValByIdName(1, 'pricing_feature');
?>
<nav class="pc-sidebar">
    <div class="navbar-wrapper">
        <div class="m-header">
            <a href="#" class="b-brand text-primary">
                <img src="<?php echo e(asset(Storage::url('upload/logo/')) . '/' . (isset($admin_logo) && !empty($admin_logo) ? $admin_logo : 'logo.png')); ?>"
                    alt="" class="logo logo-lg" />
            </a>
        </div>
        <div class="navbar-content">
            <ul class="pc-navbar">
                <li class="pc-item pc-caption">
                    <label><?php echo e(__('navigation.home')); ?></label>
                    <i class="ti ti-dashboard"></i>
                </li>
                <li class="pc-item <?php echo e(in_array($routeName, ['dashboard', 'home', '']) ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('dashboard')); ?>" class="pc-link">
                        <span class="pc-micon"><i class="ti ti-dashboard"></i></span>
                        <span class="pc-mtext"><?php echo e(__('navigation.dashboard')); ?></span>
                    </a>
                </li>
                
                <?php if(\Auth::user()->type == 'super admin'): ?>
                    <?php if(Gate::check('manage user')): ?>
                        <li class="pc-item <?php echo e(in_array($routeName, ['users.index', 'users.show']) ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('users.index')); ?>" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-user-plus"></i></span>
                                <span class="pc-mtext"><?php echo e(__('navigation.customers')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php else: ?>
                    <?php if(Gate::check('manage user') || Gate::check('manage role') || Gate::check('manage logged history')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['users.index', 'logged.history', 'role.index', 'role.create', 'role.edit']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-users"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Staff Management')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['users.index', 'logged.history', 'role.index', 'role.create', 'role.edit']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('manage user')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['users.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('users.index')); ?>"><?php echo e(__('navigation.users')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage role')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['role.index', 'role.create', 'role.edit']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('role.index')); ?>"><?php echo e(__('roles.roles')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if($pricing_feature_settings == 'off' || $subscription->enabled_logged_history == 1): ?>
                                    <?php if(Gate::check('manage logged history')): ?>
                                        <li class="pc-item <?php echo e(in_array($routeName, ['logged.history']) ? 'active' : ''); ?>">
                                            <a class="pc-link" href="<?php echo e(route('logged.history')); ?>"><?php echo e(__('Logged History')); ?></a>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                
                <?php if(Gate::check('manage client') ||
                        Gate::check('manage item') ||
                        Gate::check('manage estimation') ||
                        Gate::check('manage invoice') ||
                        Gate::check('manage expense') ||
                        Gate::check('manage contact') ||
                        Gate::check('manage note') ||
                        Gate::check('manage leads') ||
                        Gate::check('manage projects') ||
                        Gate::check('manage tasks')): ?>
                    <li class="pc-item pc-caption">
                        <label><?php echo e(__('Business Management')); ?></label>
                        <i class="ti ti-chart-arcs"></i>
                    </li>

                    
                    <?php if(Gate::check('manage client')): ?>
                        <li class="pc-item <?php echo e(in_array($routeName, ['client.index']) ? 'active' : ''); ?>">
                            <a class="pc-link" href="<?php echo e(route('client.index')); ?>">
                                <span class="pc-micon"><i data-feather="user"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Client')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('manage leads')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['leads.index', 'leads.create', 'leads.show', 'leads.edit', 'leads.duplicates.stats', 'leads.lifecycle.analytics']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="phone-call"></i></span>
                                <span class="pc-mtext"><?php echo e(__('navigation.leads')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['leads.index', 'leads.create', 'leads.show', 'leads.edit', 'leads.duplicates.stats', 'leads.lifecycle.analytics']) ? 'block' : 'none'); ?>">
                                <li class="pc-item <?php echo e(in_array($routeName, ['leads.index']) ? 'active' : ''); ?>">
                                    <a class="pc-link" href="<?php echo e(route('leads.index')); ?>"><?php echo e(__('All Leads')); ?></a>
                                </li>
                                <?php if(Gate::check('view duplicate stats')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['leads.duplicates.stats']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('leads.duplicates.stats')); ?>"><?php echo e(__('Duplicate Management')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('view lifecycle analytics')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['leads.lifecycle.analytics']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('leads.lifecycle.analytics')); ?>"><?php echo e(__('Lifecycle Analytics')); ?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('manage projects')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['projects.index', 'projects.create', 'projects.show', 'projects.edit', 'projects.dashboard']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="briefcase"></i></span>
                                <span class="pc-mtext"><?php echo e(__('navigation.projects')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['projects.index', 'projects.create', 'projects.show', 'projects.edit', 'projects.dashboard']) ? 'block' : 'none'); ?>">
                                <li class="pc-item <?php echo e(in_array($routeName, ['projects.index']) ? 'active' : ''); ?>">
                                    <a class="pc-link" href="<?php echo e(route('projects.index')); ?>"><?php echo e(__('All Projects')); ?></a>
                                </li>
                                <?php if(Gate::check('view project dashboard')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['projects.dashboard']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('projects.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('manage tasks') || Gate::check('view tasks')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['tasks.index', 'tasks.create', 'tasks.show', 'tasks.edit', 'tasks.dashboard', 'tasks.calendar', 'tasks.analytics']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="check-square"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Task Management')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['tasks.index', 'tasks.create', 'tasks.show', 'tasks.edit', 'tasks.dashboard', 'tasks.calendar', 'tasks.analytics']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('view task dashboard')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['tasks.dashboard']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('tasks.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <li class="pc-item <?php echo e(in_array($routeName, ['tasks.index']) ? 'active' : ''); ?>">
                                    <a class="pc-link" href="<?php echo e(route('tasks.index')); ?>"><?php echo e(__('All Tasks')); ?></a>
                                </li>
                                <?php if(Gate::check('create tasks')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['tasks.create']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('tasks.create')); ?>"><?php echo e(__('Create Task')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('view task calendar')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['tasks.calendar']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('tasks.calendar')); ?>"><?php echo e(__('Task Calendar')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('view task analytics')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['tasks.analytics']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('tasks.analytics')); ?>"><?php echo e(__('Analytics & Reports')); ?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('calling_system_call_log_view') || Gate::check('calling_system_lead_assignment_view') || Gate::check('calling_system_follow_up_view') || Gate::check('calling_system_call_queue_view') || Gate::check('calling_system_performance_view_own')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['calling.dashboard', 'call-logs.index', 'lead-assignments.index', 'follow-ups.index', 'call-queue.index', 'calling.performance', 'calling.analytics']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="phone"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Calling System')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['calling.dashboard', 'call-logs.index', 'lead-assignments.index', 'follow-ups.index', 'call-queue.index', 'calling.performance', 'calling.analytics']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('calling_system_dashboard_agent') || Gate::check('calling_system_dashboard_manager') || Gate::check('calling_system_dashboard_admin')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['calling.dashboard']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('calling.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('calling_system_call_log_view')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['call-logs.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('call-logs.index')); ?>"><?php echo e(__('Call Logs')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('calling_system_lead_assignment_view')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['lead-assignments.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('lead-assignments.index')); ?>"><?php echo e(__('Lead Assignments')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('calling_system_follow_up_view')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['follow-ups.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('follow-ups.index')); ?>"><?php echo e(__('Follow-ups')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('calling_system_call_queue_view')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['call-queue.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('call-queue.index')); ?>"><?php echo e(__('Call Queue')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('calling_system_performance_view_own') || Gate::check('calling_system_performance_view_team') || Gate::check('calling_system_performance_view_all')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['calling.performance']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('calling.performance')); ?>"><?php echo e(__('Performance')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('calling_system_analytics_view')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['calling.analytics']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('calling.analytics')); ?>"><?php echo e(__('Analytics')); ?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('manage item') || Gate::check('manage estimation') || Gate::check('manage invoice') || Gate::check('manage expense')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['item.index', 'estimation.index', 'estimation.show', 'invoice.index', 'invoice.show', 'expense.index']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="dollar-sign"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Sales & Finance')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['item.index', 'estimation.index', 'estimation.show', 'invoice.index', 'invoice.show', 'expense.index']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('manage item')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['item.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('item.index')); ?>">
                                            <span class="pc-micon"><i data-feather="sliders"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Items')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage estimation')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['estimation.index', 'estimation.show']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('estimation.index')); ?>">
                                            <span class="pc-micon"><i data-feather="file-text"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Estimation')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage invoice')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['invoice.index', 'invoice.show']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('invoice.index')); ?>">
                                            <span class="pc-micon"><i data-feather="file-plus"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Invoice')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage expense')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['expense.index']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('expense.index')); ?>">
                                            <span class="pc-micon"><i data-feather="check-circle"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Expense')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('view documents') || Gate::check('form_builder_access')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, [
                            'documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard',
                            'forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show'
                        ]) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="file-text"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Documents & Forms')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, [
                                'documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard',
                                'forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show'
                            ]) ? 'block' : 'none'); ?>">
                                
                                
                                <?php if(Gate::check('view documents')): ?>
                                    <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard']) ? 'pc-trigger active' : ''); ?>">
                                        <a href="#!" class="pc-link">
                                            <span class="pc-mtext"><?php echo e(__('Document Management')); ?></span>
                                            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                        </a>
                                        <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['documents.index', 'documents.create', 'documents.show', 'documents.edit', 'documents.dashboard']) ? 'block' : 'none'); ?>">
                                            <li class="pc-item <?php echo e(in_array($routeName, ['documents.index']) ? 'active' : ''); ?>">
                                                <a class="pc-link" href="<?php echo e(route('documents.index')); ?>"><?php echo e(__('All Documents')); ?></a>
                                            </li>
                                            <?php if(Gate::check('create documents')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['documents.create']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('documents.create')); ?>"><?php echo e(__('Create Document')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('view document dashboard')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['documents.dashboard']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('documents.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>

                                
                                <?php if(Gate::check('form_builder_access')): ?>
                                    <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show']) ? 'pc-trigger active' : ''); ?>">
                                        <a href="#!" class="pc-link">
                                            <span class="pc-mtext"><?php echo e(__('Form Builder')); ?></span>
                                            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                        </a>
                                        <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['forms.index', 'forms.create', 'forms.show', 'forms.edit', 'form-submissions.index', 'form-submissions.show']) ? 'block' : 'none'); ?>">
                                            <li class="pc-item <?php echo e(in_array($routeName, ['forms.index']) ? 'active' : ''); ?>">
                                                <a class="pc-link" href="<?php echo e(route('forms.index')); ?>"><?php echo e(__('All Forms')); ?></a>
                                            </li>
                                            <?php if(Gate::check('form_submissions_view')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['form-submissions.index']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('form-submissions.index')); ?>"><?php echo e(__('Submissions')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('form_templates_view')): ?>
                                                <li class="pc-item">
                                                    <a class="pc-link" href="#" onclick="$('#commonModal').modal('show'); loadModal('<?php echo e(route('forms.templates')); ?>', '<?php echo e(__('Form Templates')); ?>');"><?php echo e(__('Templates')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('view properties')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, [
                            'properties.index', 'properties.create', 'properties.show', 'properties.edit', 'properties.dashboard',
                            'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                            'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                        ]) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="home"></i></span>
                                <span class="pc-mtext"><?php echo e(__('navigation.properties')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, [
                                'properties.index', 'properties.create', 'properties.show', 'properties.edit', 'properties.dashboard',
                                'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                                'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                            ]) ? 'block' : 'none'); ?>">
                                <li class="pc-item <?php echo e(in_array($routeName, ['properties.index']) ? 'active' : ''); ?>">
                                    <a class="pc-link" href="<?php echo e(route('properties.index')); ?>"><?php echo e(__('All Properties')); ?></a>
                                </li>
                                <?php if(Gate::check('create properties')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['properties.add']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('properties.add')); ?>"><?php echo e(__('Add Property')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('view property dashboard')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['properties.dashboard']) ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('properties.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('view property analytics')): ?>
                                    <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, [
                                        'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                                        'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                                    ]) ? 'pc-trigger active' : ''); ?>">
                                        <a href="#!" class="pc-link">
                                            <span class="pc-mtext"><?php echo e(__('Analytics & Reports')); ?></span>
                                            <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                        </a>
                                        <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, [
                                            'properties.analytics.index', 'properties.analytics.dashboard', 'properties.analytics.performance',
                                            'properties.analytics.occupancy', 'properties.analytics.pricing', 'properties.analytics.leads'
                                        ]) ? 'block' : 'none'); ?>">
                                            <li class="pc-item <?php echo e(in_array($routeName, ['properties.analytics.index']) ? 'active' : ''); ?>">
                                                <a class="pc-link" href="<?php echo e(route('properties.analytics.index')); ?>"><?php echo e(__('Overview')); ?></a>
                                            </li>
                                            <?php if(Gate::check('view property performance')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['properties.analytics.performance']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('properties.analytics.performance')); ?>"><?php echo e(__('Performance')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('view occupancy reports')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['properties.analytics.occupancy']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('properties.analytics.occupancy')); ?>"><?php echo e(__('Occupancy')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('view pricing analytics')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['properties.analytics.pricing']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('properties.analytics.pricing')); ?>"><?php echo e(__('Pricing Analytics')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('view lead analytics')): ?>
                                                <li class="pc-item <?php echo e(in_array($routeName, ['properties.analytics.leads']) ? 'active' : ''); ?>">
                                                    <a class="pc-link" href="<?php echo e(route('properties.analytics.leads')); ?>"><?php echo e(__('Lead Analytics')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('manage contact') || Gate::check('manage note')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['contact.index', 'note.index']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-message-circle"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Communication')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['contact.index', 'note.index']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('manage contact')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['contact.index']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('contact.index')); ?>" class="pc-link">
                                            <span class="pc-micon"><i class="ti ti-phone-call"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Contact Diary')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage note')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['note.index']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('note.index')); ?>" class="pc-link">
                                            <span class="pc-micon"><i class="ti ti-notebook"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Notice Board')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                
                <?php if(Gate::check('manage category') ||
                        Gate::check('manage tax') ||
                        Gate::check('manage unit') ||
                        Gate::check('manage notification')): ?>
                    <li class="pc-item pc-caption">
                        <label><?php echo e(__('System Configuration')); ?></label>
                        <i class="ti ti-chart-arcs"></i>
                    </li>

                    
                    <?php if(Gate::check('manage category') || Gate::check('manage tax') || Gate::check('manage unit')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['item-category.index', 'item-tax.index', 'item-unit.index', 'sales-category.index']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i data-feather="package"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Item Configuration')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['item-category.index', 'item-tax.index', 'item-unit.index', 'sales-category.index']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('manage category')): ?>
                                    <li class="pc-item <?php echo e(Request::route()->getName() == 'item-category.index' ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('item-category.index')); ?>">
                                            <span class="pc-micon"><i data-feather="wind"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Item Category')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage tax')): ?>
                                    <li class="pc-item <?php echo e(Request::route()->getName() == 'item-tax.index' ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('item-tax.index')); ?>">
                                            <span class="pc-micon"><i data-feather="trello"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Item Tax')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage unit')): ?>
                                    <li class="pc-item <?php echo e(Request::route()->getName() == 'item-unit.index' ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('item-unit.index')); ?>">
                                            <span class="pc-micon"><i data-feather="tag"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Item Unit')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage category')): ?>
                                    <li class="pc-item <?php echo e(Request::route()->getName() == 'sales-category.index' ? 'active' : ''); ?>">
                                        <a class="pc-link" href="<?php echo e(route('sales-category.index')); ?>">
                                            <span class="pc-micon"><i data-feather="wind"></i></span>
                                            <span class="pc-mtext"><?php echo e(__('Sales Category')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    <?php if(Gate::check('manage notification')): ?>
                        <li class="pc-item <?php echo e(in_array($routeName, ['notification.index']) ? 'active' : ''); ?>">
                            <a class="pc-link" href="<?php echo e(route('notification.index')); ?>">
                                <span class="pc-micon"><i class="ti ti-bell"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Email Notification')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                
                <?php if(Gate::check('manage pricing packages') ||
                        Gate::check('manage pricing transation') ||
                        Gate::check('manage account settings') ||
                        Gate::check('manage password settings') ||
                        Gate::check('manage general settings') ||
                        Gate::check('manage email settings') ||
                        Gate::check('manage payment settings') ||
                        Gate::check('manage company settings') ||
                        Gate::check('manage seo settings') ||
                        Gate::check('manage google recaptcha settings')): ?>
                    <li class="pc-item pc-caption">
                        <label><?php echo e(__('System Settings')); ?></label>
                        <i class="ti ti-chart-arcs"></i>
                    </li>

                    
                    <?php if(Gate::check('manage FAQ') || Gate::check('manage Page')): ?>
                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['homepage.index', 'FAQ.index', 'pages.index', 'footerSetting', 'authPage.index']) ? 'pc-trigger active' : ''); ?>">
                            <a href="#!" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-layout-rows"></i></span>
                                <span class="pc-mtext"><?php echo e(__('CMS')); ?></span>
                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['homepage.index', 'FAQ.index', 'pages.index', 'footerSetting', 'authPage.index']) ? 'block' : 'none'); ?>">
                                <?php if(Gate::check('manage home page')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['homepage.index']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('homepage.index')); ?>" class="pc-link"><?php echo e(__('Home Page')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage Page')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['pages.index']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('pages.index')); ?>" class="pc-link"><?php echo e(__('Custom Page')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage FAQ')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['FAQ.index']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('FAQ.index')); ?>" class="pc-link"><?php echo e(__('FAQ')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage footer')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['footerSetting']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('footerSetting')); ?>" class="pc-link"><?php echo e(__('Footer')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage auth page')): ?>
                                    <li class="pc-item <?php echo e(in_array($routeName, ['authPage.index']) ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('authPage.index')); ?>" class="pc-link"><?php echo e(__('Auth Page')); ?></a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    
                    <?php if(Auth::user()->type == 'super admin' || $pricing_feature_settings == 'on'): ?>
                        <?php if(Gate::check('manage pricing packages') || Gate::check('manage pricing transation') || Gate::check('manage coupon') || Gate::check('manage coupon history')): ?>
                            <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction', 'coupons.index', 'coupons.history']) ? 'pc-trigger active' : ''); ?>">
                                <a href="#!" class="pc-link">
                                    <span class="pc-micon"><i class="ti ti-package"></i></span>
                                    <span class="pc-mtext"><?php echo e(__('Pricing & Coupons')); ?></span>
                                    <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                </a>
                                <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction', 'coupons.index', 'coupons.history']) ? 'block' : 'none'); ?>">
                                    
                                    
                                    <?php if(Gate::check('manage pricing packages') || Gate::check('manage pricing transation')): ?>
                                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction']) ? 'pc-trigger active' : ''); ?>">
                                            <a href="#!" class="pc-link">
                                                <span class="pc-mtext"><?php echo e(__('Pricing Management')); ?></span>
                                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['subscriptions.index', 'subscriptions.show', 'subscription.transaction']) ? 'block' : 'none'); ?>">
                                                <?php if(Gate::check('manage pricing packages')): ?>
                                                    <li class="pc-item <?php echo e(in_array($routeName, ['subscriptions.index', 'subscriptions.show']) ? 'active' : ''); ?>">
                                                        <a class="pc-link" href="<?php echo e(route('subscriptions.index')); ?>"><?php echo e(__('Packages')); ?></a>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if(Gate::check('manage pricing transation')): ?>
                                                    <li class="pc-item <?php echo e(in_array($routeName, ['subscription.transaction']) ? 'active' : ''); ?>">
                                                        <a class="pc-link" href="<?php echo e(route('subscription.transaction')); ?>"><?php echo e(__('Transactions')); ?></a>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </li>
                                    <?php endif; ?>

                                    
                                    <?php if(Gate::check('manage coupon') || Gate::check('manage coupon history')): ?>
                                        <li class="pc-item pc-hasmenu <?php echo e(in_array($routeName, ['coupons.index', 'coupons.history']) ? 'pc-trigger active' : ''); ?>">
                                            <a href="#!" class="pc-link">
                                                <span class="pc-mtext"><?php echo e(__('Coupon Management')); ?></span>
                                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="pc-submenu" style="display: <?php echo e(in_array($routeName, ['coupons.index', 'coupons.history']) ? 'block' : 'none'); ?>">
                                                <?php if(Gate::check('manage coupon')): ?>
                                                    <li class="pc-item <?php echo e(in_array($routeName, ['coupons.index']) ? 'active' : ''); ?>">
                                                        <a class="pc-link" href="<?php echo e(route('coupons.index')); ?>"><?php echo e(__('All Coupon')); ?></a>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if(Gate::check('manage coupon history')): ?>
                                                    <li class="pc-item <?php echo e(in_array($routeName, ['coupons.history']) ? 'active' : ''); ?>">
                                                        <a class="pc-link" href="<?php echo e(route('coupons.history')); ?>"><?php echo e(__('Coupon History')); ?></a>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>

                    
                    <?php if(Gate::check('manage account settings') ||
                            Gate::check('manage password settings') ||
                            Gate::check('manage general settings') ||
                            Gate::check('manage email settings') ||
                            Gate::check('manage payment settings') ||
                            Gate::check('manage company settings') ||
                            Gate::check('manage seo settings') ||
                            Gate::check('manage google recaptcha settings')): ?>
                        <li class="pc-item <?php echo e(in_array($routeName, ['setting.index']) ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('setting.index')); ?>" class="pc-link">
                                <span class="pc-micon"><i class="ti ti-settings"></i></span>
                                <span class="pc-mtext"><?php echo e(__('Settings')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
            </ul>
            <div class="w-100 text-center">
                <div class="badge theme-version badge rounded-pill bg-light text-dark f-12"></div>
            </div>
        </div>
    </div>
</nav><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/admin/menu.blade.php ENDPATH**/ ?>