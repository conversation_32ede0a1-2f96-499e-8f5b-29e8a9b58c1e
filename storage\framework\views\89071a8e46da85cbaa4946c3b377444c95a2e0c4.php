<?php $__env->startSection('page-title'); ?>
    <?php echo e($project->name); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('projects.index')); ?>"><?php echo e(__('Projects')); ?></a></li>
    <li class="breadcrumb-item" aria-current="page"><?php echo e($project->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Project Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <h3 class="mb-0"><?php echo e($project->name); ?></h3>
                    <p class="text-muted mb-0"><?php echo e($project->code); ?> • <?php echo e($project->location); ?></p>
                </div>
                <div class="ms-auto">
                    <span class="badge bg-<?php echo e($project->status_color); ?> me-2"><?php echo e(\App\Models\Project::$statuses[$project->status]); ?></span>
                    <span class="badge bg-<?php echo e($project->priority_color); ?>"><?php echo e(\App\Models\Project::$priorities[$project->priority]); ?></span>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit projects')): ?>
                <a href="#" class="btn btn-outline-primary customModal" data-size="lg"
                    data-url="<?php echo e(route('projects.edit', $project->id)); ?>" data-title="<?php echo e(__('Edit Project')); ?>">
                    <i class="ti ti-edit"></i> <?php echo e(__('Edit')); ?>

                </a>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete projects')): ?>
                <form method="POST" action="<?php echo e(route('projects.destroy', $project->id)); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-outline-danger" onclick="return confirm('<?php echo e(__('Are you sure?')); ?>')">
                        <i class="ti ti-trash"></i> <?php echo e(__('Delete')); ?>

                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- Project Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-progress fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Progress')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($project->progress_percentage); ?>%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-target fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Milestones')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['completed_milestones']); ?>/<?php echo e($stats['total_milestones']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-currency-dollar fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Budget Used')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['budget_utilization']); ?>%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-files fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Documents')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['total_documents']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Project Details -->
        <div class="col-md-8">
            <!-- Project Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><?php echo e(__('Project Information')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><?php echo e(__('Type')); ?>:</strong></td>
                                    <td><?php echo e(\App\Models\Project::$types[$project->type] ?? $project->type); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Total Area')); ?>:</strong></td>
                                    <td><?php echo e($project->total_area ? number_format($project->total_area, 2) . ' sq ft' : __('Not specified')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Total Units')); ?>:</strong></td>
                                    <td><?php echo e($project->total_units ?? __('Not specified')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Price per Sq Ft')); ?>:</strong></td>
                                    <td><?php echo e($project->price_per_sqft ? currency_format_with_sym($project->price_per_sqft) : __('Not specified')); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><?php echo e(__('Project Manager')); ?>:</strong></td>
                                    <td><?php echo e($project->projectManager->name ?? __('Not assigned')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Channel Partner')); ?>:</strong></td>
                                    <td><?php echo e($project->channelPartner->name ?? __('Not assigned')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Current Phase')); ?>:</strong></td>
                                    <td><?php echo e($project->current_phase ?? __('Not specified')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(__('Last Updated')); ?>:</strong></td>
                                    <td><?php echo e($project->last_updated_at ? $project->last_updated_at->format('M d, Y H:i') : __('Never')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php if($project->description): ?>
                        <div class="mt-3">
                            <strong><?php echo e(__('Description')); ?>:</strong>
                            <p class="mt-2"><?php echo e($project->description); ?></p>
                        </div>
                    <?php endif; ?>
                    <?php if($project->notes): ?>
                        <div class="mt-3">
                            <strong><?php echo e(__('Notes')); ?>:</strong>
                            <p class="mt-2"><?php echo e($project->notes); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><?php echo e(__('Timeline')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><?php echo e(__('Planned Timeline')); ?></h6>
                            <p>
                                <strong><?php echo e(__('Start')); ?>:</strong> <?php echo e($project->planned_start_date ? $project->planned_start_date->format('M d, Y') : __('Not set')); ?><br>
                                <strong><?php echo e(__('End')); ?>:</strong> <?php echo e($project->planned_end_date ? $project->planned_end_date->format('M d, Y') : __('Not set')); ?>

                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6><?php echo e(__('Actual Timeline')); ?></h6>
                            <p>
                                <strong><?php echo e(__('Start')); ?>:</strong> <?php echo e($project->actual_start_date ? $project->actual_start_date->format('M d, Y') : __('Not started')); ?><br>
                                <strong><?php echo e(__('End')); ?>:</strong> <?php echo e($project->actual_end_date ? $project->actual_end_date->format('M d, Y') : __('Not completed')); ?>

                            </p>
                        </div>
                    </div>
                    <?php if($project->planned_start_date && $project->planned_end_date): ?>
                        <div class="progress mt-3">
                            <div class="progress-bar" role="progressbar" style="width: <?php echo e($project->progress_percentage); ?>%">
                                <?php echo e($project->progress_percentage); ?>%
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <small class="text-muted"><?php echo e($project->planned_start_date->format('M d, Y')); ?></small>
                            <small class="text-muted"><?php echo e($project->planned_end_date->format('M d, Y')); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Budget Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><?php echo e(__('Budget Information')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted"><?php echo e(__('Total Budget')); ?></h6>
                                <h4><?php echo e($project->budget ? currency_format_with_sym($project->budget) : __('Not set')); ?></h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted"><?php echo e(__('Allocated')); ?></h6>
                                <h4><?php echo e($project->allocated_budget ? currency_format_with_sym($project->allocated_budget) : __('Not set')); ?></h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted"><?php echo e(__('Spent')); ?></h6>
                                <h4><?php echo e(currency_format_with_sym($project->spent_budget)); ?></h4>
                            </div>
                        </div>
                    </div>
                    <?php if($project->allocated_budget): ?>
                        <div class="progress mt-3">
                            <div class="progress-bar bg-<?php echo e($project->budget_utilization > 90 ? 'danger' : ($project->budget_utilization > 75 ? 'warning' : 'success')); ?>" 
                                 role="progressbar" style="width: <?php echo e(min($project->budget_utilization, 100)); ?>%">
                                <?php echo e($project->budget_utilization); ?>%
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <small class="text-muted"><?php echo e(__('Remaining')); ?>: <?php echo e(currency_format_with_sym($project->remaining_budget)); ?></small>
                            <small class="text-muted"><?php echo e($project->budget_utilization); ?>% <?php echo e(__('utilized')); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><?php echo e(__('Quick Actions')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage milestones')): ?>
                            <a href="#" class="btn btn-outline-primary">
                                <i class="ti ti-target me-2"></i><?php echo e(__('Manage Milestones')); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage project documents')): ?>
                            <a href="#" class="btn btn-outline-secondary">
                                <i class="ti ti-files me-2"></i><?php echo e(__('Documents')); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage project resources')): ?>
                            <a href="#" class="btn btn-outline-info">
                                <i class="ti ti-users me-2"></i><?php echo e(__('Resources')); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view project reports')): ?>
                            <a href="#" class="btn btn-outline-success">
                                <i class="ti ti-chart-line me-2"></i><?php echo e(__('Reports')); ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Recent Activities')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if(isset($project->activities) && $project->activities->count() > 0): ?>
                        <div class="timeline">
                            <?php $__currentLoopData = $project->activities->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1"><?php echo e($activity->title); ?></h6>
                                        <?php if($activity->description): ?>
                                            <p class="text-muted mb-1"><?php echo e($activity->description); ?></p>
                                        <?php endif; ?>
                                        <small class="text-muted">
                                            <?php echo e($activity->user->name ?? __('System')); ?> • 
                                            <?php echo e($activity->activity_date->diffForHumans()); ?>

                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center"><?php echo e(__('No activities yet')); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/projects/show.blade.php ENDPATH**/ ?>