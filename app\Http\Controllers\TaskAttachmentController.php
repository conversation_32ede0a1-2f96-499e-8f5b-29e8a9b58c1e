<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\TaskAttachment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class TaskAttachmentController extends Controller
{
    /**
     * Store a new task attachment
     */
    public function store(Request $request, $taskId)
    {
        if (!Gate::check('upload task attachments')) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $task = Task::find($taskId);
        if (!$task) {
            return response()->json(['success' => false, 'message' => 'Task not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
            'description' => 'nullable|string|max:255',
            'is_public' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $filename = Str::uuid() . '.' . $extension;
            
            // Store file in task-specific directory
            $filePath = $file->storeAs("tasks/{$taskId}/attachments", $filename, 'public');

            $attachment = TaskAttachment::create([
                'task_id' => $taskId,
                'uploaded_by' => Auth::id(),
                'filename' => $filename,
                'original_filename' => $originalName,
                'file_path' => $filePath,
                'file_type' => $extension,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'description' => $request->description,
                'is_public' => $request->boolean('is_public', false)
            ]);

            // Add comment to task
            $task->addComment(
                "File attached: {$originalName}",
                'system',
                ['attachment_id' => $attachment->id, 'filename' => $originalName]
            );

            $attachment->load('uploadedBy');

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'attachment' => $attachment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download task attachment
     */
    public function download($taskId, $attachmentId)
    {
        if (!Gate::check('download task attachments')) {
            abort(403, 'Permission denied');
        }

        $task = Task::find($taskId);
        if (!$task) {
            abort(404, 'Task not found');
        }

        $attachment = TaskAttachment::where('task_id', $taskId)
                                   ->where('id', $attachmentId)
                                   ->first();

        if (!$attachment) {
            abort(404, 'Attachment not found');
        }

        if (!Storage::disk('public')->exists($attachment->file_path)) {
            abort(404, 'File not found on disk');
        }

        return Storage::disk('public')->download(
            $attachment->file_path,
            $attachment->original_filename
        );
    }

    /**
     * Delete task attachment
     */
    public function destroy($taskId, $attachmentId)
    {
        if (!Gate::check('delete task attachments')) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $task = Task::find($taskId);
        if (!$task) {
            return response()->json(['success' => false, 'message' => 'Task not found'], 404);
        }

        $attachment = TaskAttachment::where('task_id', $taskId)
                                   ->where('id', $attachmentId)
                                   ->first();

        if (!$attachment) {
            return response()->json(['success' => false, 'message' => 'Attachment not found'], 404);
        }

        try {
            // Delete file from storage
            if (Storage::disk('public')->exists($attachment->file_path)) {
                Storage::disk('public')->delete($attachment->file_path);
            }

            // Add comment to task
            $task->addComment(
                "File deleted: {$attachment->original_filename}",
                'system',
                ['attachment_id' => $attachment->id, 'filename' => $attachment->original_filename]
            );

            // Delete attachment record
            $attachment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Attachment deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete attachment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * View attachment (for images and PDFs)
     */
    public function view($taskId, $attachmentId)
    {
        if (!Gate::check('view task attachments')) {
            abort(403, 'Permission denied');
        }

        $task = Task::find($taskId);
        if (!$task) {
            abort(404, 'Task not found');
        }

        $attachment = TaskAttachment::where('task_id', $taskId)
                                   ->where('id', $attachmentId)
                                   ->first();

        if (!$attachment) {
            abort(404, 'Attachment not found');
        }

        if (!$attachment->canBeViewedInBrowser()) {
            return redirect()->route('tasks.attachments.download', [$taskId, $attachmentId]);
        }

        if (!Storage::disk('public')->exists($attachment->file_path)) {
            abort(404, 'File not found on disk');
        }

        $fileContents = Storage::disk('public')->get($attachment->file_path);
        
        return response($fileContents)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Disposition', 'inline; filename="' . $attachment->original_filename . '"');
    }

    /**
     * Update attachment details
     */
    public function update(Request $request, $taskId, $attachmentId)
    {
        if (!Gate::check('edit task attachments')) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        $task = Task::find($taskId);
        if (!$task) {
            return response()->json(['success' => false, 'message' => 'Task not found'], 404);
        }

        $attachment = TaskAttachment::where('task_id', $taskId)
                                   ->where('id', $attachmentId)
                                   ->first();

        if (!$attachment) {
            return response()->json(['success' => false, 'message' => 'Attachment not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'description' => 'nullable|string|max:255',
            'is_public' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $attachment->update($request->only(['description', 'is_public']));

        return response()->json([
            'success' => true,
            'message' => 'Attachment updated successfully',
            'attachment' => $attachment
        ]);
    }
}
