<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Task Management')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Task Management')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create tasks')): ?>
            <a href="#" data-url="<?php echo e(route('tasks.create')); ?>" data-size="lg" data-ajax-popup="true" 
               data-title="<?php echo e(__('Create New Task')); ?>" data-bs-toggle="tooltip" title="<?php echo e(__('Create')); ?>" 
               class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i>
            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Task Statistics -->
        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Total Tasks')); ?></h6>
                            <h3 class="text-primary"><?php echo e($stats['total']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-primary-light ti ti-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('In Progress')); ?></h6>
                            <h3 class="text-warning"><?php echo e($stats['in_progress']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-warning-light ti ti-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Overdue')); ?></h6>
                            <h3 class="text-danger"><?php echo e($stats['overdue']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-danger-light ti ti-alert-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20"><?php echo e(__('Completed')); ?></h6>
                            <h3 class="text-success"><?php echo e($stats['completed']); ?></h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-success-light ti ti-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <div class="row">
                        <div class="col-lg-9">
                            <h5><?php echo e(__('Tasks')); ?></h5>
                        </div>
                        <div class="col-lg-3">
                            <div class="float-end">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create tasks')): ?>
                                    <a href="#" data-url="<?php echo e(route('tasks.create')); ?>" data-size="lg" 
                                       data-ajax-popup="true" data-title="<?php echo e(__('Create New Task')); ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="ti ti-plus"></i> <?php echo e(__('Create Task')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('tasks.index')); ?>">
                        <div class="row">
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value=""><?php echo e(__('All Status')); ?></option>
                                    <?php $__currentLoopData = \App\Models\Task::$statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(request('status') == $key ? 'selected' : ''); ?>>
                                            <?php echo e(__($status)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="priority" class="form-control">
                                    <option value=""><?php echo e(__('All Priority')); ?></option>
                                    <?php $__currentLoopData = \App\Models\Task::$priorities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $priority): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(request('priority') == $key ? 'selected' : ''); ?>>
                                            <?php echo e(__($priority)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="category" class="form-control">
                                    <option value=""><?php echo e(__('All Categories')); ?></option>
                                    <?php $__currentLoopData = \App\Models\Task::$categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(request('category') == $key ? 'selected' : ''); ?>>
                                            <?php echo e(__($category)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="assigned_to" class="form-control">
                                    <option value=""><?php echo e(__('All Assignees')); ?></option>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>" <?php echo e(request('assigned_to') == $user->id ? 'selected' : ''); ?>>
                                            <?php echo e($user->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="<?php echo e(__('Search tasks...')); ?>" value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Tasks Table -->
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Task')); ?></th>
                                    <th><?php echo e(__('Category')); ?></th>
                                    <th><?php echo e(__('Priority')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Assigned To')); ?></th>
                                    <th><?php echo e(__('Due Date')); ?></th>
                                    <th><?php echo e(__('Related')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($task->title); ?></strong>
                                                <?php if($task->description): ?>
                                                    <br><small class="text-muted"><?php echo e(Str::limit($task->description, 50)); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e($task->category_label); ?></span>
                                        </td>
                                        <td>
                                            <?php
                                                $priorityColors = [
                                                    'low' => 'success',
                                                    'medium' => 'warning', 
                                                    'high' => 'danger',
                                                    'urgent' => 'dark'
                                                ];
                                            ?>
                                            <span class="badge bg-<?php echo e($priorityColors[$task->priority] ?? 'secondary'); ?>">
                                                <?php echo e($task->priority_label); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                                $statusColors = [
                                                    'not_started' => 'secondary',
                                                    'in_progress' => 'warning',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger',
                                                    'overdue' => 'danger'
                                                ];
                                            ?>
                                            <span class="badge bg-<?php echo e($statusColors[$task->status] ?? 'secondary'); ?>">
                                                <?php echo e($task->status_label); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($task->assignedTo): ?>
                                                <?php echo e($task->assignedTo->name); ?>

                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($task->due_date): ?>
                                                <span class="<?php echo e($task->is_overdue ? 'text-danger' : ''); ?>">
                                                    <?php echo e($task->due_date->format('M d, Y')); ?>

                                                </span>
                                                <?php if($task->is_overdue): ?>
                                                    <br><small class="text-danger"><?php echo e(__('Overdue')); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('No due date')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($task->lead): ?>
                                                <small class="text-primary"><?php echo e(__('Lead')); ?>: <?php echo e($task->lead->name); ?></small>
                                            <?php elseif($task->project): ?>
                                                <small class="text-info"><?php echo e(__('Project')); ?>: <?php echo e($task->project->name); ?></small>
                                            <?php elseif($task->property): ?>
                                                <small class="text-success"><?php echo e(__('Property')); ?>: <?php echo e($task->property->name); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('General')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="Action">
                                            <span>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view tasks')): ?>
                                                    <div class="action-btn bg-warning ms-2">
                                                        <a href="<?php echo e(route('tasks.show', $task->id)); ?>" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           data-bs-toggle="tooltip" title="<?php echo e(__('View')); ?>">
                                                            <i class="ti ti-eye text-white"></i>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit tasks')): ?>
                                                    <div class="action-btn bg-info ms-2">
                                                        <a href="#" data-url="<?php echo e(route('tasks.edit', $task->id)); ?>" 
                                                           data-size="lg" data-ajax-popup="true" 
                                                           data-title="<?php echo e(__('Edit Task')); ?>" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           data-bs-toggle="tooltip" title="<?php echo e(__('Edit')); ?>">
                                                            <i class="ti ti-pencil text-white"></i>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete tasks')): ?>
                                                    <div class="action-btn bg-danger ms-2">
                                                        <?php echo Form::open(['method' => 'DELETE', 'route' => ['tasks.destroy', $task->id], 'id' => 'delete-form-' . $task->id]); ?>

                                                        <a href="#" class="mx-3 btn btn-sm align-items-center bs-pass-para" 
                                                           data-bs-toggle="tooltip" title="<?php echo e(__('Delete')); ?>">
                                                            <i class="ti ti-trash text-white text-white"></i>
                                                        </a>
                                                        <?php echo Form::close(); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="card-footer">
                    <?php echo e($tasks->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        $(document).ready(function() {
            // Auto-submit form on filter change
            $('select[name="status"], select[name="priority"], select[name="category"], select[name="assigned_to"]').change(function() {
                $(this).closest('form').submit();
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/tasks/index.blade.php ENDPATH**/ ?>