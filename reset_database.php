<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'calling_agent_crm',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    echo "🔄 Starting database reset...\n";
    
    // Get all tables
    $tables = $capsule->getConnection()->select('SHOW TABLES');
    $tableNames = [];
    foreach ($tables as $table) {
        $tableNames[] = array_values((array)$table)[0];
    }
    
    if (!empty($tableNames)) {
        echo "📋 Found " . count($tableNames) . " tables to drop\n";
        
        // Disable foreign key checks
        $capsule->getConnection()->statement('SET FOREIGN_KEY_CHECKS = 0');

        // Drop all tables
        foreach ($tableNames as $tableName) {
            try {
                $capsule->getConnection()->statement("DROP TABLE IF EXISTS `{$tableName}`");
                echo "🗑️ Dropped table: {$tableName}\n";
            } catch (Exception $e) {
                echo "⚠️ Could not drop table {$tableName}: " . $e->getMessage() . "\n";
            }
        }

        // Re-enable foreign key checks
        $capsule->getConnection()->statement('SET FOREIGN_KEY_CHECKS = 1');
        
        echo "✅ All tables dropped successfully\n";
    } else {
        echo "ℹ️ No tables found to drop\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
