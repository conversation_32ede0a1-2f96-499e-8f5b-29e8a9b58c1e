{{ Form::model($task, ['route' => ['tasks.update', $task->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data']) }}
<div class="modal-body">
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <!-- Task Title -->
                        <div class="form-group col-md-12">
                            {{ Form::label('title', __('Task Title'), ['class' => 'form-label']) }}
                            {{ Form::text('title', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter task title')]) }}
                        </div>

                        <!-- Task Description -->
                        <div class="form-group col-md-12">
                            {{ Form::label('description', __('Description'), ['class' => 'form-label']) }}
                            {{ Form::textarea('description', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter task description')]) }}
                        </div>

                        <!-- Category and Priority -->
                        <div class="form-group col-md-6">
                            {{ Form::label('category', __('Category'), ['class' => 'form-label']) }}
                            {{ Form::select('category', \App\Models\Task::$categories, null, ['class' => 'form-control select2', 'required' => 'required']) }}
                        </div>

                        <div class="form-group col-md-6">
                            {{ Form::label('priority', __('Priority'), ['class' => 'form-label']) }}
                            {{ Form::select('priority', \App\Models\Task::$priorities, null, ['class' => 'form-control select2', 'required' => 'required']) }}
                        </div>

                        <!-- Status -->
                        <div class="form-group col-md-6">
                            {{ Form::label('status', __('Status'), ['class' => 'form-label']) }}
                            {{ Form::select('status', \App\Models\Task::$statuses, null, ['class' => 'form-control select2', 'required' => 'required']) }}
                        </div>

                        <!-- Due Date -->
                        <div class="form-group col-md-6">
                            {{ Form::label('due_date', __('Due Date'), ['class' => 'form-label']) }}
                            {{ Form::datetime('due_date', null, ['class' => 'form-control', 'placeholder' => __('Select due date')]) }}
                        </div>

                        <!-- Estimated and Actual Hours -->
                        <div class="form-group col-md-6">
                            {{ Form::label('estimated_hours', __('Estimated Hours'), ['class' => 'form-label']) }}
                            {{ Form::number('estimated_hours', null, ['class' => 'form-control', 'min' => 1, 'placeholder' => __('Enter estimated hours')]) }}
                        </div>

                        <div class="form-group col-md-6">
                            {{ Form::label('actual_hours', __('Actual Hours'), ['class' => 'form-label']) }}
                            {{ Form::number('actual_hours', null, ['class' => 'form-control', 'min' => 1, 'placeholder' => __('Enter actual hours')]) }}
                        </div>

                        <!-- Assigned To -->
                        <div class="form-group col-md-12">
                            {{ Form::label('assigned_to', __('Assign To'), ['class' => 'form-label']) }}
                            {{ Form::select('assigned_to', $users->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select user to assign')]) }}
                        </div>

                        <!-- Related Entity Selection -->
                        <div class="form-group col-md-12">
                            <label class="form-label">{{ __('Link to Related Entity') }}</label>
                            <div class="row">
                                <div class="col-md-4">
                                    {{ Form::label('lead_id', __('Lead'), ['class' => 'form-label']) }}
                                    {{ Form::select('lead_id', $leads->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select lead')]) }}
                                </div>
                                <div class="col-md-4">
                                    {{ Form::label('project_id', __('Project'), ['class' => 'form-label']) }}
                                    {{ Form::select('project_id', $projects->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select project')]) }}
                                </div>
                                <div class="col-md-4">
                                    {{ Form::label('property_id', __('Property'), ['class' => 'form-label']) }}
                                    {{ Form::select('property_id', $properties->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select property')]) }}
                                </div>
                            </div>
                            <small class="text-muted">{{ __('You can link this task to a specific lead, project, or property (optional)') }}</small>
                        </div>

                        <!-- Completion Notes (only show if task is completed or cancelled) -->
                        @if(in_array($task->status, ['completed', 'cancelled']))
                            <div class="form-group col-md-12">
                                {{ Form::label('completion_notes', __('Completion Notes'), ['class' => 'form-label']) }}
                                {{ Form::textarea('completion_notes', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter completion notes')]) }}
                            </div>
                        @endif

                        <!-- Task Progress Information -->
                        <div class="form-group col-md-12">
                            <div class="alert alert-info">
                                <h6>{{ __('Task Progress Information') }}</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>{{ __('Created:') }}</strong> {{ $task->created_at->format('M d, Y H:i') }}<br>
                                        <strong>{{ __('Created By:') }}</strong> {{ $task->createdBy->name }}
                                    </div>
                                    <div class="col-md-6">
                                        @if($task->started_at)
                                            <strong>{{ __('Started:') }}</strong> {{ $task->started_at->format('M d, Y H:i') }}<br>
                                        @endif
                                        @if($task->completed_at)
                                            <strong>{{ __('Completed:') }}</strong> {{ $task->completed_at->format('M d, Y H:i') }}<br>
                                        @endif
                                        @if($task->cancelled_at)
                                            <strong>{{ __('Cancelled:') }}</strong> {{ $task->cancelled_at->format('M d, Y H:i') }}<br>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" onclick="location.href = '{{ route('tasks.show', $task->id) }}';" class="btn btn-light">
    <input type="submit" value="{{ __('Update') }}" class="btn btn-primary">
</div>
{{ Form::close() }}

<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            placeholder: function() {
                return $(this).data('placeholder');
            },
            allowClear: true,
            width: '100%'
        });

        // Clear other related entity selections when one is selected
        $('#lead_id').change(function() {
            if ($(this).val()) {
                $('#project_id, #property_id').val(null).trigger('change');
            }
        });

        $('#project_id').change(function() {
            if ($(this).val()) {
                $('#lead_id, #property_id').val(null).trigger('change');
            }
        });

        $('#property_id').change(function() {
            if ($(this).val()) {
                $('#lead_id, #project_id').val(null).trigger('change');
            }
        });

        // Auto-set category based on related entity
        $('#lead_id').change(function() {
            if ($(this).val()) {
                $('#category').val('lead_task').trigger('change');
            }
        });

        $('#project_id').change(function() {
            if ($(this).val()) {
                $('#category').val('project_task').trigger('change');
            }
        });

        $('#property_id').change(function() {
            if ($(this).val()) {
                $('#category').val('property_task').trigger('change');
            }
        });

        // Show/hide completion notes based on status
        $('#status').change(function() {
            var status = $(this).val();
            var completionNotesGroup = $('textarea[name="completion_notes"]').closest('.form-group');
            
            if (status === 'completed' || status === 'cancelled') {
                if (completionNotesGroup.length === 0) {
                    // Add completion notes field if it doesn't exist
                    var completionNotesHtml = `
                        <div class="form-group col-md-12">
                            <label class="form-label">{{ __('Completion Notes') }}</label>
                            <textarea name="completion_notes" class="form-control" rows="3" placeholder="{{ __('Enter completion notes') }}"></textarea>
                        </div>
                    `;
                    $('.card-body .row').append(completionNotesHtml);
                }
            } else {
                completionNotesGroup.remove();
            }
        });
    });
</script>
