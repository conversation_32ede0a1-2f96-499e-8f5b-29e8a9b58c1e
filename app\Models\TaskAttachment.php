<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class TaskAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'uploaded_by',
        'filename',
        'original_filename',
        'file_path',
        'file_type',
        'file_size',
        'mime_type',
        'description',
        'is_public'
    ];

    protected $casts = [
        'file_size' => 'integer',
        'is_public' => 'boolean'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set uploaded_by when creating
        static::creating(function ($model) {
            if (Auth::check() && !$model->uploaded_by) {
                $model->uploaded_by = Auth::user()->id;
            }
        });

        // Delete file when model is deleted
        static::deleting(function ($model) {
            if ($model->file_path && Storage::exists($model->file_path)) {
                Storage::delete($model->file_path);
            }
        });
    }

    // Relationships
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('file_type', $type);
    }

    public function scopeImages($query)
    {
        return $query->where('mime_type', 'like', 'image/%');
    }

    public function scopeDocuments($query)
    {
        return $query->whereIn('file_type', ['pdf', 'doc', 'docx', 'txt', 'rtf']);
    }

    public function scopeSpreadsheets($query)
    {
        return $query->whereIn('file_type', ['xls', 'xlsx', 'csv']);
    }

    // Accessors
    public function getFileSizeHumanAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getDownloadUrlAttribute()
    {
        return route('tasks.attachments.download', ['task' => $this->task_id, 'attachment' => $this->id]);
    }

    public function getIsImageAttribute()
    {
        return strpos($this->mime_type, 'image/') === 0;
    }

    public function getIsDocumentAttribute()
    {
        return in_array($this->file_type, ['pdf', 'doc', 'docx', 'txt', 'rtf']);
    }

    public function getIsSpreadsheetAttribute()
    {
        return in_array($this->file_type, ['xls', 'xlsx', 'csv']);
    }

    public function getIconClassAttribute()
    {
        if ($this->is_image) {
            return 'fas fa-image text-success';
        } elseif ($this->is_document) {
            return 'fas fa-file-alt text-primary';
        } elseif ($this->is_spreadsheet) {
            return 'fas fa-file-excel text-success';
        } elseif ($this->file_type == 'pdf') {
            return 'fas fa-file-pdf text-danger';
        } else {
            return 'fas fa-file text-secondary';
        }
    }

    // Business Logic Methods
    public function makePublic()
    {
        $this->update(['is_public' => true]);
        return $this;
    }

    public function makePrivate()
    {
        $this->update(['is_public' => false]);
        return $this;
    }

    public function getFileContents()
    {
        if (!Storage::exists($this->file_path)) {
            return null;
        }

        return Storage::get($this->file_path);
    }

    public function getFileUrl()
    {
        if (!Storage::exists($this->file_path)) {
            return null;
        }

        return Storage::url($this->file_path);
    }

    public function canBeViewedInBrowser()
    {
        $viewableTypes = ['pdf', 'txt', 'csv'];
        $viewableMimes = ['image/', 'text/', 'application/pdf'];

        if (in_array($this->file_type, $viewableTypes)) {
            return true;
        }

        foreach ($viewableMimes as $mime) {
            if (strpos($this->mime_type, $mime) === 0) {
                return true;
            }
        }

        return false;
    }
}
