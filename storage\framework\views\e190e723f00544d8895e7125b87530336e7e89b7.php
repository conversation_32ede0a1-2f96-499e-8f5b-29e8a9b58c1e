<?php $__env->startSection('page-title'); ?>
    <?php echo e($form->title); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('forms.index')); ?>"><?php echo e(__('Forms')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e($form->title); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('form_builder_edit')): ?>
            <a href="<?php echo e(route('forms.edit', $form->id)); ?>" class="btn btn-sm btn-primary">
                <i class="ti ti-pencil"></i> <?php echo e(__('Edit Form')); ?>

            </a>
        <?php endif; ?>
        <?php if($form->status === 'published'): ?>
            <a href="<?php echo e($form->public_url); ?>" target="_blank" class="btn btn-sm btn-success">
                <i class="ti ti-external-link"></i> <?php echo e(__('View Public')); ?>

            </a>
        <?php endif; ?>
        <a href="<?php echo e(route('forms.index')); ?>" class="btn btn-sm btn-secondary">
            <i class="ti ti-arrow-left"></i> <?php echo e(__('Back')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Form Statistics -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-primary">
                                    <i class="ti ti-eye"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Total Views')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['view_count']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-success">
                                    <i class="ti ti-send"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Submissions')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['total_submissions']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-info">
                                    <i class="ti ti-percentage"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Conversion Rate')); ?></small>
                                    <h6 class="m-0"><?php echo e(number_format($stats['conversion_rate'], 1)); ?>%</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-warning">
                                    <i class="ti ti-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Last Submission')); ?></small>
                                    <h6 class="m-0">
                                        <?php if($stats['last_submission']): ?>
                                            <?php echo e($stats['last_submission']->diffForHumans()); ?>

                                        <?php else: ?>
                                            <?php echo e(__('Never')); ?>

                                        <?php endif; ?>
                                    </h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Form Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Form Details')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label"><?php echo e(__('Title')); ?></label>
                                <p class="form-control-static"><?php echo e($form->title); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label"><?php echo e(__('Status')); ?></label>
                                <span class="badge bg-<?php echo e($form->status_badge); ?>"><?php echo e(ucfirst($form->status)); ?></span>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label class="form-label"><?php echo e(__('Description')); ?></label>
                                <p class="form-control-static"><?php echo e($form->description ?: __('No description provided')); ?></p>
                            </div>
                        </div>
                        <?php if($form->status === 'published'): ?>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="form-label"><?php echo e(__('Public URL')); ?></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="<?php echo e($form->public_url); ?>" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo e($form->public_url); ?>')">
                                            <i class="ti ti-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Form Preview -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Form Preview')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="form-preview">
                        <h4><?php echo e($form->title); ?></h4>
                        <?php if($form->description): ?>
                            <p class="text-muted"><?php echo e($form->description); ?></p>
                        <?php endif; ?>
                        
                        <?php if($form->fields->count() > 0): ?>
                            <form>
                                <?php $__currentLoopData = $form->fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <?php echo e($field->label); ?>

                                            <?php if($field->is_required): ?>
                                                <span class="text-danger">*</span>
                                            <?php endif; ?>
                                        </label>
                                        <?php echo $field->generateHtml(); ?>

                                        <?php if($field->help_text): ?>
                                            <small class="form-text text-muted"><?php echo e($field->help_text); ?></small>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <button type="submit" class="btn btn-primary" disabled><?php echo e(__('Submit')); ?></button>
                            </form>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="ti ti-forms text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3"><?php echo e(__('No fields added')); ?></h5>
                                <p class="text-muted"><?php echo e(__('Add fields to this form to see the preview')); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions & Info -->
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Quick Actions')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('form_builder_edit')): ?>
                            <a href="<?php echo e(route('forms.edit', $form->id)); ?>" class="btn btn-primary">
                                <i class="ti ti-pencil"></i> <?php echo e(__('Edit Form')); ?>

                            </a>
                        <?php endif; ?>
                        
                        <?php if($form->status === 'published'): ?>
                            <a href="<?php echo e($form->public_url); ?>" target="_blank" class="btn btn-success">
                                <i class="ti ti-external-link"></i> <?php echo e(__('View Public Form')); ?>

                            </a>
                            
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('public_forms_embed')): ?>
                                <button type="button" class="btn btn-outline-primary" onclick="showEmbedCode()">
                                    <i class="ti ti-code"></i> <?php echo e(__('Get Embed Code')); ?>

                                </button>
                            <?php endif; ?>
                            
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('public_forms_qr_code')): ?>
                                <a href="<?php echo e(route('public.forms.qr-code', $form->slug)); ?>" target="_blank" class="btn btn-outline-secondary">
                                    <i class="ti ti-qrcode"></i> <?php echo e(__('QR Code')); ?>

                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('form_builder_duplicate')): ?>
                            <button type="button" class="btn btn-outline-info" onclick="duplicateForm()">
                                <i class="ti ti-copy"></i> <?php echo e(__('Duplicate Form')); ?>

                            </button>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('form_builder_export')): ?>
                            <a href="<?php echo e(route('forms.export', $form->id)); ?>" class="btn btn-outline-warning">
                                <i class="ti ti-download"></i> <?php echo e(__('Export Form')); ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Form Information -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Form Information')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <small class="text-muted"><?php echo e(__('Created')); ?></small>
                            <p><?php echo e($form->formatted_created_at); ?></p>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo e(__('Created By')); ?></small>
                            <p><?php echo e($form->creator->name ?? __('Unknown')); ?></p>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo e(__('Total Fields')); ?></small>
                            <p><?php echo e($form->fields->count()); ?></p>
                        </div>
                        <div class="col-12">
                            <small class="text-muted"><?php echo e(__('Form Slug')); ?></small>
                            <p><code><?php echo e($form->slug); ?></code></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if($form->submissions->count() > 0): ?>
        <!-- Recent Submissions -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="mb-0"><?php echo e(__('Recent Submissions')); ?></h5>
                            </div>
                            <div class="col-6 text-end">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('form_submissions_view')): ?>
                                    <a href="<?php echo e(route('form-submissions.index', ['form_id' => $form->id])); ?>" class="btn btn-sm btn-primary">
                                        <?php echo e(__('View All Submissions')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Contact')); ?></th>
                                        <th><?php echo e(__('Status')); ?></th>
                                        <th><?php echo e(__('Submitted')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $form->submissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $submission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo e($submission->contact_name ?: __('Unknown')); ?></strong>
                                                    <?php if($submission->contact_email): ?>
                                                        <br><small class="text-muted"><?php echo e($submission->contact_email); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($submission->status_badge); ?>"><?php echo e(ucfirst($submission->status)); ?></span>
                                            </td>
                                            <td><?php echo e($submission->formatted_created_at); ?></td>
                                            <td>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('form_submissions_view')): ?>
                                                    <a href="<?php echo e(route('form-submissions.show', $submission->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            show_toastr('Success', 'URL copied to clipboard!', 'success');
        });
    }

    function showEmbedCode() {
        // This would open a modal with embed code options
        alert('Embed code functionality would be implemented here');
    }

    function duplicateForm() {
        if (confirm('Are you sure you want to duplicate this form?')) {
            $.ajax({
                url: '<?php echo e(route("forms.duplicate", $form->id)); ?>',
                type: 'POST',
                data: {
                    '_token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.status === 'success') {
                        show_toastr('Success', response.messages, 'success');
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                    } else {
                        show_toastr('Error', response.messages, 'error');
                    }
                },
                error: function(xhr) {
                    show_toastr('Error', 'Something went wrong', 'error');
                }
            });
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/forms/show.blade.php ENDPATH**/ ?>