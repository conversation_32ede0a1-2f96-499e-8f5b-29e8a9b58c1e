<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\Task;
use App\Models\Lead;
use App\Models\Project;
use App\Models\Property;

echo "=== DATABASE SETUP VERIFICATION ===\n\n";

echo "ROLES CREATED:\n";
$roles = Role::all();
foreach ($roles as $role) {
    $permissionCount = $role->permissions()->count();
    echo "- {$role->name} ({$permissionCount} permissions)\n";
}

echo "\nUSERS BY TYPE:\n";
$userTypes = User::select('type', \DB::raw('count(*) as count'))
                 ->groupBy('type')
                 ->get();
foreach ($userTypes as $userType) {
    echo "- {$userType->type}: {$userType->count} users\n";
}

echo "\nDATA SUMMARY:\n";
echo "- Total Users: " . User::count() . "\n";
echo "- Total Roles: " . Role::count() . "\n";
echo "- Total Permissions: " . Permission::count() . "\n";
echo "- Total Tasks: " . Task::count() . "\n";
echo "- Total Leads: " . Lead::count() . "\n";
echo "- Total Projects: " . Project::count() . "\n";
echo "- Total Properties: " . Property::count() . "\n";

echo "\nTASK MANAGEMENT PERMISSIONS:\n";
$taskPermissions = Permission::where('name', 'like', '%task%')->get();
echo "- Task-related permissions: " . $taskPermissions->count() . "\n";
foreach ($taskPermissions as $permission) {
    echo "  * {$permission->name}\n";
}

echo "\nALL PERMISSIONS CONTAINING 'TASK':\n";
$allTaskPerms = Permission::where('name', 'like', '%task%')->orWhere('name', 'like', '%Task%')->get();
echo "- Found: " . $allTaskPerms->count() . "\n";
foreach ($allTaskPerms as $permission) {
    echo "  * {$permission->name}\n";
}

echo "\n=== SETUP COMPLETED SUCCESSFULLY! ===\n";
