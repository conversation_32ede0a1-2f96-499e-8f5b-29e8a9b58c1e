<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Lead;
use App\Models\CallLog;
use App\Models\FollowUp;
use App\Models\CallQueue;
use App\Models\AgentPerformanceMetric;
use App\Models\LeadActivity;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class EnhancedRealisticDataSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker::create('en_IN');
        
        $this->command->info('Creating enhanced realistic data for leads and calling system...');
        
        // Get owner user
        $owner = User::where('type', 'owner')->first();
        if (!$owner) {
            $this->command->error('Owner user not found. Please run basic seeders first.');
            return;
        }

        // Create additional agents if needed
        $this->createAdditionalAgents($faker, $owner);
        
        // Create 50 realistic leads
        $this->createRealisticLeads($faker, $owner);
        
        // Create 50 realistic call logs
        $this->createRealisticCallLogs($faker);
        
        // Create follow-ups
        $this->createFollowUps($faker);
        
        // Create call queue entries
        $this->createCallQueueEntries($faker);
        
        // Create agent performance metrics
        $this->createAgentPerformanceMetrics($faker);
        
        // Create lead activities
        $this->createLeadActivities($faker);
        
        $this->command->info('Enhanced realistic data created successfully!');
    }

    private function createAdditionalAgents($faker, $owner)
    {
        $this->command->info('Creating additional agents...');
        
        $agentNames = [
            ['name' => 'Ravi Sharma', 'email' => '<EMAIL>'],
            ['name' => 'Meera Gupta', 'email' => '<EMAIL>'],
            ['name' => 'Karan Singh', 'email' => '<EMAIL>'],
            ['name' => 'Anita Verma', 'email' => '<EMAIL>'],
            ['name' => 'Suresh Kumar', 'email' => '<EMAIL>'],
            ['name' => 'Deepika Rao', 'email' => '<EMAIL>'],
            ['name' => 'Manoj Patel', 'email' => '<EMAIL>'],
            ['name' => 'Sunita Joshi', 'email' => '<EMAIL>'],
        ];

        foreach ($agentNames as $agent) {
            $existingUser = User::where('email', $agent['email'])->first();
            
            if (!$existingUser) {
                $user = User::create([
                    'name' => $agent['name'],
                    'email' => $agent['email'],
                    'password' => Hash::make('password123'),
                    'type' => 'agent',
                    'parent_id' => $owner->id,
                    'phone_number' => '+91' . $faker->numerify('##########'),
                    'is_active' => 1,
                    'email_verified_at' => now(),
                    'created_at' => now()->subDays(rand(1, 60)),
                    'updated_at' => now()
                ]);
                
                $user->assignRole('agent');
                $this->command->info("Created agent: {$agent['name']}");
            }
        }
    }

    private function createRealisticLeads($faker, $owner)
    {
        $this->command->info('Creating 50 realistic leads...');
        
        $agents = User::where('type', 'agent')->where('parent_id', $owner->id)->get();
        $leadSources = ['Website', 'Facebook', 'Google Ads', 'Referral', 'Cold Call', 'WhatsApp', 'Instagram', 'Walk-in'];
        $stages = ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost'];
        $priorities = ['low', 'medium', 'high', 'urgent'];
        $temperatures = ['cold', 'warm', 'hot'];
        $budgetRanges = [
            ['min' => 500000, 'max' => 1000000],
            ['min' => 1000000, 'max' => 2500000],
            ['min' => 2500000, 'max' => 5000000],
            ['min' => 5000000, 'max' => 10000000],
            ['min' => 10000000, 'max' => 25000000],
        ];

        $indianNames = [
            'Rajesh Kumar', 'Priya Sharma', 'Amit Singh', 'Sneha Patel', 'Vikram Reddy',
            'Kavya Iyer', 'Rohit Agarwal', 'Pooja Jain', 'Arjun Mehta', 'Divya Nair',
            'Sunil Gupta', 'Ritu Verma', 'Manoj Yadav', 'Neha Kapoor', 'Sanjay Mishra',
            'Anita Rao', 'Deepak Joshi', 'Meera Bansal', 'Rahul Saxena', 'Sunita Agarwal',
            'Kiran Kumar', 'Shweta Singh', 'Naveen Sharma', 'Rekha Patel', 'Ajay Gupta',
            'Nisha Verma', 'Ravi Yadav', 'Geeta Kapoor', 'Suresh Mishra', 'Kavita Rao',
            'Manish Joshi', 'Preeti Bansal', 'Anil Saxena', 'Sushma Agarwal', 'Vinod Kumar',
            'Rashmi Singh', 'Prakash Sharma', 'Usha Patel', 'Ramesh Gupta', 'Seema Verma',
            'Ashok Yadav', 'Lata Kapoor', 'Dinesh Mishra', 'Vandana Rao', 'Mukesh Joshi',
            'Asha Bansal', 'Rajendra Saxena', 'Kamala Agarwal', 'Harish Kumar', 'Sudha Singh'
        ];

        $cities = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata', 'Hyderabad', 'Pune', 'Ahmedabad', 'Jaipur', 'Lucknow'];
        $propertyTypes = ['Apartment', 'Villa', 'Plot', 'Commercial Space', 'Office', 'Warehouse', 'Retail Shop'];

        for ($i = 0; $i < 50; $i++) {
            $budget = $faker->randomElement($budgetRanges);
            $createdAt = $faker->dateTimeBetween('-90 days', 'now');
            
            Lead::create([
                'name' => $faker->randomElement($indianNames),
                'email' => $faker->unique()->safeEmail,
                'phone' => '+91' . $faker->numerify('##########'),
                'alternate_phone' => rand(0, 1) ? '+91' . $faker->numerify('##########') : null,
                'source' => $faker->randomElement($leadSources),
                'stage' => $faker->randomElement($stages),
                'status' => $faker->randomElement(['new', 'contacted', 'interested', 'not_interested', 'converted', 'lost']),
                'priority' => $faker->randomElement($priorities),
                'temperature' => $faker->randomElement($temperatures),
                'score' => rand(0, 10), // Score is 0-10 based on migration
                'score_grade' => $this->getScoreGrade(rand(0, 10)),
                'budget_min' => $budget['min'],
                'budget_max' => $budget['max'],
                'requirements' => $faker->randomElement($propertyTypes) . ' in ' . $faker->randomElement($cities),
                'property_preferences' => json_encode([
                    'type' => $faker->randomElement($propertyTypes),
                    'location' => $faker->randomElement($cities),
                    'bedrooms' => rand(1, 4),
                    'area_sqft' => rand(500, 3000)
                ]),
                'address' => $faker->address,
                'city' => $faker->randomElement($cities),
                'state' => $faker->randomElement(['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'West Bengal']),
                'pincode' => $faker->numerify('######'),
                'assigned_to' => $agents->random()->id,
                'notes' => $faker->paragraph,
                'last_contacted_at' => $faker->dateTimeBetween($createdAt, 'now'),
                'next_followup_at' => $faker->dateTimeBetween('now', '+30 days'), // Correct column name
                'converted_at' => rand(0, 1) ? $faker->dateTimeBetween($createdAt, 'now') : null,
                'created_at' => $createdAt,
                'updated_at' => $faker->dateTimeBetween($createdAt, 'now'),
            ]);
        }
        
        $this->command->info('Created 50 realistic leads');
    }

    private function getScoreGrade($score)
    {
        if ($score >= 8) return 'A';
        if ($score >= 6) return 'B';
        if ($score >= 4) return 'C';
        if ($score >= 2) return 'D';
        return 'F';
    }

    private function createRealisticCallLogs($faker)
    {
        $this->command->info('Creating 50 realistic call logs...');
        
        $leads = Lead::all();
        $agents = User::where('type', 'agent')->get();
        $callTypes = ['outgoing', 'incoming'];
        $callDirections = ['inbound', 'outbound'];
        $callStatuses = ['completed', 'answered', 'busy', 'no_answer', 'failed', 'voicemail'];
        $callOutcomes = ['connected', 'not_interested', 'interested', 'callback_requested', 'follow_up_needed', 'converted', 'invalid_number'];
        $callQualities = ['excellent', 'good', 'fair', 'poor'];

        for ($i = 0; $i < 50; $i++) {
            $lead = $leads->random();
            $agent = $agents->random();
            $callStarted = $faker->dateTimeBetween('-30 days', 'now');
            $duration = rand(30, 1800); // 30 seconds to 30 minutes
            $talkDuration = rand(10, $duration - 10);
            
            CallLog::create([
                'call_id' => 'CALL_' . time() . '_' . uniqid(),
                'call_type' => $faker->randomElement($callTypes),
                'call_direction' => $faker->randomElement($callDirections),
                'phone_number' => $lead->phone,
                'contact_name' => $lead->name,
                'lead_id' => $lead->id,
                'user_id' => $agent->id,
                'call_started_at' => $callStarted,
                'call_ended_at' => Carbon::parse($callStarted)->addSeconds($duration),
                'duration_seconds' => $duration,
                'talk_duration_seconds' => $talkDuration,
                'call_status' => $faker->randomElement($callStatuses),
                'call_outcome' => $faker->randomElement($callOutcomes),
                'call_notes' => $faker->paragraph,
                'agent_notes' => $faker->sentence,
                'call_quality_score' => rand(1, 10),
                'call_rating' => $faker->randomElement($callQualities),
                'requires_follow_up' => rand(0, 1),
                'created_at' => $callStarted,
                'updated_at' => $callStarted,
            ]);
        }
        
        $this->command->info('Created 50 realistic call logs');
    }

    private function createFollowUps($faker)
    {
        $this->command->info('Creating follow-ups...');

        $leads = Lead::all();
        $agents = User::where('type', 'agent')->get();
        $followUpTypes = ['call', 'email', 'whatsapp', 'meeting', 'site_visit', 'document_send', 'quote_send', 'demo'];
        $followUpStatuses = ['pending', 'in_progress', 'completed', 'cancelled', 'rescheduled', 'overdue', 'failed'];
        $priorities = ['low', 'medium', 'high', 'urgent'];

        $followUpTitles = [
            'Follow up on property inquiry',
            'Send property brochure and floor plans',
            'Schedule site visit',
            'Discuss pricing and payment plans',
            'Send loan assistance information',
            'Arrange meeting with sales team',
            'Provide project updates',
            'Clarify property specifications',
            'Share virtual tour link',
            'Discuss special offers and discounts'
        ];

        for ($i = 0; $i < 30; $i++) {
            $lead = $leads->random();
            $agent = $agents->random();
            $createdAt = $faker->dateTimeBetween('-30 days', 'now');

            FollowUp::create([
                'follow_up_id' => 'FU_' . time() . '_' . uniqid(),
                'lead_id' => $lead->id,
                'agent_id' => $agent->id,
                'created_by' => $agent->id,
                'follow_up_type' => $faker->randomElement($followUpTypes),
                'title' => $faker->randomElement($followUpTitles),
                'description' => $faker->paragraph,
                'scheduled_at' => $faker->dateTimeBetween('now', '+30 days'),
                'priority' => $faker->randomElement($priorities),
                'status' => $faker->randomElement($followUpStatuses),
                'notes' => $faker->paragraph,
                'created_at' => $createdAt,
                'updated_at' => $faker->dateTimeBetween($createdAt, 'now'),
            ]);
        }

        $this->command->info('Created 30 follow-ups');
    }

    private function createCallQueueEntries($faker)
    {
        $this->command->info('Creating call queue entries...');

        $leads = Lead::where('stage', '!=', 'closed_won')->where('stage', '!=', 'closed_lost')->get();
        $agents = User::where('type', 'agent')->get();
        $priorities = ['low', 'medium', 'high', 'urgent'];
        $statuses = ['queued', 'in_progress', 'completed', 'skipped', 'failed', 'cancelled', 'rescheduled'];

        for ($i = 0; $i < 25; $i++) {
            $lead = $leads->random();
            $agent = $agents->random();
            $scheduledFor = $faker->dateTimeBetween('now', '+7 days');
            $createdAt = $faker->dateTimeBetween('-7 days', 'now');

            CallQueue::create([
                'queue_id' => 'QUEUE_' . time() . '_' . uniqid(),
                'tenant_id' => $agent->parent_id ?? $agent->id,
                'lead_id' => $lead->id,
                'agent_id' => $agent->id,
                'created_by' => $agent->id,
                'phone_number' => $lead->phone,
                'contact_name' => $lead->name,
                'priority' => $faker->randomElement($priorities),
                'scheduled_for' => $scheduledFor,
                'status' => $faker->randomElement($statuses),
                'contact_attempts' => rand(0, 3),
                'retry_count' => rand(0, 2),
                'completion_notes' => $faker->sentence,
                'created_at' => $createdAt,
                'updated_at' => now(),
            ]);
        }

        $this->command->info('Created 25 call queue entries');
    }

    private function createAgentPerformanceMetrics($faker)
    {
        $this->command->info('Creating agent performance metrics...');

        $agents = User::where('type', 'agent')->get();

        foreach ($agents as $agent) {
            // Create metrics for last 30 days
            for ($day = 0; $day < 30; $day++) {
                $date = Carbon::now()->subDays($day);

                AgentPerformanceMetric::updateOrCreate([
                    'tenant_id' => $agent->parent_id ?? $agent->id,
                    'agent_id' => $agent->id,
                    'metric_date' => $date->format('Y-m-d'),
                    'metric_period' => 'daily'
                ], [
                    'period_start' => $date->format('Y-m-d'),
                    'period_end' => $date->format('Y-m-d'),
                    'total_calls' => rand(5, 25),
                    'outbound_calls' => rand(3, 20),
                    'inbound_calls' => rand(1, 5),
                    'connected_calls' => rand(3, 20),
                    'total_talk_time_minutes' => rand(30, 300),
                    'leads_contacted' => rand(2, 15),
                    'leads_converted' => rand(0, 3),
                    'follow_ups_completed' => rand(1, 8),
                    'appointments_scheduled' => rand(0, 5),
                    'revenue_generated' => rand(0, 500000),
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);
            }
        }

        $this->command->info('Created agent performance metrics for 30 days');
    }

    private function createLeadActivities($faker)
    {
        $this->command->info('Creating lead activities...');

        $leads = Lead::all();
        $agents = User::where('type', 'agent')->get();
        $activityTypes = ['call', 'email', 'meeting', 'note', 'status_change', 'stage_change', 'assignment', 'follow_up'];

        foreach ($leads as $lead) {
            // Create 3-8 activities per lead
            $activityCount = rand(3, 8);

            for ($i = 0; $i < $activityCount; $i++) {
                $activityDate = $faker->dateTimeBetween($lead->created_at, 'now');

                LeadActivity::create([
                    'lead_id' => $lead->id,
                    'user_id' => $agents->random()->id,
                    'activity_type' => $faker->randomElement($activityTypes),
                    'title' => $this->getActivityTitle($faker->randomElement($activityTypes)),
                    'description' => $faker->paragraph,
                    'activity_date' => $activityDate,
                    'metadata' => json_encode([
                        'duration' => rand(5, 60),
                        'outcome' => $faker->randomElement(['positive', 'neutral', 'negative']),
                        'next_action' => $faker->sentence
                    ]),
                    'created_at' => $activityDate,
                    'updated_at' => $activityDate,
                ]);
            }
        }

        $this->command->info('Created lead activities');
    }

    private function getActivityTitle($type)
    {
        $titles = [
            'call' => 'Phone call with prospect',
            'email' => 'Email sent to prospect',
            'meeting' => 'Meeting scheduled',
            'note' => 'Added note to lead',
            'status_change' => 'Status updated',
            'stage_change' => 'Stage changed',
            'assignment' => 'Lead assigned',
            'follow_up' => 'Follow-up completed'
        ];

        return $titles[$type] ?? 'Activity completed';
    }
}
