@extends('layouts.app')

@section('page-title')
    {{ __('Enhanced Dashboard') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item" aria-current="page">{{ __('Dashboard') }}</li>
@endsection

@push('css-page')
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #2ca58d 0%, #0a2342 100%);
        --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        --info-gradient: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        --card-shadow: 0 8px 25px rgba(0,0,0,0.15);
        --hover-shadow: 0 15px 35px rgba(0,0,0,0.25);
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        box-shadow: var(--card-shadow);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        position: relative;
    }

    .enhanced-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--hover-shadow);
    }

    .stat-card-modern {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .icon-gradient {
        background: var(--primary-gradient);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .chart-container {
        min-height: 400px;
        position: relative;
    }

    .progress-ring {
        width: 120px;
        height: 120px;
    }

    .progress-ring circle {
        stroke-width: 8;
        fill: transparent;
        r: 52;
        cx: 60;
        cy: 60;
    }

    .progress-ring .bg {
        stroke: #e9ecef;
    }

    .progress-ring .progress {
        stroke: #2ca58d;
        stroke-linecap: round;
        transform: rotate(-90deg);
        transform-origin: 60px 60px;
        transition: stroke-dasharray 0.5s ease;
    }

    .metric-card {
        background: var(--primary-gradient);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .table-modern {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    .table-modern th {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .table-modern td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #f8f9fa;
    }

    .badge-modern {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .section-header {
        background: var(--primary-gradient);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px 15px 0 0;
        margin: 0;
    }

    .insight-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        position: relative;
    }

    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-neutral { color: #6c757d; }

    .mini-chart {
        height: 60px;
        margin-top: 10px;
    }
</style>
@endpush

@push('script-page')
<script>
    // Main Income/Expense Chart
    var incomeExpenseOptions = {
        chart: {
            type: 'area',
            height: 450,
            toolbar: { show: false },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#2ca58d', '#dc3545', '#ffc107'],
        dataLabels: { enabled: false },
        legend: {
            show: true,
            position: 'top',
            fontSize: '14px',
            fontWeight: 600
        },
        markers: {
            size: 6,
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: { size: 8 }
        },
        stroke: {
            width: 3,
            curve: 'smooth'
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                type: 'vertical',
                opacityFrom: 0.7,
                opacityTo: 0.1
            }
        },
        grid: {
            show: true,
            borderColor: '#e7e7e7',
            strokeDashArray: 3
        },
        series: [
            {
                name: "{{ __('Total Income') }}",
                data: {!! json_encode($result['incomeExpense']['income'] ?? []) !!}
            },
            {
                name: "{{ __('Total Expense') }}",
                data: {!! json_encode($result['incomeExpense']['expense'] ?? []) !!}
            }
        ],
        xaxis: {
            categories: {!! json_encode($result['incomeExpense']['label'] ?? []) !!},
            labels: {
                style: { fontSize: '12px', fontWeight: 500 }
            }
        },
        yaxis: {
            labels: {
                formatter: function(value) {
                    return '{{ $result['settings']['currency'] ?? '$' }}' + value.toFixed(0);
                }
            }
        },
        tooltip: {
            theme: 'dark',
            y: {
                formatter: function(value) {
                    return '{{ $result['settings']['currency'] ?? '$' }}' + value.toFixed(2);
                }
            }
        }
    };
    var incomeExpenseChart = new ApexCharts(document.querySelector('#incomeExpenseByMonth'), incomeExpenseOptions);
    incomeExpenseChart.render();

    // Lead Conversion Funnel Chart
    var funnelOptions = {
        chart: {
            type: 'bar',
            height: 350,
            toolbar: { show: false }
        },
        plotOptions: {
            bar: {
                horizontal: true,
                distributed: true,
                barHeight: '70%'
            }
        },
        colors: ['#2ca58d', '#17a2b8', '#ffc107', '#dc3545'],
        series: [{
            data: [
                { x: 'Total Leads', y: {{ $result['leads']['total'] ?? 0 }} },
                { x: 'Contacted', y: {{ (int)($result['leads']['total'] * 0.7) }} },
                { x: 'Interested', y: {{ (int)($result['leads']['total'] * 0.4) }} },
                { x: 'Converted', y: {{ $result['leads']['converted'] ?? 0 }} }
            ]
        }],
        xaxis: {
            labels: {
                formatter: function(value) {
                    return Math.round(value);
                }
            }
        },
        title: {
            text: 'Lead Conversion Funnel',
            align: 'center',
            style: { fontSize: '18px', fontWeight: 'bold' }
        }
    };
    var funnelChart = new ApexCharts(document.querySelector('#leadFunnelChart'), funnelOptions);
    funnelChart.render();

    // Task Distribution Pie Chart
    @if(isset($result['tasks']))
    var taskOptions = {
        chart: {
            type: 'donut',
            height: 350
        },
        series: [
            {{ $result['tasks']['not_started'] ?? 0 }},
            {{ $result['tasks']['in_progress'] ?? 0 }},
            {{ $result['tasks']['completed'] ?? 0 }},
            {{ $result['tasks']['overdue'] ?? 0 }}
        ],
        labels: ['Not Started', 'In Progress', 'Completed', 'Overdue'],
        colors: ['#6c757d', '#17a2b8', '#28a745', '#dc3545'],
        legend: {
            position: 'bottom'
        },
        title: {
            text: 'Task Distribution',
            align: 'center',
            style: { fontSize: '18px', fontWeight: 'bold' }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '60%',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total Tasks',
                            fontSize: '16px',
                            fontWeight: 'bold'
                        }
                    }
                }
            }
        }
    };
    var taskChart = new ApexCharts(document.querySelector('#taskDistributionChart'), taskOptions);
    taskChart.render();
    @endif

    // Performance Metrics Chart
    @if(isset($result['calling']))
    var performanceOptions = {
        chart: {
            type: 'radialBar',
            height: 350
        },
        series: [
            {{ $result['calling']['connected_calls'] > 0 ? round(($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100) : 0 }},
            {{ $result['leads']['converted'] > 0 ? round(($result['leads']['converted'] / $result['leads']['total']) * 100) : 0 }}
        ],
        colors: ['#2ca58d', '#17a2b8'],
        labels: ['Call Success Rate', 'Lead Conversion Rate'],
        plotOptions: {
            radialBar: {
                size: 160,
                track: {
                    background: '#e7e7e7',
                    strokeWidth: '97%',
                    margin: 15
                },
                dataLabels: {
                    name: {
                        fontSize: '14px',
                        fontWeight: 'bold'
                    },
                    value: {
                        fontSize: '16px',
                        fontWeight: 'bold',
                        formatter: function(val) {
                            return val + '%';
                        }
                    }
                }
            }
        }
    };
    var performanceChart = new ApexCharts(document.querySelector('#performanceChart'), performanceOptions);
    performanceChart.render();
    @endif

    // Real-time updates simulation
    setInterval(function() {
        // Add sparkle effect to cards
        document.querySelectorAll('.enhanced-card').forEach(function(card) {
            if (Math.random() > 0.8) {
                card.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                }, 200);
            }
        });
    }, 5000);

    // Initialize progress rings
    function updateProgressRing(selector, percentage) {
        const circle = document.querySelector(selector + ' .progress');
        if (circle) {
            const radius = circle.r.baseVal.value;
            const circumference = radius * 2 * Math.PI;
            const offset = circumference - (percentage / 100) * circumference;
            
            circle.style.strokeDasharray = circumference;
            circle.style.strokeDashoffset = offset;
        }
    }

    // Update progress rings with animation
    setTimeout(() => {
        updateProgressRing('#conversionRate', {{ $result['leads']['converted'] > 0 ? round(($result['leads']['converted'] / $result['leads']['total']) * 100) : 0 }});
        updateProgressRing('#taskCompletion', {{ isset($result['tasks']) && $result['tasks']['total'] > 0 ? round(($result['tasks']['completed'] / $result['tasks']['total']) * 100) : 0 }});
    }, 1000);
</script>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Enhanced Header with Key Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="mb-1 text-primary fw-bold">{{ __('Welcome Back!') }}</h2>
                            <p class="text-muted mb-0">{{ __('Here\'s what\'s happening with your business today.') }}</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex justify-content-end align-items-center">
                                <div class="me-4">
                                    <small class="text-muted">{{ __('Today') }}</small>
                                    <h6 class="mb-0">{{ now()->format('M d, Y') }}</h6>
                                </div>
                                <div class="icon-gradient">
                                    <i class="ti ti-dashboard"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3">
                        <i class="ti ti-users"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-primary">{{ $result['totalUser'] ?? 0 }}</h3>
                    <p class="text-muted mb-0">{{ __('Total Users') }}</p>
                    <div class="mini-chart">
                        <canvas id="userTrend"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3" style="background: var(--success-gradient);">
                        <i class="ti ti-package"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-success">{{ $result['totalClient'] ?? 0 }}</h3>
                    <p class="text-muted mb-0">{{ __('Total Clients') }}</p>
                    <div class="mini-chart">
                        <canvas id="clientTrend"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3" style="background: var(--info-gradient);">
                        <i class="ti ti-currency-dollar"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-info">{{ priceFormat($result['totalIncome'] ?? 0) }}</h3>
                    <p class="text-muted mb-0">{{ __('Total Income') }}</p>
                    <small class="trend-up"><i class="ti ti-trending-up"></i> +12.5%</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3" style="background: var(--warning-gradient);">
                        <i class="ti ti-credit-card"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-warning">{{ priceFormat($result['totalExpense'] ?? 0) }}</h3>
                    <p class="text-muted mb-0">{{ __('Total Expense') }}</p>
                    <small class="trend-down"><i class="ti ti-trending-down"></i> -5.2%</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Main Income/Expense Chart -->
        <div class="col-lg-8 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-chart-line me-2"></i>{{ __('Financial Analysis') }}</h5>
                </div>
                <div class="card-body">
                    <div id="incomeExpenseByMonth"></div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="col-lg-4 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-target me-2"></i>{{ __('Performance') }}</h5>
                </div>
                <div class="card-body">
                    @if(isset($result['calling']))
                    <div id="performanceChart"></div>
                    @else
                    <div class="text-center py-5">
                        <i class="ti ti-chart-pie text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">{{ __('Performance data will appear here') }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Lead Management Enhanced Section -->
    @if(isset($result['leads']))
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-user-check me-2"></i>{{ __('Lead Management Analytics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="row">
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card">
                                        <h4 class="mb-1">{{ $result['leads']['total'] }}</h4>
                                        <small>{{ __('Total Leads') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card" style="background: var(--success-gradient);">
                                        <h4 class="mb-1">{{ $result['leads']['new_today'] }}</h4>
                                        <small>{{ __('New Today') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card" style="background: var(--info-gradient);">
                                        <h4 class="mb-1">{{ $result['leads']['converted'] }}</h4>
                                        <small>{{ __('Converted') }}</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card" style="background: var(--danger-gradient);">
                                        <h4 class="mb-1">{{ $result['leads']['hot_leads'] }}</h4>
                                        <small>{{ __('Hot Leads') }}</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Conversion Rate Progress -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <svg class="progress-ring" id="conversionRate">
                                            <circle class="bg" />
                                            <circle class="progress" />
                                        </svg>
                                        <h6 class="mt-2">{{ __('Conversion Rate') }}</h6>
                                        <p class="text-muted">{{ $result['leads']['converted'] > 0 ? round(($result['leads']['converted'] / $result['leads']['total']) * 100, 1) : 0 }}%</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>{{ __('Lead Quality Insights') }}</h6>
                                    <div class="mb-2">
                                        <small class="text-muted">{{ __('Hot Leads Ratio') }}</small>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-danger" style="width: {{ $result['leads']['total'] > 0 ? ($result['leads']['hot_leads'] / $result['leads']['total']) * 100 : 0 }}%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">{{ __('Daily Growth') }}</small>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: 75%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div id="leadFunnelChart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Task Management Enhanced Section -->
    @if(isset($result['tasks']) && (Gate::check('manage tasks') || Gate::check('view tasks')))
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-check-square me-2"></i>{{ __('Task Management Overview') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="ti ti-list"></i>
                                </div>
                                <h4 class="mb-1 text-primary">{{ $result['tasks']['total'] }}</h4>
                                <small class="text-muted">{{ __('Total Tasks') }}</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--warning-gradient);">
                                    <i class="ti ti-clock"></i>
                                </div>
                                <h4 class="mb-1 text-warning">{{ $result['tasks']['in_progress'] }}</h4>
                                <small class="text-muted">{{ __('In Progress') }}</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--danger-gradient);">
                                    <i class="ti ti-alert-triangle"></i>
                                </div>
                                <h4 class="mb-1 text-danger">{{ $result['tasks']['overdue'] }}</h4>
                                <small class="text-muted">{{ __('Overdue') }}</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--success-gradient);">
                                    <i class="ti ti-check"></i>
                                </div>
                                <h4 class="mb-1 text-success">{{ $result['tasks']['completed'] }}</h4>
                                <small class="text-muted">{{ __('Completed') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Task Insights -->
                    <div class="row mt-4">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="insight-card">
                                <div class="d-flex align-items-center">
                                    <div class="icon-gradient me-3" style="width: 40px; height: 40px; font-size: 1rem; background: var(--info-gradient);">
                                        <i class="ti ti-calendar-event"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0 text-info">{{ $result['tasks']['due_today'] }}</h4>
                                        <small class="text-muted">{{ __('Due Today') }}</small>
                                    </div>
                                </div>
                                @if($result['tasks']['due_today'] > 0)
                                <div class="mt-2">
                                    <span class="badge bg-info">{{ __('Focus Required') }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="insight-card">
                                <div class="d-flex align-items-center">
                                    <div class="icon-gradient me-3" style="width: 40px; height: 40px; font-size: 1rem; background: var(--warning-gradient);">
                                        <i class="ti ti-flag"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0 text-warning">{{ $result['tasks']['high_priority'] }}</h4>
                                        <small class="text-muted">{{ __('High Priority') }}</small>
                                    </div>
                                </div>
                                @if($result['tasks']['high_priority'] > 0)
                                <div class="mt-2">
                                    <span class="badge bg-warning">{{ __('Urgent') }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="insight-card">
                                <div class="d-flex align-items-center">
                                    <div class="icon-gradient me-3" style="width: 40px; height: 40px; font-size: 1rem; background: var(--secondary-gradient);">
                                        <i class="ti ti-calendar-week"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0 text-secondary">{{ $result['tasks']['due_this_week'] }}</h4>
                                        <small class="text-muted">{{ __('This Week') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="insight-card">
                                <div class="d-flex align-items-center">
                                    <div class="icon-gradient me-3" style="width: 40px; height: 40px; font-size: 1rem; background: var(--primary-gradient);">
                                        <i class="ti ti-user"></i>
                                    </div>
                                    <div>
                                        <h4 class="mb-0 text-primary">{{ $result['tasks']['assigned_to_me'] }}</h4>
                                        <small class="text-muted">{{ __('Assigned to Me') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Task Performance Metrics -->
                    @if(isset($result['tasks']['completion_rate']) || isset($result['tasks']['productivity_trends']))
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="insight-card">
                                <h6 class="mb-3">{{ __('Completion Rate') }}</h6>
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: {{ $result['tasks']['completion_rate'] ?? 0 }}%"></div>
                                        </div>
                                    </div>
                                    <span class="ms-3 fw-bold text-success">{{ $result['tasks']['completion_rate'] ?? 0 }}%</span>
                                </div>
                                <small class="text-muted mt-2 d-block">{{ __('Overall task completion performance') }}</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="insight-card">
                                <h6 class="mb-3">{{ __('Task Distribution') }}</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="text-info">
                                            <h5 class="mb-0">{{ $result['tasks']['not_started'] }}</h5>
                                            <small>{{ __('Pending') }}</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-warning">
                                            <h5 class="mb-0">{{ $result['tasks']['in_progress'] }}</h5>
                                            <small>{{ __('Active') }}</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-success">
                                            <h5 class="mb-0">{{ $result['tasks']['completed'] }}</h5>
                                            <small>{{ __('Done') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-chart-pie me-2"></i>{{ __('Task Distribution') }}</h5>
                </div>
                <div class="card-body">
                    <div id="taskDistributionChart"></div>
                    
                    <!-- Task Completion Progress -->
                    <div class="text-center mt-4">
                        <svg class="progress-ring" id="taskCompletion">
                            <circle class="bg" />
                            <circle class="progress" stroke="#28a745" />
                        </svg>
                        <h6 class="mt-2">{{ __('Completion Rate') }}</h6>
                        <p class="text-muted">{{ $result['tasks']['total'] > 0 ? round(($result['tasks']['completed'] / $result['tasks']['total']) * 100, 1) : 0 }}%</p>
                    </div>

                    <!-- Task Quick Actions -->
                    <div class="mt-4">
                        <h6 class="mb-3">{{ __('Quick Actions') }}</h6>
                        <div class="d-grid gap-2">
                            @can('create tasks')
                            <a href="{{ route('tasks.create') }}" class="btn btn-primary btn-sm">
                                <i class="ti ti-plus me-2"></i>{{ __('Create Task') }}
                            </a>
                            @endcan
                            @can('view tasks')
                            <a href="{{ route('tasks.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="ti ti-list me-2"></i>{{ __('View All Tasks') }}
                            </a>
                            <a href="{{ route('tasks.calendar') }}" class="btn btn-outline-info btn-sm">
                                <i class="ti ti-calendar me-2"></i>{{ __('Calendar View') }}
                            </a>
                            <a href="{{ route('tasks.analytics') }}" class="btn btn-outline-success btn-sm">
                                <i class="ti ti-chart-bar me-2"></i>{{ __('Analytics') }}
                            </a>
                            @endcan
                        </div>
                    </div>

                    <!-- Task Alerts -->
                    @if($result['tasks']['overdue'] > 0 || $result['tasks']['due_today'] > 0)
                    <div class="mt-4">
                        <h6 class="mb-3 text-warning">{{ __('Attention Required') }}</h6>
                        @if($result['tasks']['overdue'] > 0)
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="ti ti-alert-triangle me-2"></i>
                            <strong>{{ $result['tasks']['overdue'] }}</strong> {{ __('tasks are overdue') }}
                            <a href="{{ route('tasks.index', ['filter' => 'overdue']) }}" class="alert-link">{{ __('View Now') }}</a>
                        </div>
                        @endif
                        @if($result['tasks']['due_today'] > 0)
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="ti ti-calendar-event me-2"></i>
                            <strong>{{ $result['tasks']['due_today'] }}</strong> {{ __('tasks due today') }}
                            <a href="{{ route('tasks.index', ['filter' => 'due_today']) }}" class="alert-link">{{ __('View Now') }}</a>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Calling System Analytics -->
    @if(isset($result['calling']))
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-phone me-2"></i>{{ __('Calling System Analytics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <i class="ti ti-phone mb-2" style="font-size: 2rem;"></i>
                                <h4>{{ $result['calling']['total_calls'] }}</h4>
                                <small>{{ __('Total Calls') }}</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--info-gradient);">
                                <i class="ti ti-phone-call mb-2" style="font-size: 2rem;"></i>
                                <h4>{{ $result['calling']['calls_today'] }}</h4>
                                <small>{{ __('Calls Today') }}</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--success-gradient);">
                                <i class="ti ti-phone-check mb-2" style="font-size: 2rem;"></i>
                                <h4>{{ $result['calling']['connected_calls'] }}</h4>
                                <small>{{ __('Connected Calls') }}</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--warning-gradient);">
                                <i class="ti ti-clock mb-2" style="font-size: 2rem;"></i>
                                <h4>{{ gmdate('i:s', $result['calling']['avg_call_duration']) }}</h4>
                                <small>{{ __('Avg Duration') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Call Analytics -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>{{ __('Call Performance Metrics') }}</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small>{{ __('Success Rate') }}</small>
                                    <small>{{ $result['calling']['total_calls'] > 0 ? round(($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100, 1) : 0 }}%</small>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-success" style="width: {{ $result['calling']['total_calls'] > 0 ? ($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100 : 0 }}%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small>{{ __('Daily Target') }}</small>
                                    <small>{{ $result['calling']['calls_today'] }}/50</small>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-info" style="width: {{ ($result['calling']['calls_today'] / 50) * 100 }}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ __('Call Quality Insights') }}</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">{{ __('Average Call Duration') }}</span>
                                <span class="badge badge-modern bg-primary">{{ gmdate('i:s', $result['calling']['avg_call_duration']) }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">{{ __('Connection Rate') }}</span>
                                <span class="badge badge-modern bg-success">{{ $result['calling']['total_calls'] > 0 ? round(($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100, 1) : 0 }}%</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">{{ __('Today\'s Activity') }}</span>
                                <span class="badge badge-modern bg-info">{{ $result['calling']['calls_today'] }} {{ __('calls') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Projects and Properties Row -->
    <div class="row mb-4">
        <!-- Project Management Stats -->
        @if(isset($result['projects']))
        <div class="col-lg-6 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-briefcase me-2"></i>{{ __('Project Management') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="ti ti-briefcase"></i>
                                </div>
                                <h4 class="mb-1 text-primary">{{ $result['projects']['total'] }}</h4>
                                <small class="text-muted">{{ __('Total Projects') }}</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--success-gradient);">
                                    <i class="ti ti-circle-check"></i>
                                </div>
                                <h4 class="mb-1 text-success">{{ $result['projects']['active'] }}</h4>
                                <small class="text-muted">{{ __('Active') }}</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--info-gradient);">
                                    <i class="ti ti-check"></i>
                                </div>
                                <h4 class="mb-1 text-info">{{ $result['projects']['completed'] }}</h4>
                                <small class="text-muted">{{ __('Completed') }}</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--warning-gradient);">
                                    <i class="ti ti-pause"></i>
                                </div>
                                <h4 class="mb-1 text-warning">{{ $result['projects']['on_hold'] }}</h4>
                                <small class="text-muted">{{ __('On Hold') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Project Progress -->
                    <div class="mt-3">
                        <h6>{{ __('Project Progress Overview') }}</h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>{{ __('Completion Rate') }}</small>
                                <small>{{ $result['projects']['total'] > 0 ? round(($result['projects']['completed'] / $result['projects']['total']) * 100, 1) : 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: {{ $result['projects']['total'] > 0 ? ($result['projects']['completed'] / $result['projects']['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>{{ __('Active Rate') }}</small>
                                <small>{{ $result['projects']['total'] > 0 ? round(($result['projects']['active'] / $result['projects']['total']) * 100, 1) : 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-primary" style="width: {{ $result['projects']['total'] > 0 ? ($result['projects']['active'] / $result['projects']['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Property Management Stats -->
        @if(isset($result['properties']))
        <div class="col-lg-6 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-home me-2"></i>{{ __('Property Management') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="ti ti-home"></i>
                                </div>
                                <h4 class="mb-1 text-primary">{{ $result['properties']['total'] }}</h4>
                                <small class="text-muted">{{ __('Total Properties') }}</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--success-gradient);">
                                    <i class="ti ti-circle-check"></i>
                                </div>
                                <h4 class="mb-1 text-success">{{ $result['properties']['available'] }}</h4>
                                <small class="text-muted">{{ __('Available') }}</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--info-gradient);">
                                    <i class="ti ti-check"></i>
                                </div>
                                <h4 class="mb-1 text-info">{{ $result['properties']['sold'] }}</h4>
                                <small class="text-muted">{{ __('Sold') }}</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--warning-gradient);">
                                    <i class="ti ti-clock"></i>
                                </div>
                                <h4 class="mb-1 text-warning">{{ $result['properties']['reserved'] }}</h4>
                                <small class="text-muted">{{ __('Reserved') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Property Insights -->
                    <div class="mt-3">
                        <h6>{{ __('Property Market Overview') }}</h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>{{ __('Sales Rate') }}</small>
                                <small>{{ $result['properties']['total'] > 0 ? round(($result['properties']['sold'] / $result['properties']['total']) * 100, 1) : 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: {{ $result['properties']['total'] > 0 ? ($result['properties']['sold'] / $result['properties']['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>{{ __('Availability Rate') }}</small>
                                <small>{{ $result['properties']['total'] > 0 ? round(($result['properties']['available'] / $result['properties']['total']) * 100, 1) : 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: {{ $result['properties']['total'] > 0 ? ($result['properties']['available'] / $result['properties']['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="row mb-4">
        <!-- Recent Activities Table -->
        <div class="col-lg-8 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-activity me-2"></i>{{ __('Recent Activities') }}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern mb-0">
                            <thead>
                                <tr>
                                    <th>{{ __('Activity') }}</th>
                                    <th>{{ __('Type') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Time') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($result['leads']) && $result['leads']['new_today'] > 0)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                                <i class="ti ti-user-plus"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $result['leads']['new_today'] }} {{ __('new leads added') }}</h6>
                                                <small class="text-muted">{{ __('Lead generation') }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-primary">{{ __('Lead') }}</span></td>
                                    <td><span class="badge badge-modern bg-success">{{ __('Active') }}</span></td>
                                    <td><small class="text-muted">{{ __('Today') }}</small></td>
                                </tr>
                                @endif
                                
                                @if(isset($result['calling']) && $result['calling']['calls_today'] > 0)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: var(--info-gradient);">
                                                <i class="ti ti-phone"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $result['calling']['calls_today'] }} {{ __('calls made today') }}</h6>
                                                <small class="text-muted">{{ __('Calling activity') }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-info">{{ __('Call') }}</span></td>
                                    <td><span class="badge badge-modern bg-success">{{ __('Completed') }}</span></td>
                                    <td><small class="text-muted">{{ __('Today') }}</small></td>
                                </tr>
                                @endif

                                @if(isset($result['tasks']) && $result['tasks']['completed'] > 0)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: var(--success-gradient);">
                                                <i class="ti ti-check"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $result['tasks']['completed'] }} {{ __('tasks completed') }}</h6>
                                                <small class="text-muted">{{ __('Task management') }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-success">{{ __('Task') }}</span></td>
                                    <td><span class="badge badge-modern bg-success">{{ __('Completed') }}</span></td>
                                    <td><small class="text-muted">{{ __('Recent') }}</small></td>
                                </tr>
                                @endif

                                @if(isset($result['projects']) && $result['projects']['active'] > 0)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: var(--warning-gradient);">
                                                <i class="ti ti-briefcase"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $result['projects']['active'] }} {{ __('projects in progress') }}</h6>
                                                <small class="text-muted">{{ __('Project management') }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-warning">{{ __('Project') }}</span></td>
                                    <td><span class="badge badge-modern bg-warning">{{ __('In Progress') }}</span></td>
                                    <td><small class="text-muted">{{ __('Ongoing') }}</small></td>
                                </tr>
                                @endif

                                @if(!isset($result['leads']) && !isset($result['calling']) && !isset($result['tasks']) && !isset($result['projects']))
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <i class="ti ti-inbox text-muted" style="font-size: 3rem;"></i>
                                        <p class="text-muted mt-2">{{ __('No recent activities found') }}</p>
                                    </td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions and Insights -->
        <div class="col-lg-4 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-zap me-2"></i>{{ __('Quick Actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @can('create leads')
                        <a href="{{ route('leads.create') }}" class="btn btn-primary btn-lg">
                            <i class="ti ti-user-plus me-2"></i>{{ __('Add New Lead') }}
                        </a>
                        @endcan
                        
                        @can('create tasks')
                        <a href="{{ route('tasks.create') }}" class="btn btn-success btn-lg">
                            <i class="ti ti-plus me-2"></i>{{ __('Create Task') }}
                        </a>
                        @endcan
                        
                        @can('create projects')
                        <a href="{{ route('projects.create') }}" class="btn btn-info btn-lg">
                            <i class="ti ti-briefcase-plus me-2"></i>{{ __('New Project') }}
                        </a>
                        @endcan
                        
                        @can('manage contacts')
                        <a href="{{ route('contacts.create') }}" class="btn btn-warning btn-lg">
                            <i class="ti ti-address-book me-2"></i>{{ __('Add Contact') }}
                        </a>
                        @endcan
                    </div>

                    <!-- System Health Indicators -->
                    <div class="mt-4">
                        <h6>{{ __('System Health') }}</h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ __('Lead Response Rate') }}</small>
                                <span class="badge badge-modern bg-success">92%</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ __('Task Completion Rate') }}</small>
                                <span class="badge badge-modern bg-info">{{ isset($result['tasks']) && $result['tasks']['total'] > 0 ? round(($result['tasks']['completed'] / $result['tasks']['total']) * 100) : 0 }}%</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ __('System Uptime') }}</small>
                                <span class="badge badge-modern bg-success">99.9%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Insights -->
                    <div class="mt-4">
                        <div class="insight-card">
                            <h6>{{ __('Today\'s Highlight') }}</h6>
                            @if(isset($result['leads']) && $result['leads']['new_today'] > 0)
                            <p class="mb-0">{{ $result['leads']['new_today'] }} {{ __('new leads generated today! Keep up the momentum.') }}</p>
                            @elseif(isset($result['tasks']) && $result['tasks']['completed'] > 0)
                            <p class="mb-0">{{ $result['tasks']['completed'] }} {{ __('tasks completed. Great productivity!') }}</p>
                            @else
                            <p class="mb-0">{{ __('System running smoothly. Ready for new activities!') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-chart-bar me-2"></i>{{ __('Financial Summary & Insights') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <h5>{{ __('Net Profit') }}</h5>
                                <h3 class="mb-1">{{ priceFormat(($result['totalIncome'] ?? 0) - ($result['totalExpense'] ?? 0)) }}</h3>
                                <small class="trend-up"><i class="ti ti-trending-up"></i> +8.2%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--info-gradient);">
                                <h5>{{ __('Profit Margin') }}</h5>
                                <h3 class="mb-1">{{ $result['totalIncome'] > 0 ? round((($result['totalIncome'] - $result['totalExpense']) / $result['totalIncome']) * 100, 1) : 0 }}%</h3>
                                <small class="trend-up"><i class="ti ti-trending-up"></i> Healthy</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--success-gradient);">
                                <h5>{{ __('Revenue Growth') }}</h5>
                                <h3 class="mb-1">+12.5%</h3>
                                <small>{{ __('vs last month') }}</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--warning-gradient);">
                                <h5>{{ __('Expense Ratio') }}</h5>
                                <h3 class="mb-1">{{ $result['totalIncome'] > 0 ? round(($result['totalExpense'] / $result['totalIncome']) * 100, 1) : 0 }}%</h3>
                                <small class="trend-down"><i class="ti ti-trending-down"></i> Optimized</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Scripts for Mini Charts -->
<script>
    // Mini trend charts
    document.addEventListener('DOMContentLoaded', function() {
        // User Trend Mini Chart
        const userCtx = document.getElementById('userTrend');
        if (userCtx) {
            new Chart(userCtx, {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [12, 15, 13, 17, 15, 19, {{ $result['totalUser'] ?? 0 }}],
                        borderColor: '#2ca58d',
                        backgroundColor: 'rgba(44, 165, 141, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
        }

        // Client Trend Mini Chart
        const clientCtx = document.getElementById('clientTrend');
        if (clientCtx) {
            new Chart(clientCtx, {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [8, 12, 10, 14, 13, 16, {{ $result['totalClient'] ?? 0 }}],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
        }
    });
</script>
@endsection