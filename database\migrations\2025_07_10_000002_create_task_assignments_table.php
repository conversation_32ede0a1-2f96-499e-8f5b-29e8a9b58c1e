<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained('tasks')->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade');
            
            $table->enum('status', ['pending', 'accepted', 'rejected', 'reassigned'])->default('pending');
            $table->text('assignment_notes')->nullable();
            $table->text('response_notes')->nullable();
            
            $table->datetime('assigned_at');
            $table->datetime('responded_at')->nullable();
            $table->datetime('due_date')->nullable();
            
            // Track assignment history
            $table->boolean('is_current')->default(true);
            $table->foreignId('previous_assignment_id')->nullable()->constrained('task_assignments')->onDelete('set null');
            
            $table->index(['task_id']);
            $table->index(['assigned_to']);
            $table->index(['assigned_by']);
            $table->index(['status']);
            $table->index(['is_current']);
            $table->index(['assigned_at']);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task_assignments');
    }
};
