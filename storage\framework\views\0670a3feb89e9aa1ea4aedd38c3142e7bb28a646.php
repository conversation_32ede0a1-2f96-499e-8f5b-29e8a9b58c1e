<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Project Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('projects.index')); ?>"><?php echo e(__('Projects')); ?></a></li>
    <li class="breadcrumb-item" aria-current="page"><?php echo e(__('Dashboard')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-building fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Total Projects')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['total_projects']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-play fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Active')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['active_projects']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-check fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Completed')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['completed_projects']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-alert-triangle fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Overdue')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e($stats['overdue_projects']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-currency-dollar fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Total Budget')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e(currency_format_with_sym($stats['total_budget'])); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="ti ti-trending-up fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0 text-white"><?php echo e(__('Spent Budget')); ?></h6>
                            <h4 class="mb-0 text-white"><?php echo e(currency_format_with_sym($stats['spent_budget'])); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Charts -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Projects by Status')); ?></h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Projects by Type')); ?></h5>
                </div>
                <div class="card-body">
                    <canvas id="typeChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Projects -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5><?php echo e(__('Recent Projects')); ?></h5>
                        </div>
                        <div class="col-auto">
                            <a href="<?php echo e(route('projects.index')); ?>" class="btn btn-outline-primary btn-sm">
                                <?php echo e(__('View All')); ?>

                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($recentProjects->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Project')); ?></th>
                                        <th><?php echo e(__('Type')); ?></th>
                                        <th><?php echo e(__('Status')); ?></th>
                                        <th><?php echo e(__('Progress')); ?></th>
                                        <th><?php echo e(__('Manager')); ?></th>
                                        <th><?php echo e(__('Timeline')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentProjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e($project->name); ?></h6>
                                                    <small class="text-muted"><?php echo e($project->code); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e(\App\Models\Project::$types[$project->type] ?? $project->type); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($project->status_color); ?>"><?php echo e(\App\Models\Project::$statuses[$project->status] ?? $project->status); ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 6px;">
                                                        <div class="progress-bar" role="progressbar" style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                                                    </div>
                                                    <small><?php echo e($project->progress_percentage); ?>%</small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($project->projectManager): ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar avatar-sm me-2">
                                                            <img src="<?php echo e(!empty($project->projectManager->avatar) ? asset('storage/uploads/avatar/' . $project->projectManager->avatar) : asset('storage/uploads/avatar/avatar.png')); ?>" alt="<?php echo e($project->projectManager->name); ?>" class="rounded-circle">
                                                        </div>
                                                        <span><?php echo e($project->projectManager->name); ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Not Assigned')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($project->planned_start_date && $project->planned_end_date): ?>
                                                    <div>
                                                        <small class="text-muted"><?php echo e($project->planned_start_date->format('M d')); ?> - <?php echo e($project->planned_end_date->format('M d, Y')); ?></small><br>
                                                        <span class="badge bg-<?php echo e($project->is_overdue ? 'danger' : 'success'); ?>"><?php echo e($project->timeline_status); ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Not Set')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show projects')): ?>
                                                        <a href="<?php echo e(route('projects.show', $project->id)); ?>" class="btn btn-sm btn-outline-primary" title="<?php echo e(__('View')); ?>">
                                                            <i class="ti ti-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit projects')): ?>
                                                        <a href="#" class="btn btn-sm btn-outline-secondary customModal" data-size="lg"
                                                            data-url="<?php echo e(route('projects.edit', $project->id)); ?>" data-title="<?php echo e(__('Edit Project')); ?>" title="<?php echo e(__('Edit')); ?>">
                                                            <i class="ti ti-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="ti ti-building display-1 text-muted"></i>
                            <h5 class="mt-3"><?php echo e(__('No projects found')); ?></h5>
                            <p class="text-muted"><?php echo e(__('Start by creating your first project.')); ?></p>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create projects')): ?>
                                <a href="#" class="btn btn-primary customModal" data-size="lg"
                                    data-url="<?php echo e(route('projects.create')); ?>" data-title="<?php echo e(__('Create Project')); ?>">
                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Project')); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Projects by Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode(array_keys($projectsByStatus)); ?>,
            datasets: [{
                data: <?php echo json_encode(array_values($projectsByStatus)); ?>,
                backgroundColor: [
                    '#28a745', // active - green
                    '#ffc107', // inactive - yellow
                    '#007bff'  // completed - blue
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Projects by Type Chart
    const typeCtx = document.getElementById('typeChart').getContext('2d');
    const typeChart = new Chart(typeCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode(array_keys($projectsByType)); ?>,
            datasets: [{
                label: 'Projects',
                data: <?php echo json_encode(array_values($projectsByType)); ?>,
                backgroundColor: [
                    '#007bff', // residential - blue
                    '#28a745', // commercial - green
                    '#ffc107', // mixed - yellow
                    '#dc3545'  // industrial - red
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/projects/dashboard.blade.php ENDPATH**/ ?>