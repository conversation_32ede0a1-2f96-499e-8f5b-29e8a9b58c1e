<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Enhanced Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item" aria-current="page"><?php echo e(__('Dashboard')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #2ca58d 0%, #0a2342 100%);
        --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        --info-gradient: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        --card-shadow: 0 8px 25px rgba(0,0,0,0.15);
        --hover-shadow: 0 15px 35px rgba(0,0,0,0.25);
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        box-shadow: var(--card-shadow);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        position: relative;
    }

    .enhanced-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--hover-shadow);
    }

    .stat-card-modern {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .icon-gradient {
        background: var(--primary-gradient);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .chart-container {
        min-height: 400px;
        position: relative;
    }

    .progress-ring {
        width: 120px;
        height: 120px;
    }

    .progress-ring circle {
        stroke-width: 8;
        fill: transparent;
        r: 52;
        cx: 60;
        cy: 60;
    }

    .progress-ring .bg {
        stroke: #e9ecef;
    }

    .progress-ring .progress {
        stroke: #2ca58d;
        stroke-linecap: round;
        transform: rotate(-90deg);
        transform-origin: 60px 60px;
        transition: stroke-dasharray 0.5s ease;
    }

    .metric-card {
        background: var(--primary-gradient);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .table-modern {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    .table-modern th {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .table-modern td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #f8f9fa;
    }

    .badge-modern {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .section-header {
        background: var(--primary-gradient);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px 15px 0 0;
        margin: 0;
    }

    .insight-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        position: relative;
    }

    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-neutral { color: #6c757d; }

    .mini-chart {
        height: 60px;
        margin-top: 10px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    // Main Income/Expense Chart
    var incomeExpenseOptions = {
        chart: {
            type: 'area',
            height: 450,
            toolbar: { show: false },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#2ca58d', '#dc3545', '#ffc107'],
        dataLabels: { enabled: false },
        legend: {
            show: true,
            position: 'top',
            fontSize: '14px',
            fontWeight: 600
        },
        markers: {
            size: 6,
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: { size: 8 }
        },
        stroke: {
            width: 3,
            curve: 'smooth'
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                type: 'vertical',
                opacityFrom: 0.7,
                opacityTo: 0.1
            }
        },
        grid: {
            show: true,
            borderColor: '#e7e7e7',
            strokeDashArray: 3
        },
        series: [
            {
                name: "<?php echo e(__('Total Income')); ?>",
                data: <?php echo json_encode($result['incomeExpense']['income'] ?? []); ?>

            },
            {
                name: "<?php echo e(__('Total Expense')); ?>",
                data: <?php echo json_encode($result['incomeExpense']['expense'] ?? []); ?>

            }
        ],
        xaxis: {
            categories: <?php echo json_encode($result['incomeExpense']['label'] ?? []); ?>,
            labels: {
                style: { fontSize: '12px', fontWeight: 500 }
            }
        },
        yaxis: {
            labels: {
                formatter: function(value) {
                    return '<?php echo e($result['settings']['currency'] ?? '$'); ?>' + value.toFixed(0);
                }
            }
        },
        tooltip: {
            theme: 'dark',
            y: {
                formatter: function(value) {
                    return '<?php echo e($result['settings']['currency'] ?? '$'); ?>' + value.toFixed(2);
                }
            }
        }
    };
    var incomeExpenseChart = new ApexCharts(document.querySelector('#incomeExpenseByMonth'), incomeExpenseOptions);
    incomeExpenseChart.render();

    // Lead Conversion Funnel Chart
    var funnelOptions = {
        chart: {
            type: 'bar',
            height: 350,
            toolbar: { show: false }
        },
        plotOptions: {
            bar: {
                horizontal: true,
                distributed: true,
                barHeight: '70%'
            }
        },
        colors: ['#2ca58d', '#17a2b8', '#ffc107', '#dc3545'],
        series: [{
            data: [
                { x: 'Total Leads', y: <?php echo e($result['leads']['total'] ?? 0); ?> },
                { x: 'Contacted', y: <?php echo e((int)($result['leads']['total'] * 0.7)); ?> },
                { x: 'Interested', y: <?php echo e((int)($result['leads']['total'] * 0.4)); ?> },
                { x: 'Converted', y: <?php echo e($result['leads']['converted'] ?? 0); ?> }
            ]
        }],
        xaxis: {
            labels: {
                formatter: function(value) {
                    return Math.round(value);
                }
            }
        },
        title: {
            text: 'Lead Conversion Funnel',
            align: 'center',
            style: { fontSize: '18px', fontWeight: 'bold' }
        }
    };
    var funnelChart = new ApexCharts(document.querySelector('#leadFunnelChart'), funnelOptions);
    funnelChart.render();

    // Task Distribution Pie Chart
    <?php if(isset($result['tasks'])): ?>
    var taskOptions = {
        chart: {
            type: 'donut',
            height: 350
        },
        series: [
            <?php echo e($result['tasks']['not_started'] ?? 0); ?>,
            <?php echo e($result['tasks']['in_progress'] ?? 0); ?>,
            <?php echo e($result['tasks']['completed'] ?? 0); ?>,
            <?php echo e($result['tasks']['overdue'] ?? 0); ?>

        ],
        labels: ['Not Started', 'In Progress', 'Completed', 'Overdue'],
        colors: ['#6c757d', '#17a2b8', '#28a745', '#dc3545'],
        legend: {
            position: 'bottom'
        },
        title: {
            text: 'Task Distribution',
            align: 'center',
            style: { fontSize: '18px', fontWeight: 'bold' }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '60%',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total Tasks',
                            fontSize: '16px',
                            fontWeight: 'bold'
                        }
                    }
                }
            }
        }
    };
    var taskChart = new ApexCharts(document.querySelector('#taskDistributionChart'), taskOptions);
    taskChart.render();
    <?php endif; ?>

    // Performance Metrics Chart
    <?php if(isset($result['calling'])): ?>
    var performanceOptions = {
        chart: {
            type: 'radialBar',
            height: 350
        },
        series: [
            <?php echo e($result['calling']['connected_calls'] > 0 ? round(($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100) : 0); ?>,
            <?php echo e($result['leads']['converted'] > 0 ? round(($result['leads']['converted'] / $result['leads']['total']) * 100) : 0); ?>

        ],
        colors: ['#2ca58d', '#17a2b8'],
        labels: ['Call Success Rate', 'Lead Conversion Rate'],
        plotOptions: {
            radialBar: {
                size: 160,
                track: {
                    background: '#e7e7e7',
                    strokeWidth: '97%',
                    margin: 15
                },
                dataLabels: {
                    name: {
                        fontSize: '14px',
                        fontWeight: 'bold'
                    },
                    value: {
                        fontSize: '16px',
                        fontWeight: 'bold',
                        formatter: function(val) {
                            return val + '%';
                        }
                    }
                }
            }
        }
    };
    var performanceChart = new ApexCharts(document.querySelector('#performanceChart'), performanceOptions);
    performanceChart.render();
    <?php endif; ?>

    // Real-time updates simulation
    setInterval(function() {
        // Add sparkle effect to cards
        document.querySelectorAll('.enhanced-card').forEach(function(card) {
            if (Math.random() > 0.8) {
                card.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                }, 200);
            }
        });
    }, 5000);

    // Initialize progress rings
    function updateProgressRing(selector, percentage) {
        const circle = document.querySelector(selector + ' .progress');
        if (circle) {
            const radius = circle.r.baseVal.value;
            const circumference = radius * 2 * Math.PI;
            const offset = circumference - (percentage / 100) * circumference;
            
            circle.style.strokeDasharray = circumference;
            circle.style.strokeDashoffset = offset;
        }
    }

    // Update progress rings with animation
    setTimeout(() => {
        updateProgressRing('#conversionRate', <?php echo e($result['leads']['converted'] > 0 ? round(($result['leads']['converted'] / $result['leads']['total']) * 100) : 0); ?>);
        updateProgressRing('#taskCompletion', <?php echo e(isset($result['tasks']) && $result['tasks']['total'] > 0 ? round(($result['tasks']['completed'] / $result['tasks']['total']) * 100) : 0); ?>);
    }, 1000);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Enhanced Header with Key Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="mb-1 text-primary fw-bold"><?php echo e(__('Welcome Back!')); ?></h2>
                            <p class="text-muted mb-0"><?php echo e(__('Here\'s what\'s happening with your business today.')); ?></p>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex justify-content-end align-items-center">
                                <div class="me-4">
                                    <small class="text-muted"><?php echo e(__('Today')); ?></small>
                                    <h6 class="mb-0"><?php echo e(now()->format('M d, Y')); ?></h6>
                                </div>
                                <div class="icon-gradient">
                                    <i class="ti ti-dashboard"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3">
                        <i class="ti ti-users"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-primary"><?php echo e($result['totalUser'] ?? 0); ?></h3>
                    <p class="text-muted mb-0"><?php echo e(__('Total Users')); ?></p>
                    <div class="mini-chart">
                        <canvas id="userTrend"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3" style="background: var(--success-gradient);">
                        <i class="ti ti-package"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-success"><?php echo e($result['totalClient'] ?? 0); ?></h3>
                    <p class="text-muted mb-0"><?php echo e(__('Total Clients')); ?></p>
                    <div class="mini-chart">
                        <canvas id="clientTrend"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3" style="background: var(--info-gradient);">
                        <i class="ti ti-currency-dollar"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-info"><?php echo e(priceFormat($result['totalIncome'] ?? 0)); ?></h3>
                    <p class="text-muted mb-0"><?php echo e(__('Total Income')); ?></p>
                    <small class="trend-up"><i class="ti ti-trending-up"></i> +12.5%</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="enhanced-card stat-card-modern">
                <div class="card-body text-center">
                    <div class="icon-gradient mx-auto mb-3" style="background: var(--warning-gradient);">
                        <i class="ti ti-credit-card"></i>
                    </div>
                    <h3 class="mb-1 fw-bold text-warning"><?php echo e(priceFormat($result['totalExpense'] ?? 0)); ?></h3>
                    <p class="text-muted mb-0"><?php echo e(__('Total Expense')); ?></p>
                    <small class="trend-down"><i class="ti ti-trending-down"></i> -5.2%</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Main Income/Expense Chart -->
        <div class="col-lg-8 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-chart-line me-2"></i><?php echo e(__('Financial Analysis')); ?></h5>
                </div>
                <div class="card-body">
                    <div id="incomeExpenseByMonth"></div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="col-lg-4 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-target me-2"></i><?php echo e(__('Performance')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if(isset($result['calling'])): ?>
                    <div id="performanceChart"></div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="ti ti-chart-pie text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3"><?php echo e(__('Performance data will appear here')); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Lead Management Enhanced Section -->
    <?php if(isset($result['leads'])): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-user-check me-2"></i><?php echo e(__('Lead Management Analytics')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="row">
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card">
                                        <h4 class="mb-1"><?php echo e($result['leads']['total']); ?></h4>
                                        <small><?php echo e(__('Total Leads')); ?></small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card" style="background: var(--success-gradient);">
                                        <h4 class="mb-1"><?php echo e($result['leads']['new_today']); ?></h4>
                                        <small><?php echo e(__('New Today')); ?></small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card" style="background: var(--info-gradient);">
                                        <h4 class="mb-1"><?php echo e($result['leads']['converted']); ?></h4>
                                        <small><?php echo e(__('Converted')); ?></small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <div class="metric-card" style="background: var(--danger-gradient);">
                                        <h4 class="mb-1"><?php echo e($result['leads']['hot_leads']); ?></h4>
                                        <small><?php echo e(__('Hot Leads')); ?></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Conversion Rate Progress -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <svg class="progress-ring" id="conversionRate">
                                            <circle class="bg" />
                                            <circle class="progress" />
                                        </svg>
                                        <h6 class="mt-2"><?php echo e(__('Conversion Rate')); ?></h6>
                                        <p class="text-muted"><?php echo e($result['leads']['converted'] > 0 ? round(($result['leads']['converted'] / $result['leads']['total']) * 100, 1) : 0); ?>%</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6><?php echo e(__('Lead Quality Insights')); ?></h6>
                                    <div class="mb-2">
                                        <small class="text-muted"><?php echo e(__('Hot Leads Ratio')); ?></small>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-danger" style="width: <?php echo e($result['leads']['total'] > 0 ? ($result['leads']['hot_leads'] / $result['leads']['total']) * 100 : 0); ?>%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted"><?php echo e(__('Daily Growth')); ?></small>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: 75%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div id="leadFunnelChart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Task Management Enhanced Section -->
    <?php if(isset($result['tasks']) && (Gate::check('manage tasks') || Gate::check('view tasks'))): ?>
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-check-square me-2"></i><?php echo e(__('Task Management Overview')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="ti ti-list"></i>
                                </div>
                                <h4 class="mb-1 text-primary"><?php echo e($result['tasks']['total']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Total Tasks')); ?></small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--warning-gradient);">
                                    <i class="ti ti-clock"></i>
                                </div>
                                <h4 class="mb-1 text-warning"><?php echo e($result['tasks']['in_progress']); ?></h4>
                                <small class="text-muted"><?php echo e(__('In Progress')); ?></small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--danger-gradient);">
                                    <i class="ti ti-alert-triangle"></i>
                                </div>
                                <h4 class="mb-1 text-danger"><?php echo e($result['tasks']['overdue']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Overdue')); ?></small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--success-gradient);">
                                    <i class="ti ti-check"></i>
                                </div>
                                <h4 class="mb-1 text-success"><?php echo e($result['tasks']['completed']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Completed')); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Task Insights -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="insight-card">
                                <h5><?php echo e(__('Today\'s Priority')); ?></h5>
                                <h3><?php echo e($result['tasks']['due_today']); ?></h3>
                                <small><?php echo e(__('Tasks due today')); ?></small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="insight-card" style="background: var(--danger-gradient);">
                                <h5><?php echo e(__('High Priority')); ?></h5>
                                <h3><?php echo e($result['tasks']['high_priority']); ?></h3>
                                <small><?php echo e(__('Urgent tasks')); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-chart-pie me-2"></i><?php echo e(__('Task Distribution')); ?></h5>
                </div>
                <div class="card-body">
                    <div id="taskDistributionChart"></div>
                    
                    <!-- Task Completion Progress -->
                    <div class="text-center mt-4">
                        <svg class="progress-ring" id="taskCompletion">
                            <circle class="bg" />
                            <circle class="progress" stroke="#28a745" />
                        </svg>
                        <h6 class="mt-2"><?php echo e(__('Completion Rate')); ?></h6>
                        <p class="text-muted"><?php echo e($result['tasks']['total'] > 0 ? round(($result['tasks']['completed'] / $result['tasks']['total']) * 100, 1) : 0); ?>%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Calling System Analytics -->
    <?php if(isset($result['calling'])): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-phone me-2"></i><?php echo e(__('Calling System Analytics')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <i class="ti ti-phone mb-2" style="font-size: 2rem;"></i>
                                <h4><?php echo e($result['calling']['total_calls']); ?></h4>
                                <small><?php echo e(__('Total Calls')); ?></small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--info-gradient);">
                                <i class="ti ti-phone-call mb-2" style="font-size: 2rem;"></i>
                                <h4><?php echo e($result['calling']['calls_today']); ?></h4>
                                <small><?php echo e(__('Calls Today')); ?></small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--success-gradient);">
                                <i class="ti ti-phone-check mb-2" style="font-size: 2rem;"></i>
                                <h4><?php echo e($result['calling']['connected_calls']); ?></h4>
                                <small><?php echo e(__('Connected Calls')); ?></small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--warning-gradient);">
                                <i class="ti ti-clock mb-2" style="font-size: 2rem;"></i>
                                <h4><?php echo e(gmdate('i:s', $result['calling']['avg_call_duration'])); ?></h4>
                                <small><?php echo e(__('Avg Duration')); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Call Analytics -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6><?php echo e(__('Call Performance Metrics')); ?></h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small><?php echo e(__('Success Rate')); ?></small>
                                    <small><?php echo e($result['calling']['total_calls'] > 0 ? round(($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100, 1) : 0); ?>%</small>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo e($result['calling']['total_calls'] > 0 ? ($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100 : 0); ?>%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small><?php echo e(__('Daily Target')); ?></small>
                                    <small><?php echo e($result['calling']['calls_today']); ?>/50</small>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-info" style="width: <?php echo e(($result['calling']['calls_today'] / 50) * 100); ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><?php echo e(__('Call Quality Insights')); ?></h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted"><?php echo e(__('Average Call Duration')); ?></span>
                                <span class="badge badge-modern bg-primary"><?php echo e(gmdate('i:s', $result['calling']['avg_call_duration'])); ?></span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted"><?php echo e(__('Connection Rate')); ?></span>
                                <span class="badge badge-modern bg-success"><?php echo e($result['calling']['total_calls'] > 0 ? round(($result['calling']['connected_calls'] / $result['calling']['total_calls']) * 100, 1) : 0); ?>%</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted"><?php echo e(__('Today\'s Activity')); ?></span>
                                <span class="badge badge-modern bg-info"><?php echo e($result['calling']['calls_today']); ?> <?php echo e(__('calls')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Projects and Properties Row -->
    <div class="row mb-4">
        <!-- Project Management Stats -->
        <?php if(isset($result['projects'])): ?>
        <div class="col-lg-6 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-briefcase me-2"></i><?php echo e(__('Project Management')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="ti ti-briefcase"></i>
                                </div>
                                <h4 class="mb-1 text-primary"><?php echo e($result['projects']['total']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Total Projects')); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--success-gradient);">
                                    <i class="ti ti-circle-check"></i>
                                </div>
                                <h4 class="mb-1 text-success"><?php echo e($result['projects']['active']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Active')); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--info-gradient);">
                                    <i class="ti ti-check"></i>
                                </div>
                                <h4 class="mb-1 text-info"><?php echo e($result['projects']['completed']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Completed')); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--warning-gradient);">
                                    <i class="ti ti-pause"></i>
                                </div>
                                <h4 class="mb-1 text-warning"><?php echo e($result['projects']['on_hold']); ?></h4>
                                <small class="text-muted"><?php echo e(__('On Hold')); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Project Progress -->
                    <div class="mt-3">
                        <h6><?php echo e(__('Project Progress Overview')); ?></h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small><?php echo e(__('Completion Rate')); ?></small>
                                <small><?php echo e($result['projects']['total'] > 0 ? round(($result['projects']['completed'] / $result['projects']['total']) * 100, 1) : 0); ?>%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: <?php echo e($result['projects']['total'] > 0 ? ($result['projects']['completed'] / $result['projects']['total']) * 100 : 0); ?>%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small><?php echo e(__('Active Rate')); ?></small>
                                <small><?php echo e($result['projects']['total'] > 0 ? round(($result['projects']['active'] / $result['projects']['total']) * 100, 1) : 0); ?>%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-primary" style="width: <?php echo e($result['projects']['total'] > 0 ? ($result['projects']['active'] / $result['projects']['total']) * 100 : 0); ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Property Management Stats -->
        <?php if(isset($result['properties'])): ?>
        <div class="col-lg-6 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-home me-2"></i><?php echo e(__('Property Management')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                    <i class="ti ti-home"></i>
                                </div>
                                <h4 class="mb-1 text-primary"><?php echo e($result['properties']['total']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Total Properties')); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--success-gradient);">
                                    <i class="ti ti-circle-check"></i>
                                </div>
                                <h4 class="mb-1 text-success"><?php echo e($result['properties']['available']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Available')); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--info-gradient);">
                                    <i class="ti ti-check"></i>
                                </div>
                                <h4 class="mb-1 text-info"><?php echo e($result['properties']['sold']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Sold')); ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <div class="icon-gradient mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem; background: var(--warning-gradient);">
                                    <i class="ti ti-clock"></i>
                                </div>
                                <h4 class="mb-1 text-warning"><?php echo e($result['properties']['reserved']); ?></h4>
                                <small class="text-muted"><?php echo e(__('Reserved')); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Property Insights -->
                    <div class="mt-3">
                        <h6><?php echo e(__('Property Market Overview')); ?></h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small><?php echo e(__('Sales Rate')); ?></small>
                                <small><?php echo e($result['properties']['total'] > 0 ? round(($result['properties']['sold'] / $result['properties']['total']) * 100, 1) : 0); ?>%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: <?php echo e($result['properties']['total'] > 0 ? ($result['properties']['sold'] / $result['properties']['total']) * 100 : 0); ?>%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small><?php echo e(__('Availability Rate')); ?></small>
                                <small><?php echo e($result['properties']['total'] > 0 ? round(($result['properties']['available'] / $result['properties']['total']) * 100, 1) : 0); ?>%</small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: <?php echo e($result['properties']['total'] > 0 ? ($result['properties']['available'] / $result['properties']['total']) * 100 : 0); ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="row mb-4">
        <!-- Recent Activities Table -->
        <div class="col-lg-8 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-activity me-2"></i><?php echo e(__('Recent Activities')); ?></h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern mb-0">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Activity')); ?></th>
                                    <th><?php echo e(__('Type')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Time')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if(isset($result['leads']) && $result['leads']['new_today'] > 0): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                                <i class="ti ti-user-plus"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($result['leads']['new_today']); ?> <?php echo e(__('new leads added')); ?></h6>
                                                <small class="text-muted"><?php echo e(__('Lead generation')); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-primary"><?php echo e(__('Lead')); ?></span></td>
                                    <td><span class="badge badge-modern bg-success"><?php echo e(__('Active')); ?></span></td>
                                    <td><small class="text-muted"><?php echo e(__('Today')); ?></small></td>
                                </tr>
                                <?php endif; ?>
                                
                                <?php if(isset($result['calling']) && $result['calling']['calls_today'] > 0): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: var(--info-gradient);">
                                                <i class="ti ti-phone"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($result['calling']['calls_today']); ?> <?php echo e(__('calls made today')); ?></h6>
                                                <small class="text-muted"><?php echo e(__('Calling activity')); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-info"><?php echo e(__('Call')); ?></span></td>
                                    <td><span class="badge badge-modern bg-success"><?php echo e(__('Completed')); ?></span></td>
                                    <td><small class="text-muted"><?php echo e(__('Today')); ?></small></td>
                                </tr>
                                <?php endif; ?>

                                <?php if(isset($result['tasks']) && $result['tasks']['completed'] > 0): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: var(--success-gradient);">
                                                <i class="ti ti-check"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($result['tasks']['completed']); ?> <?php echo e(__('tasks completed')); ?></h6>
                                                <small class="text-muted"><?php echo e(__('Task management')); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-success"><?php echo e(__('Task')); ?></span></td>
                                    <td><span class="badge badge-modern bg-success"><?php echo e(__('Completed')); ?></span></td>
                                    <td><small class="text-muted"><?php echo e(__('Recent')); ?></small></td>
                                </tr>
                                <?php endif; ?>

                                <?php if(isset($result['projects']) && $result['projects']['active'] > 0): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-gradient me-3" style="width: 35px; height: 35px; font-size: 0.9rem; background: var(--warning-gradient);">
                                                <i class="ti ti-briefcase"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($result['projects']['active']); ?> <?php echo e(__('projects in progress')); ?></h6>
                                                <small class="text-muted"><?php echo e(__('Project management')); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-modern bg-warning"><?php echo e(__('Project')); ?></span></td>
                                    <td><span class="badge badge-modern bg-warning"><?php echo e(__('In Progress')); ?></span></td>
                                    <td><small class="text-muted"><?php echo e(__('Ongoing')); ?></small></td>
                                </tr>
                                <?php endif; ?>

                                <?php if(!isset($result['leads']) && !isset($result['calling']) && !isset($result['tasks']) && !isset($result['projects'])): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <i class="ti ti-inbox text-muted" style="font-size: 3rem;"></i>
                                        <p class="text-muted mt-2"><?php echo e(__('No recent activities found')); ?></p>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions and Insights -->
        <div class="col-lg-4 mb-4">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-zap me-2"></i><?php echo e(__('Quick Actions')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create leads')): ?>
                        <a href="<?php echo e(route('leads.create')); ?>" class="btn btn-primary btn-lg">
                            <i class="ti ti-user-plus me-2"></i><?php echo e(__('Add New Lead')); ?>

                        </a>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create tasks')): ?>
                        <a href="<?php echo e(route('tasks.create')); ?>" class="btn btn-success btn-lg">
                            <i class="ti ti-plus me-2"></i><?php echo e(__('Create Task')); ?>

                        </a>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create projects')): ?>
                        <a href="<?php echo e(route('projects.create')); ?>" class="btn btn-info btn-lg">
                            <i class="ti ti-briefcase-plus me-2"></i><?php echo e(__('New Project')); ?>

                        </a>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage contacts')): ?>
                        <a href="<?php echo e(route('contacts.create')); ?>" class="btn btn-warning btn-lg">
                            <i class="ti ti-address-book me-2"></i><?php echo e(__('Add Contact')); ?>

                        </a>
                        <?php endif; ?>
                    </div>

                    <!-- System Health Indicators -->
                    <div class="mt-4">
                        <h6><?php echo e(__('System Health')); ?></h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted"><?php echo e(__('Lead Response Rate')); ?></small>
                                <span class="badge badge-modern bg-success">92%</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted"><?php echo e(__('Task Completion Rate')); ?></small>
                                <span class="badge badge-modern bg-info"><?php echo e(isset($result['tasks']) && $result['tasks']['total'] > 0 ? round(($result['tasks']['completed'] / $result['tasks']['total']) * 100) : 0); ?>%</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted"><?php echo e(__('System Uptime')); ?></small>
                                <span class="badge badge-modern bg-success">99.9%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Insights -->
                    <div class="mt-4">
                        <div class="insight-card">
                            <h6><?php echo e(__('Today\'s Highlight')); ?></h6>
                            <?php if(isset($result['leads']) && $result['leads']['new_today'] > 0): ?>
                            <p class="mb-0"><?php echo e($result['leads']['new_today']); ?> <?php echo e(__('new leads generated today! Keep up the momentum.')); ?></p>
                            <?php elseif(isset($result['tasks']) && $result['tasks']['completed'] > 0): ?>
                            <p class="mb-0"><?php echo e($result['tasks']['completed']); ?> <?php echo e(__('tasks completed. Great productivity!')); ?></p>
                            <?php else: ?>
                            <p class="mb-0"><?php echo e(__('System running smoothly. Ready for new activities!')); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="enhanced-card">
                <div class="section-header">
                    <h5 class="mb-0"><i class="ti ti-chart-bar me-2"></i><?php echo e(__('Financial Summary & Insights')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <h5><?php echo e(__('Net Profit')); ?></h5>
                                <h3 class="mb-1"><?php echo e(priceFormat(($result['totalIncome'] ?? 0) - ($result['totalExpense'] ?? 0))); ?></h3>
                                <small class="trend-up"><i class="ti ti-trending-up"></i> +8.2%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--info-gradient);">
                                <h5><?php echo e(__('Profit Margin')); ?></h5>
                                <h3 class="mb-1"><?php echo e($result['totalIncome'] > 0 ? round((($result['totalIncome'] - $result['totalExpense']) / $result['totalIncome']) * 100, 1) : 0); ?>%</h3>
                                <small class="trend-up"><i class="ti ti-trending-up"></i> Healthy</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--success-gradient);">
                                <h5><?php echo e(__('Revenue Growth')); ?></h5>
                                <h3 class="mb-1">+12.5%</h3>
                                <small><?php echo e(__('vs last month')); ?></small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card" style="background: var(--warning-gradient);">
                                <h5><?php echo e(__('Expense Ratio')); ?></h5>
                                <h3 class="mb-1"><?php echo e($result['totalIncome'] > 0 ? round(($result['totalExpense'] / $result['totalIncome']) * 100, 1) : 0); ?>%</h3>
                                <small class="trend-down"><i class="ti ti-trending-down"></i> Optimized</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Scripts for Mini Charts -->
<script>
    // Mini trend charts
    document.addEventListener('DOMContentLoaded', function() {
        // User Trend Mini Chart
        const userCtx = document.getElementById('userTrend');
        if (userCtx) {
            new Chart(userCtx, {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [12, 15, 13, 17, 15, 19, <?php echo e($result['totalUser'] ?? 0); ?>],
                        borderColor: '#2ca58d',
                        backgroundColor: 'rgba(44, 165, 141, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
        }

        // Client Trend Mini Chart
        const clientCtx = document.getElementById('clientTrend');
        if (clientCtx) {
            new Chart(clientCtx, {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [8, 12, 10, 14, 13, 16, <?php echo e($result['totalClient'] ?? 0); ?>],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/dashboard/index.blade.php ENDPATH**/ ?>