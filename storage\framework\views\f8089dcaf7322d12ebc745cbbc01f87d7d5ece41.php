<?php $__env->startSection('page-title'); ?>
    <?php echo e($property->name); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('properties.index')); ?>"><?php echo e(__('Properties')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e($property->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if(Gate::check('edit properties')): ?>
            <a href="#" class="btn btn-sm btn-primary me-2 customModal" data-size="xl"
               data-url="<?php echo e(route('properties.edit', $property->id)); ?>" data-title="<?php echo e(__('Edit Property')); ?>">
                <i class="ti ti-pencil"></i> <?php echo e(__('Edit')); ?>

            </a>
        <?php endif; ?>
        <a href="<?php echo e(route('properties.index')); ?>" class="btn btn-sm btn-secondary">
            <i class="ti ti-arrow-left"></i> <?php echo e(__('Back')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Property Header -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2"><?php echo e($property->name); ?></h4>
                            <p class="text-muted mb-2">
                                <i class="ti ti-map-pin me-1"></i><?php echo e($property->address); ?>, <?php echo e($property->city); ?>, <?php echo e($property->state); ?>

                            </p>
                            <div class="d-flex flex-wrap gap-2">
                                <span class="badge bg-primary"><?php echo e(\App\Models\Property::$types[$property->type] ?? $property->type); ?></span>
                                <span class="badge bg-info"><?php echo e(\App\Models\Property::$categories[$property->category] ?? $property->category); ?></span>
                                <?php if($property->status == 'available'): ?>
                                    <span class="badge bg-success"><?php echo e(\App\Models\Property::$statuses[$property->status] ?? $property->status); ?></span>
                                <?php elseif($property->status == 'sold'): ?>
                                    <span class="badge bg-danger"><?php echo e(\App\Models\Property::$statuses[$property->status] ?? $property->status); ?></span>
                                <?php else: ?>
                                    <span class="badge bg-warning"><?php echo e(\App\Models\Property::$statuses[$property->status] ?? $property->status); ?></span>
                                <?php endif; ?>
                                <?php if($property->is_featured): ?>
                                    <span class="badge bg-warning"><?php echo e(__('Featured')); ?></span>
                                <?php endif; ?>
                                <?php if($property->is_premium): ?>
                                    <span class="badge bg-dark"><?php echo e(__('Premium')); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <h3 class="text-primary mb-1"><?php echo e($property->formatted_price); ?></h3>
                            <p class="text-muted mb-0"><?php echo e(__('Property Code')); ?>: <?php echo e($property->property_code); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><?php echo e(__('Property Details')); ?></h5>
                    <?php if(Gate::check('edit properties')): ?>
                        <a href="#" class="btn btn-sm btn-outline-primary customModal" data-size="xl"
                           data-url="<?php echo e(route('properties.edit', $property->id)); ?>" data-title="<?php echo e(__('Edit Basic Details')); ?>">
                            <i class="ti ti-pencil"></i> <?php echo e(__('Edit Basic Info')); ?>

                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if($property->description): ?>
                        <div class="mb-4">
                            <h6><?php echo e(__('Description')); ?></h6>
                            <p><?php echo e($property->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0"><?php echo e(__('Unit Specifications')); ?></h6>
                                <?php if(Gate::check('edit properties')): ?>
                                    <a href="#" class="btn btn-xs btn-outline-primary customModal" data-size="xl"
                                       data-url="<?php echo e(route('properties.edit-unit-details', $property->id)); ?>" data-title="<?php echo e(__('Edit Unit Details')); ?>">
                                        <i class="ti ti-pencil"></i> <?php echo e(__('Edit')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                            <table class="table table-sm">
                                <?php if($property->carpet_area): ?>
                                    <tr>
                                        <td><?php echo e(__('Carpet Area')); ?></td>
                                        <td><?php echo e(number_format($property->carpet_area)); ?> sq ft</td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->built_up_area): ?>
                                    <tr>
                                        <td><?php echo e(__('Built-up Area')); ?></td>
                                        <td><?php echo e(number_format($property->built_up_area)); ?> sq ft</td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->super_area): ?>
                                    <tr>
                                        <td><?php echo e(__('Super Area')); ?></td>
                                        <td><?php echo e(number_format($property->super_area)); ?> sq ft</td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->bedrooms): ?>
                                    <tr>
                                        <td><?php echo e(__('Bedrooms')); ?></td>
                                        <td><?php echo e($property->bedrooms); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->bathrooms): ?>
                                    <tr>
                                        <td><?php echo e(__('Bathrooms')); ?></td>
                                        <td><?php echo e($property->bathrooms); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->parking_spaces): ?>
                                    <tr>
                                        <td><?php echo e(__('Parking')); ?></td>
                                        <td><?php echo e($property->parking_spaces); ?> spaces</td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->floor_number): ?>
                                    <tr>
                                        <td><?php echo e(__('Floor')); ?></td>
                                        <td><?php echo e($property->floor_number); ?><?php echo e($property->total_floors ? ' of ' . $property->total_floors : ''); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->facing): ?>
                                    <tr>
                                        <td><?php echo e(__('Facing')); ?></td>
                                        <td><?php echo e(\App\Models\Property::$facings[$property->facing] ?? $property->facing); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->furnishing): ?>
                                    <tr>
                                        <td><?php echo e(__('Furnishing')); ?></td>
                                        <td><?php echo e(\App\Models\Property::$furnishings[$property->furnishing] ?? $property->furnishing); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0"><?php echo e(__('Pricing Details')); ?></h6>
                                <?php if(Gate::check('edit properties')): ?>
                                    <a href="#" class="btn btn-xs btn-outline-primary customModal" data-size="xl"
                                       data-url="<?php echo e(route('properties.edit-pricing-details', $property->id)); ?>" data-title="<?php echo e(__('Edit Pricing Details')); ?>">
                                        <i class="ti ti-pencil"></i> <?php echo e(__('Edit')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                            <table class="table table-sm">
                                <tr>
                                    <td><?php echo e(__('Current Price')); ?></td>
                                    <td><strong><?php echo e($property->formatted_price); ?></strong></td>
                                </tr>
                                <?php if($property->price_per_sqft): ?>
                                    <tr>
                                        <td><?php echo e(__('Price per Sq Ft')); ?></td>
                                        <td>₹<?php echo e(number_format($property->price_per_sqft)); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->maintenance_charges): ?>
                                    <tr>
                                        <td><?php echo e(__('Maintenance')); ?></td>
                                        <td>₹<?php echo e(number_format($property->maintenance_charges)); ?>/month</td>
                                    </tr>
                                <?php endif; ?>
                                <?php if($property->booking_amount): ?>
                                    <tr>
                                        <td><?php echo e(__('Booking Amount')); ?></td>
                                        <td>₹<?php echo e(number_format($property->booking_amount)); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><?php echo e(__('Negotiable')); ?></td>
                                    <td><?php echo e($property->price_negotiable ? __('Yes') : __('No')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if($property->amenities && count($property->amenities) > 0): ?>
                        <div class="mt-4">
                            <h6><?php echo e(__('Amenities')); ?></h6>
                            <div class="d-flex flex-wrap gap-2">
                                <?php $__currentLoopData = $property->amenities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $amenity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-light text-dark"><?php echo e($amenity); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($property->features && count($property->features) > 0): ?>
                        <div class="mt-4">
                            <h6><?php echo e(__('Features')); ?></h6>
                            <div class="d-flex flex-wrap gap-2">
                                <?php $__currentLoopData = $property->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-light text-dark"><?php echo e($feature); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Information -->
        <div class="col-lg-4">
            <!-- Project Information -->
            <?php if($property->project): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><?php echo e(__('Project Information')); ?></h5>
                    </div>
                    <div class="card-body">
                        <h6><?php echo e($property->project->name); ?></h6>
                        <?php if($property->project->description): ?>
                            <p class="text-muted"><?php echo e(Str::limit($property->project->description, 100)); ?></p>
                        <?php endif; ?>
                        <a href="<?php echo e(route('projects.show', $property->project->id)); ?>" class="btn btn-sm btn-outline-primary">
                            <?php echo e(__('View Project')); ?>

                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Images & Media -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><?php echo e(__('Images & Media')); ?></h5>
                    <?php if(Gate::check('edit properties')): ?>
                        <a href="#" class="btn btn-xs btn-outline-primary customModal" data-size="xl"
                           data-url="<?php echo e(route('properties.edit-images-media', $property->id)); ?>" data-title="<?php echo e(__('Edit Images & Media')); ?>">
                            <i class="ti ti-pencil"></i> <?php echo e(__('Edit')); ?>

                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if($property->images && count($property->images) > 0): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Property Images')); ?> (<?php echo e(count($property->images)); ?>)</h6>
                            <div class="row g-2">
                                <?php $__currentLoopData = array_slice($property->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-6">
                                        <img src="<?php echo e(asset('storage/' . $image)); ?>" class="img-fluid rounded" alt="Property Image" style="height: 80px; object-fit: cover; width: 100%;">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php if(count($property->images) > 4): ?>
                                <small class="text-muted"><?php echo e(__('and :count more images', ['count' => count($property->images) - 4])); ?></small>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted mb-3"><?php echo e(__('No images uploaded yet')); ?></p>
                    <?php endif; ?>

                    <?php if($property->virtual_tour_url): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Virtual Tour')); ?></h6>
                            <a href="<?php echo e($property->virtual_tour_url); ?>" target="_blank" class="btn btn-sm btn-outline-info">
                                <i class="ti ti-external-link"></i> <?php echo e(__('View Virtual Tour')); ?>

                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($property->video_tour_url): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Video Tour')); ?></h6>
                            <a href="<?php echo e($property->video_tour_url); ?>" target="_blank" class="btn btn-sm btn-outline-success">
                                <i class="ti ti-video"></i> <?php echo e(__('Watch Video')); ?>

                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($property->floor_plan): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Floor Plan')); ?></h6>
                            <a href="<?php echo e(asset('storage/' . $property->floor_plan)); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="ti ti-file"></i> <?php echo e(__('View Floor Plan')); ?>

                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($property->brochure): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Brochure')); ?></h6>
                            <a href="<?php echo e(asset('storage/' . $property->brochure)); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                <i class="ti ti-download"></i> <?php echo e(__('Download Brochure')); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Agent Information -->
            <?php if($property->assignedAgent): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><?php echo e(__('Assigned Agent')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avatar me-3">
                                <img src="<?php echo e(asset('assets/images/avatar.png')); ?>" alt="Agent" class="rounded-circle" width="40">
                            </div>
                            <div>
                                <h6 class="mb-0"><?php echo e($property->assignedAgent->name); ?></h6>
                                <small class="text-muted"><?php echo e($property->assignedAgent->email); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Additional Details -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><?php echo e(__('Additional Details')); ?></h5>
                    <?php if(Gate::check('edit properties')): ?>
                        <a href="#" class="btn btn-xs btn-outline-primary customModal" data-size="xl"
                           data-url="<?php echo e(route('properties.edit-additional-details', $property->id)); ?>" data-title="<?php echo e(__('Edit Additional Details')); ?>">
                            <i class="ti ti-pencil"></i> <?php echo e(__('Edit')); ?>

                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if($property->contact_person || $property->contact_phone): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Contact Information')); ?></h6>
                            <?php if($property->contact_person): ?>
                                <p class="mb-1"><strong><?php echo e(__('Contact Person')); ?>:</strong> <?php echo e($property->contact_person); ?></p>
                            <?php endif; ?>
                            <?php if($property->contact_phone): ?>
                                <p class="mb-1"><strong><?php echo e(__('Phone')); ?>:</strong>
                                    <a href="tel:<?php echo e($property->contact_phone); ?>"><?php echo e($property->contact_phone); ?></a>
                                </p>
                            <?php endif; ?>
                            <?php if($property->contact_email): ?>
                                <p class="mb-1"><strong><?php echo e(__('Email')); ?>:</strong>
                                    <a href="mailto:<?php echo e($property->contact_email); ?>"><?php echo e($property->contact_email); ?></a>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if($property->available_from || $property->possession_date): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Timeline')); ?></h6>
                            <?php if($property->available_from): ?>
                                <p class="mb-1"><strong><?php echo e(__('Available From')); ?>:</strong> <?php echo e(\Carbon\Carbon::parse($property->available_from)->format('M d, Y')); ?></p>
                            <?php endif; ?>
                            <?php if($property->possession_date): ?>
                                <p class="mb-1"><strong><?php echo e(__('Possession Date')); ?>:</strong> <?php echo e(\Carbon\Carbon::parse($property->possession_date)->format('M d, Y')); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if($property->rera_number || $property->approval_authority): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Legal Information')); ?></h6>
                            <?php if($property->rera_number): ?>
                                <p class="mb-1"><strong><?php echo e(__('RERA Number')); ?>:</strong> <?php echo e($property->rera_number); ?></p>
                            <?php endif; ?>
                            <?php if($property->approval_authority): ?>
                                <p class="mb-1"><strong><?php echo e(__('Approval Authority')); ?>:</strong> <?php echo e($property->approval_authority); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if($property->internal_notes): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Internal Notes')); ?></h6>
                            <p class="text-muted small"><?php echo e($property->internal_notes); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if($property->special_features): ?>
                        <div class="mb-3">
                            <h6 class="mb-2"><?php echo e(__('Special Features')); ?></h6>
                            <p><?php echo e($property->special_features); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Quick Actions')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($property->virtual_tour_url): ?>
                        <a href="<?php echo e($property->virtual_tour_url); ?>" target="_blank" class="btn btn-outline-info w-100 mb-2">
                            <i class="ti ti-360"></i> <?php echo e(__('Virtual Tour')); ?>

                        </a>
                    <?php endif; ?>
                    <?php if($property->video_url): ?>
                        <a href="<?php echo e($property->video_url); ?>" target="_blank" class="btn btn-outline-success w-100 mb-2">
                            <i class="ti ti-video"></i> <?php echo e(__('Property Video')); ?>

                        </a>
                    <?php endif; ?>
                    <?php if(Gate::check('edit properties')): ?>
                        <a href="#" class="btn btn-outline-primary w-100 mb-2 customModal" data-size="xl"
                           data-url="<?php echo e(route('properties.edit', $property->id)); ?>" data-title="<?php echo e(__('Edit Property')); ?>">
                            <i class="ti ti-pencil"></i> <?php echo e(__('Edit Property')); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Property Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Property Info')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="mb-0"><?php echo e($property->view_count ?? 0); ?></h6>
                            <small class="text-muted"><?php echo e(__('Views')); ?></small>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-0"><?php echo e($property->created_at->diffForHumans()); ?></h6>
                            <small class="text-muted"><?php echo e(__('Listed')); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/show.blade.php ENDPATH**/ ?>