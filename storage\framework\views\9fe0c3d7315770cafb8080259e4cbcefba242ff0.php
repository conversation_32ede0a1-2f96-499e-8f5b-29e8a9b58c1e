<?php echo e(Form::model($property, ['route' => ['properties.update', $property->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data'])); ?>

<div class="modal-body">
    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-12">
            <h6 class="mb-3"><?php echo e(__('Basic Information')); ?></h6>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('project_id', __('Project'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('project_id', $projects->pluck('name', 'id'), null, ['class' => 'form-control select2', 'required' => 'required', 'placeholder' => __('Select Project')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('name', __('Property Name'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('name', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Property Name')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('type', __('Property Type'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('type', \App\Models\Property::$types, null, ['class' => 'form-control select2', 'required' => 'required', 'placeholder' => __('Select Type')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('category', __('Category'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('category', \App\Models\Property::$categories, null, ['class' => 'form-control select2', 'required' => 'required', 'placeholder' => __('Select Category')])); ?>

            </div>
        </div>
        
        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('description', __('Description'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('description', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter Property Description')])); ?>

            </div>
        </div>
        
        <!-- Location Information -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Location Information')); ?></h6>
        </div>
        
        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('address', __('Address'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('address', null, ['class' => 'form-control', 'rows' => 2, 'required' => 'required', 'placeholder' => __('Enter Complete Address')])); ?>

            </div>
        </div>
        
        <div class="col-lg-4 col-md-4">
            <div class="form-group">
                <?php echo e(Form::label('city', __('City'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('city', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter City')])); ?>

            </div>
        </div>
        
        <div class="col-lg-4 col-md-4">
            <div class="form-group">
                <?php echo e(Form::label('state', __('State'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('state', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter State')])); ?>

            </div>
        </div>
        
        <div class="col-lg-4 col-md-4">
            <div class="form-group">
                <?php echo e(Form::label('pincode', __('Pincode'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('pincode', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Pincode')])); ?>

            </div>
        </div>
        
        <!-- Property Specifications -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Property Specifications')); ?></h6>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('carpet_area', __('Carpet Area (sq ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('carpet_area', null, ['class' => 'form-control', 'step' => '0.01', 'placeholder' => __('Enter Carpet Area')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('built_up_area', __('Built-up Area (sq ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('built_up_area', null, ['class' => 'form-control', 'step' => '0.01', 'placeholder' => __('Enter Built-up Area')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('super_area', __('Super Area (sq ft)'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('super_area', null, ['class' => 'form-control', 'step' => '0.01', 'placeholder' => __('Enter Super Area')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('bedrooms', __('Bedrooms'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('bedrooms', null, ['class' => 'form-control', 'min' => '0', 'placeholder' => __('Number of Bedrooms')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('bathrooms', __('Bathrooms'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('bathrooms', null, ['class' => 'form-control', 'min' => '0', 'placeholder' => __('Number of Bathrooms')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('parking_spaces', __('Parking Spaces'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('parking_spaces', null, ['class' => 'form-control', 'min' => '0', 'placeholder' => __('Number of Parking Spaces')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('floor_number', __('Floor Number'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('floor_number', null, ['class' => 'form-control', 'min' => '0', 'placeholder' => __('Floor Number')])); ?>

            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('facing', __('Facing'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('facing', \App\Models\Property::$facings, null, ['class' => 'form-control select2', 'placeholder' => __('Select Facing')])); ?>

            </div>
        </div>
        
        <!-- Pricing Information -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Pricing Information')); ?></h6>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('base_price', __('Base Price'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('base_price', null, ['class' => 'form-control', 'step' => '0.01', 'required' => 'required', 'placeholder' => __('Enter Base Price')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('current_price', __('Current Price'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('current_price', null, ['class' => 'form-control', 'step' => '0.01', 'required' => 'required', 'placeholder' => __('Enter Current Price')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('maintenance_charges', __('Maintenance Charges'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('maintenance_charges', null, ['class' => 'form-control', 'step' => '0.01', 'placeholder' => __('Monthly Maintenance')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <div class="form-check">
                    <?php echo e(Form::checkbox('price_negotiable', 1, null, ['class' => 'form-check-input', 'id' => 'price_negotiable'])); ?>

                    <?php echo e(Form::label('price_negotiable', __('Price Negotiable'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>
        
        <!-- Management -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Management')); ?></h6>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('assigned_agent_id', __('Assigned Agent'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('assigned_agent_id', $agents->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select Agent')])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('status', __('Status'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('status', \App\Models\Property::$statuses, null, ['class' => 'form-control select2'])); ?>

            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <div class="form-check">
                    <?php echo e(Form::checkbox('is_featured', 1, null, ['class' => 'form-check-input', 'id' => 'is_featured'])); ?>

                    <?php echo e(Form::label('is_featured', __('Featured Property'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>
        
        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <div class="form-check">
                    <?php echo e(Form::checkbox('is_premium', 1, null, ['class' => 'form-check-input', 'id' => 'is_premium'])); ?>

                    <?php echo e(Form::label('is_premium', __('Premium Property'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>
        
        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('internal_notes', __('Internal Notes'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('internal_notes', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Internal notes (not visible to clients)')])); ?>

            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Update')); ?>" class="btn btn-primary">
</div>
<?php echo e(Form::close()); ?>

<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/edit.blade.php ENDPATH**/ ?>