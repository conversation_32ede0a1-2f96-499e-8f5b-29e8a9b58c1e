<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Projects')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item" aria-current="page"><?php echo e(__('Projects')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5><?php echo e(__('Manage Projects')); ?></h5>
                        </div>
                        <div class="col-auto">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create projects')): ?>
                                <a href="#" class="btn btn-secondary customModal" data-size="lg"
                                    data-url="<?php echo e(route('projects.create')); ?>" data-title="<?php echo e(__('Create Project')); ?>"> <i
                                        class="ti ti-circle-plus align-text-bottom"></i> <?php echo e(__('Create Project')); ?></a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import project data')): ?>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                                    <i class="ti ti-upload me-1"></i><?php echo e(__('Import')); ?>

                                </button>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export project data')): ?>
                                <a href="<?php echo e(route('projects.export', request()->query())); ?>" class="btn btn-success">
                                    <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

                                </a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view project dashboard')): ?>
                                <a href="<?php echo e(route('projects.dashboard')); ?>" class="btn btn-primary">
                                    <i class="ti ti-chart-line me-1"></i><?php echo e(__('Dashboard')); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="GET" action="<?php echo e(route('projects.index')); ?>">
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <input type="text" name="search" class="form-control" placeholder="<?php echo e(__('Search projects...')); ?>" value="<?php echo e(request('search')); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <select name="status" class="form-select">
                                            <option value=""><?php echo e(__('All Status')); ?></option>
                                            <?php $__currentLoopData = \App\Models\Project::$statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(request('status') == $key ? 'selected' : ''); ?>><?php echo e($status); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select name="type" class="form-select">
                                            <option value=""><?php echo e(__('All Types')); ?></option>
                                            <?php $__currentLoopData = \App\Models\Project::$types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(request('type') == $key ? 'selected' : ''); ?>><?php echo e($type); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select name="priority" class="form-select">
                                            <option value=""><?php echo e(__('All Priorities')); ?></option>
                                            <?php $__currentLoopData = \App\Models\Project::$priorities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $priority): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(request('priority') == $key ? 'selected' : ''); ?>><?php echo e($priority); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="btn-group w-100">
                                            <button type="submit" class="btn btn-outline-primary">
                                                <i class="ti ti-search"></i> <?php echo e(__('Filter')); ?>

                                            </button>
                                            <a href="<?php echo e(route('projects.index')); ?>" class="btn btn-outline-secondary">
                                                <i class="ti ti-refresh"></i> <?php echo e(__('Reset')); ?>

                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Projects Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Project')); ?></th>
                                    <th><?php echo e(__('Type')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Priority')); ?></th>
                                    <th><?php echo e(__('Progress')); ?></th>
                                    <th><?php echo e(__('Budget')); ?></th>
                                    <th><?php echo e(__('Timeline')); ?></th>
                                    <th><?php echo e(__('Manager')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($projects->count() > 0): ?>
                                    <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo e($project->name); ?></h6>
                                                        <small class="text-muted"><?php echo e($project->code); ?></small>
                                                        <?php if($project->location): ?>
                                                            <br><small class="text-muted"><i class="ti ti-map-pin"></i> <?php echo e($project->location); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo e(\App\Models\Project::$types[$project->type] ?? $project->type); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($project->status_color); ?>"><?php echo e(\App\Models\Project::$statuses[$project->status] ?? $project->status); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($project->priority_color); ?>"><?php echo e(\App\Models\Project::$priorities[$project->priority] ?? $project->priority); ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 6px;">
                                                        <div class="progress-bar" role="progressbar" style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                                                    </div>
                                                    <small><?php echo e($project->progress_percentage); ?>%</small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($project->allocated_budget): ?>
                                                    <div>
                                                        <small class="text-muted"><?php echo e(__('Allocated')); ?>: <?php echo e(currency_format_with_sym($project->allocated_budget)); ?></small><br>
                                                        <small class="text-muted"><?php echo e(__('Spent')); ?>: <?php echo e(currency_format_with_sym($project->spent_budget)); ?></small><br>
                                                        <small class="text-<?php echo e($project->budget_utilization > 90 ? 'danger' : ($project->budget_utilization > 75 ? 'warning' : 'success')); ?>">
                                                            <?php echo e($project->budget_utilization); ?>% <?php echo e(__('Used')); ?>

                                                        </small>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Not Set')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($project->planned_start_date && $project->planned_end_date): ?>
                                                    <div>
                                                        <small class="text-muted"><?php echo e($project->planned_start_date->format('M d, Y')); ?> - <?php echo e($project->planned_end_date->format('M d, Y')); ?></small><br>
                                                        <span class="badge bg-<?php echo e($project->is_overdue ? 'danger' : 'success'); ?>"><?php echo e($project->timeline_status); ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Not Set')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($project->projectManager): ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar avatar-sm me-2">
                                                            <img src="<?php echo e(!empty($project->projectManager->avatar) ? asset('storage/uploads/avatar/' . $project->projectManager->avatar) : asset('storage/uploads/avatar/avatar.png')); ?>" alt="<?php echo e($project->projectManager->name); ?>" class="rounded-circle">
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0"><?php echo e($project->projectManager->name); ?></h6>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('Not Assigned')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show projects')): ?>
                                                        <a href="<?php echo e(route('projects.show', $project->id)); ?>" class="btn btn-sm btn-outline-primary" title="<?php echo e(__('View')); ?>">
                                                            <i class="ti ti-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit projects')): ?>
                                                        <a href="#" class="btn btn-sm btn-outline-secondary customModal" data-size="lg"
                                                            data-url="<?php echo e(route('projects.edit', $project->id)); ?>" data-title="<?php echo e(__('Edit Project')); ?>" title="<?php echo e(__('Edit')); ?>">
                                                            <i class="ti ti-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete projects')): ?>
                                                        <form method="POST" action="<?php echo e(route('projects.destroy', $project->id)); ?>" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('<?php echo e(__('Are you sure?')); ?>')" title="<?php echo e(__('Delete')); ?>">
                                                                <i class="ti ti-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-5">
                                            <i class="ti ti-building display-1 text-muted"></i>
                                            <h5 class="mt-3"><?php echo e(__('No projects found')); ?></h5>
                                            <p class="text-muted"><?php echo e(__('Start by creating your first project.')); ?></p>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create projects')): ?>
                                                <a href="#" class="btn btn-primary customModal" data-size="lg"
                                                    data-url="<?php echo e(route('projects.create')); ?>" data-title="<?php echo e(__('Create Project')); ?>">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Project')); ?>

                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($projects->hasPages()): ?>
                        <div class="d-flex justify-content-center mt-3">
                            <?php echo e($projects->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('import project data')): ?>
        <div class="modal fade" id="importModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="<?php echo e(route('projects.import')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="modal-header">
                            <h5 class="modal-title"><?php echo e(__('Import Projects')); ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="file" class="form-label"><?php echo e(__('Excel File')); ?></label>
                                <input type="file" name="file" id="file" class="form-control" accept=".xlsx,.xls,.csv" required>
                                <div class="form-text"><?php echo e(__('Supported formats: Excel (.xlsx, .xls) and CSV')); ?></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Import')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    // Auto-refresh progress bars every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/projects/index.blade.php ENDPATH**/ ?>