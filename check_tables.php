<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'calling_agent_crm',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    echo "🔍 Checking for problematic tables...\n";
    
    // Check if lead_scores table exists
    $tables = $capsule->getConnection()->select("SHOW TABLES LIKE 'lead_scores'");
    if (!empty($tables)) {
        echo "⚠️ Found lead_scores table - dropping it...\n";
        $capsule->getConnection()->statement('DROP TABLE IF EXISTS lead_scores');
        echo "✅ Dropped lead_scores table\n";
    } else {
        echo "ℹ️ lead_scores table does not exist\n";
    }
    
    // List all existing tables
    $allTables = $capsule->getConnection()->select('SHOW TABLES');
    echo "\n📋 Current tables in database:\n";
    foreach ($allTables as $table) {
        $tableName = array_values((array)$table)[0];
        echo "  - {$tableName}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
