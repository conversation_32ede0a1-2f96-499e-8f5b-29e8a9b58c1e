@extends('layouts.app')
@section('page-title')
    {{ __('Role') }}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('role.index') }}">{{ __('Role') }}</a></li>
    <li class="breadcrumb-item" aria-current="Edit"> {{ __('Edit') }}</li>
@endsection

@section('content')
    @php
        $systemModules = \App\Models\User::$systemModules;
    @endphp

    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5>{{ __('Create Role And Permissions') }}</h5>
                        </div>

                    </div>
                </div>
                <div class="card-body">
                    {{Form::model($role,array('route' => array('role.update', $role->id), 'method' => 'PUT')) }}
                    <div class="form-group">
                        {{Form::label('title',__('Role Title'),['class'=>'form-label'])}}
                        {{Form::text('title',$role->name,array('class'=>'form-control','placeholder'=>__('Enter role title'),in_array($role->name,['tenant','maintainer'])?'readonly':''))}}
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-12 col-md-12">
                                @foreach($systemModules as $module)
                                    <div class="row">
                                        <div class="col-12">
                                            <h6 class="text-primary mb-3">
                                                <i class="ti ti-{{ $module === 'form' ? 'forms' : ($module === 'leads' ? 'users' : ($module === 'projects' ? 'briefcase' : ($module === 'properties' ? 'building' : ($module === 'documents' ? 'file-text' : ($module === 'tasks' ? 'check-square' : ($module === 'calling_system' ? 'phone' : 'settings')))))) }}"></i>
                                                {{ ucfirst(str_replace('_', ' ', $module)) }} {{ __('Permissions') }}
                                            </h6>
                                        </div>
                                        @php
                                            $modulePermissions = [];
                                            foreach($permissionList as $permission) {
                                                $permissionName = strtolower($permission->name);
                                                $moduleName = strtolower($module);

                                                // Special handling for form module to exclude non-form permissions
                                                if ($module === 'form') {
                                                    if (str_contains($permissionName, 'form_') ||
                                                        str_contains($permissionName, 'form ') ||
                                                        (str_contains($permissionName, 'form') &&
                                                         !str_contains($permissionName, 'property performance'))) {
                                                        $modulePermissions[] = $permission;
                                                    }
                                                } else {
                                                    if (str_contains($permissionName, $moduleName)) {
                                                        $modulePermissions[] = $permission;
                                                    }
                                                }
                                            }
                                        @endphp

                                        @foreach($modulePermissions as $permission)
                                            <div class="form-check custom-chek form-check-inline col-md-3">
                                                {{ Form::checkbox('user_permission[]', $permission->id, null, ['class'=>'form-check-input', 'id' => $module.'_permission'.$permission->id,in_array($permission->id,$assignPermission)?'checked':'']) }}
                                                {{ Form::label($module.'_permission'.$permission->id, ucfirst(str_replace('_', ' ', $permission->name)), ['class'=>'form-check-label']) }}
                                            </div>
                                        @endforeach

                                        @if(count($modulePermissions) === 0)
                                            <div class="col-12">
                                                <p class="text-muted">{{ __('No permissions available for this module') }}</p>
                                            </div>
                                        @endif
                                    </div>
                                    <hr>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <div class="form-group mt-20 text-end">
                        {{Form::submit(__('Update'),array('class'=>'btn btn-secondary btn-rounded'))}}
                    </div>
                    {{ Form::close() }}
                </div>
            </div>
        </div>
    </div>
@endsection
