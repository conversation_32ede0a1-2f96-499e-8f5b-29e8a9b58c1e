<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Task Details')); ?> - <?php echo e($task->title); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('tasks.index')); ?>"><?php echo e(__('Tasks')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e($task->title); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit tasks')): ?>
            <a href="#" data-url="<?php echo e(route('tasks.edit', $task->id)); ?>" data-size="lg" 
               data-ajax-popup="true" data-title="<?php echo e(__('Edit Task')); ?>" 
               class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="<?php echo e(__('Edit')); ?>">
                <i class="ti ti-pencil"></i>
            </a>
        <?php endif; ?>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('assign tasks')): ?>
            <a href="#" data-url="#" data-size="md" data-ajax-popup="true" 
               data-title="<?php echo e(__('Assign Task')); ?>" class="btn btn-sm btn-info" 
               data-bs-toggle="tooltip" title="<?php echo e(__('Assign')); ?>">
                <i class="ti ti-user-plus"></i>
            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Task Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0"><?php echo e($task->title); ?></h5>
                        </div>
                        <div class="col-auto">
                            <?php
                                $statusColors = [
                                    'not_started' => 'secondary',
                                    'in_progress' => 'warning',
                                    'completed' => 'success',
                                    'cancelled' => 'danger',
                                    'overdue' => 'danger'
                                ];
                                $priorityColors = [
                                    'low' => 'success',
                                    'medium' => 'warning', 
                                    'high' => 'danger',
                                    'urgent' => 'dark'
                                ];
                            ?>
                            <span class="badge bg-<?php echo e($statusColors[$task->status] ?? 'secondary'); ?> me-2">
                                <?php echo e($task->status_label); ?>

                            </span>
                            <span class="badge bg-<?php echo e($priorityColors[$task->priority] ?? 'secondary'); ?>">
                                <?php echo e($task->priority_label); ?>

                            </span>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Task Description -->
                    <?php if($task->description): ?>
                        <div class="mb-4">
                            <h6><?php echo e(__('Description')); ?></h6>
                            <p class="text-muted"><?php echo e($task->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Task Details Grid -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong><?php echo e(__('Category')); ?>:</strong>
                                <span class="badge bg-secondary ms-2"><?php echo e($task->category_label); ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong><?php echo e(__('Created By')); ?>:</strong>
                                <span class="ms-2"><?php echo e($task->createdBy->name); ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong><?php echo e(__('Assigned To')); ?>:</strong>
                                <span class="ms-2">
                                    <?php if($task->assignedTo): ?>
                                        <?php echo e($task->assignedTo->name); ?>

                                    <?php else: ?>
                                        <span class="text-muted"><?php echo e(__('Unassigned')); ?></span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong><?php echo e(__('Due Date')); ?>:</strong>
                                <span class="ms-2 <?php echo e($task->is_overdue ? 'text-danger' : ''); ?>">
                                    <?php if($task->due_date): ?>
                                        <?php echo e($task->due_date->format('M d, Y H:i')); ?>

                                        <?php if($task->is_overdue): ?>
                                            <small class="text-danger">(<?php echo e(__('Overdue')); ?>)</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo e(__('No due date')); ?></span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                        <?php if($task->estimated_hours): ?>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong><?php echo e(__('Estimated Hours')); ?>:</strong>
                                    <span class="ms-2"><?php echo e($task->estimated_hours); ?>h</span>
                                </div>
                            </div>
                        <?php endif; ?>
                        <?php if($task->actual_hours): ?>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong><?php echo e(__('Actual Hours')); ?>:</strong>
                                    <span class="ms-2"><?php echo e($task->actual_hours); ?>h</span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Related Entity -->
                    <?php if($task->lead || $task->project || $task->property): ?>
                        <div class="mb-4">
                            <h6><?php echo e(__('Related Entity')); ?></h6>
                            <?php if($task->lead): ?>
                                <div class="alert alert-primary">
                                    <strong><?php echo e(__('Lead')); ?>:</strong> 
                                    <a href="<?php echo e(route('leads.show', $task->lead->id)); ?>" class="text-primary">
                                        <?php echo e($task->lead->name); ?>

                                    </a>
                                </div>
                            <?php elseif($task->project): ?>
                                <div class="alert alert-info">
                                    <strong><?php echo e(__('Project')); ?>:</strong> 
                                    <a href="<?php echo e(route('projects.show', $task->project->id)); ?>" class="text-info">
                                        <?php echo e($task->project->name); ?>

                                    </a>
                                </div>
                            <?php elseif($task->property): ?>
                                <div class="alert alert-success">
                                    <strong><?php echo e(__('Property')); ?>:</strong> 
                                    <a href="<?php echo e(route('properties.show', $task->property->id)); ?>" class="text-success">
                                        <?php echo e($task->property->title); ?>

                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Task Progress -->
                    <div class="mb-4">
                        <h6><?php echo e(__('Progress')); ?></h6>
                        <div class="progress">
                            <div class="progress-bar bg-<?php echo e($statusColors[$task->status] ?? 'secondary'); ?>" 
                                 role="progressbar" style="width: <?php echo e($task->progress_percentage); ?>%" 
                                 aria-valuenow="<?php echo e($task->progress_percentage); ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo e($task->progress_percentage); ?>%
                            </div>
                        </div>
                    </div>

                    <!-- Status Update Actions -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update task status')): ?>
                        <div class="mb-4">
                            <h6><?php echo e(__('Quick Actions')); ?></h6>
                            <div class="btn-group" role="group">
                                <?php if($task->status == 'not_started'): ?>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="updateTaskStatus('in_progress')">
                                        <i class="ti ti-play"></i> <?php echo e(__('Start Task')); ?>

                                    </button>
                                <?php endif; ?>
                                
                                <?php if(in_array($task->status, ['not_started', 'in_progress'])): ?>
                                    <button type="button" class="btn btn-success btn-sm" onclick="updateTaskStatus('completed')">
                                        <i class="ti ti-check"></i> <?php echo e(__('Mark Complete')); ?>

                                    </button>
                                <?php endif; ?>
                                
                                <?php if($task->status != 'cancelled'): ?>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="updateTaskStatus('cancelled')">
                                        <i class="ti ti-x"></i> <?php echo e(__('Cancel Task')); ?>

                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Task Comments -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Comments & Activity')); ?></h5>
                </div>
                <div class="card-body">
                    <!-- Add Comment Form -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add task comments')): ?>
                        <form id="comment-form" class="mb-4">
                            <?php echo csrf_field(); ?>
                            <div class="form-group">
                                <textarea name="comment" class="form-control" rows="3" 
                                         placeholder="<?php echo e(__('Add a comment...')); ?>" required></textarea>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="is_internal" id="is_internal">
                                <label class="form-check-label" for="is_internal">
                                    <?php echo e(__('Internal comment (not visible to clients)')); ?>

                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="ti ti-send"></i> <?php echo e(__('Add Comment')); ?>

                            </button>
                        </form>
                    <?php endif; ?>

                    <!-- Comments List -->
                    <div id="comments-list">
                        <?php $__currentLoopData = $task->comments->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="comment-item border-bottom pb-3 mb-3">
                                <div class="d-flex">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong><?php echo e($comment->user->name); ?></strong>
                                            <small class="text-muted ms-2"><?php echo e($comment->created_at->diffForHumans()); ?></small>
                                            <?php if($comment->is_internal): ?>
                                                <span class="badge bg-warning ms-2"><?php echo e(__('Internal')); ?></span>
                                            <?php endif; ?>
                                            <?php if($comment->type != 'comment'): ?>
                                                <span class="badge bg-info ms-2"><?php echo e($comment->type_label); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <p class="mb-0"><?php echo e($comment->formatted_comment); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Sidebar -->
        <div class="col-lg-4">
            <!-- Task Attachments -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Attachments')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('upload task attachments')): ?>
                        <form id="attachment-form" enctype="multipart/form-data" class="mb-3">
                            <?php echo csrf_field(); ?>
                            <div class="form-group">
                                <input type="file" name="file" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <input type="text" name="description" class="form-control" 
                                       placeholder="<?php echo e(__('Description (optional)')); ?>">
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="ti ti-upload"></i> <?php echo e(__('Upload')); ?>

                            </button>
                        </form>
                    <?php endif; ?>

                    <!-- Attachments List -->
                    <div id="attachments-list">
                        <?php $__currentLoopData = $task->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="attachment-item d-flex align-items-center justify-content-between mb-2 p-2 border rounded">
                                <div class="d-flex align-items-center">
                                    <i class="<?php echo e($attachment->icon_class); ?> me-2"></i>
                                    <div>
                                        <div class="fw-bold"><?php echo e($attachment->original_filename); ?></div>
                                        <small class="text-muted"><?php echo e($attachment->file_size_human); ?></small>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('download task attachments')): ?>
                                        <a href="<?php echo e(route('tasks.attachments.download', [$task->id, $attachment->id])); ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="ti ti-download"></i>
                                        </a>
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete task attachments')): ?>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteAttachment(<?php echo e($attachment->id); ?>)">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>

            <!-- Task Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Timeline')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title"><?php echo e(__('Task Created')); ?></h6>
                                <p class="timeline-text"><?php echo e(__('by')); ?> <?php echo e($task->createdBy->name); ?></p>
                                <small class="text-muted"><?php echo e($task->created_at->format('M d, Y H:i')); ?></small>
                            </div>
                        </div>

                        <?php if($task->started_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title"><?php echo e(__('Task Started')); ?></h6>
                                    <small class="text-muted"><?php echo e($task->started_at->format('M d, Y H:i')); ?></small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->completed_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title"><?php echo e(__('Task Completed')); ?></h6>
                                    <?php if($task->completion_notes): ?>
                                        <p class="timeline-text"><?php echo e($task->completion_notes); ?></p>
                                    <?php endif; ?>
                                    <small class="text-muted"><?php echo e($task->completed_at->format('M d, Y H:i')); ?></small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($task->cancelled_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title"><?php echo e(__('Task Cancelled')); ?></h6>
                                    <small class="text-muted"><?php echo e($task->cancelled_at->format('M d, Y H:i')); ?></small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        // Update task status
        function updateTaskStatus(status) {
            let notes = prompt('<?php echo e(__("Add notes (optional):")); ?>');
            
            $.ajax({
                url: '<?php echo e(route("tasks.update-status", $task->id)); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    status: status,
                    notes: notes
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('<?php echo e(__("Error updating task status")); ?>');
                    }
                },
                error: function() {
                    alert('<?php echo e(__("Error updating task status")); ?>');
                }
            });
        }

        // Add comment
        $('#comment-form').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: '<?php echo e(route("tasks.add-comment", $task->id)); ?>',
                method: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('<?php echo e(__("Error adding comment")); ?>');
                    }
                },
                error: function() {
                    alert('<?php echo e(__("Error adding comment")); ?>');
                }
            });
        });

        // Upload attachment
        $('#attachment-form').submit(function(e) {
            e.preventDefault();
            
            let formData = new FormData(this);
            
            $.ajax({
                url: '<?php echo e(route("tasks.attachments.store", $task->id)); ?>',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('<?php echo e(__("Error uploading file")); ?>');
                    }
                },
                error: function() {
                    alert('<?php echo e(__("Error uploading file")); ?>');
                }
            });
        });

        // Delete attachment
        function deleteAttachment(attachmentId) {
            if (confirm('<?php echo e(__("Are you sure you want to delete this attachment?")); ?>')) {
                $.ajax({
                    url: '<?php echo e(route("tasks.attachments.destroy", [$task->id, ":id"])); ?>'.replace(':id', attachmentId),
                    method: 'DELETE',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('<?php echo e(__("Error deleting attachment")); ?>');
                        }
                    },
                    error: function() {
                        alert('<?php echo e(__("Error deleting attachment")); ?>');
                    }
                });
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('css-page'); ?>
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }

        .timeline-title {
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .timeline-text {
            margin: 0 0 5px 0;
            font-size: 13px;
        }

        .comment-item:last-child {
            border-bottom: none !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        .attachment-item:hover {
            background-color: #f8f9fa;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/tasks/show.blade.php ENDPATH**/ ?>