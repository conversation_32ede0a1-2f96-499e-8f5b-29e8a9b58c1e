<?php

namespace App\Http\Controllers;

use App\Models\CallLog;
use App\Models\LeadAssignment;
use App\Models\FollowUp;
use App\Models\CallQueue;
use App\Models\Lead;
use App\Models\User;
use App\Models\AgentPerformanceMetric;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;

class CallingDashboardController extends Controller
{
    /**
     * Display the calling system dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        // Check permissions
        if (!Gate::check('calling_system_dashboard_agent') && !Gate::check('calling_system_dashboard_manager') && !Gate::check('calling_system_dashboard_admin')) {
            abort(403, 'Access denied');
        }

        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        // Determine dashboard type based on user role and permissions
        $dashboardType = 'agent';
        if (Gate::check('calling_system_dashboard_admin') && in_array($user->type, ['admin', 'owner'])) {
            $dashboardType = 'admin';
        } elseif (Gate::check('calling_system_dashboard_manager') && in_array($user->type, ['manager', 'team_lead'])) {
            $dashboardType = 'manager';
        }

        $data = [];

        switch ($dashboardType) {
            case 'admin':
                $data = $this->getAdminDashboardData($tenantId, $today, $thisWeek, $thisMonth);
                break;
            case 'manager':
                $data = $this->getManagerDashboardData($user, $tenantId, $today, $thisWeek, $thisMonth);
                break;
            default:
                $data = $this->getAgentDashboardData($user, $tenantId, $today, $thisWeek, $thisMonth);
                break;
        }

        return view('calling.dashboard.index', compact('data', 'dashboardType', 'user'));
    }

    /**
     * Get agent dashboard data
     */
    private function getAgentDashboardData($user, $tenantId, $today, $thisWeek, $thisMonth)
    {
        // Basic stats for agent
        $stats = [
            'today' => [
                'calls_made' => CallLog::where('user_id', $user->id)
                                     ->whereDate('call_started_at', $today)
                                     ->count(),
                'calls_connected' => CallLog::where('user_id', $user->id)
                                          ->whereDate('call_started_at', $today)
                                          ->where('call_outcome', 'connected')
                                          ->count(),
                'leads_contacted' => CallLog::where('user_id', $user->id)
                                          ->whereDate('call_started_at', $today)
                                          ->distinct('lead_id')
                                          ->count('lead_id'),
                'follow_ups_due' => FollowUp::where('agent_id', $user->id)
                                           ->where('status', 'pending')
                                           ->whereDate('scheduled_at', $today)
                                           ->count()
            ],
            'this_week' => [
                'calls_made' => CallLog::where('user_id', $user->id)
                                     ->where('call_started_at', '>=', $thisWeek)
                                     ->count(),
                'leads_converted' => CallLog::where('user_id', $user->id)
                                          ->where('call_started_at', '>=', $thisWeek)
                                          ->whereIn('call_outcome', ['interested', 'converted'])
                                          ->count(),
                'total_talk_time' => CallLog::where('user_id', $user->id)
                                          ->where('call_started_at', '>=', $thisWeek)
                                          ->sum('duration_seconds')
            ],
            'this_month' => [
                'assignments_received' => LeadAssignment::where('agent_id', $user->id)
                                                       ->where('assigned_at', '>=', $thisMonth)
                                                       ->count(),
                'assignments_accepted' => LeadAssignment::where('agent_id', $user->id)
                                                       ->where('assigned_at', '>=', $thisMonth)
                                                       ->where('assignment_status', 'accepted')
                                                       ->count()
            ]
        ];

        // Pending assignments
        $pendingAssignments = LeadAssignment::where('agent_id', $user->id)
                                           ->where('assignment_status', 'pending')
                                           ->with(['lead:id,name,phone,email,company,temperature'])
                                           ->orderBy('priority')
                                           ->orderBy('assigned_at')
                                           ->limit(5)
                                           ->get();

        // Today's follow-ups
        $todayFollowUps = FollowUp::where('agent_id', $user->id)
                                 ->where('status', 'pending')
                                 ->whereDate('scheduled_at', $today)
                                 ->with(['lead:id,name,phone,email,company'])
                                 ->orderBy('scheduled_at')
                                 ->get();

        // Overdue follow-ups
        $overdueFollowUps = FollowUp::where('agent_id', $user->id)
                                   ->where('status', 'pending')
                                   ->where('scheduled_at', '<', now())
                                   ->with(['lead:id,name,phone,email,company'])
                                   ->orderBy('scheduled_at')
                                   ->limit(5)
                                   ->get();

        // Recent calls
        $recentCalls = CallLog::where('user_id', $user->id)
                             ->with(['lead:id,name,phone,email'])
                             ->orderBy('call_started_at', 'desc')
                             ->limit(10)
                             ->get();

        // Performance metric
        $performanceMetric = AgentPerformanceMetric::where('agent_id', $user->id)
                                                  ->where('tenant_id', $tenantId)
                                                  ->where('metric_date', $today)
                                                  ->where('metric_period', 'daily')
                                                  ->first();

        return [
            'stats' => $stats,
            'pending_assignments' => $pendingAssignments,
            'today_follow_ups' => $todayFollowUps,
            'overdue_follow_ups' => $overdueFollowUps,
            'recent_calls' => $recentCalls,
            'performance_metric' => $performanceMetric
        ];
    }

    /**
     * Get manager dashboard data
     */
    private function getManagerDashboardData($user, $tenantId, $today, $thisWeek, $thisMonth)
    {
        // Get team agents
        $teamAgents = User::where('parent_id', $tenantId)
                         ->where('type', 'agent')
                         ->pluck('id');

        // Team performance stats
        $teamStats = [
            'total_agents' => $teamAgents->count(),
            'today' => [
                'total_calls' => CallLog::whereIn('user_id', $teamAgents)
                                       ->where('tenant_id', $tenantId)
                                       ->whereDate('call_started_at', $today)
                                       ->count(),
                'connected_calls' => CallLog::whereIn('user_id', $teamAgents)
                                           ->where('tenant_id', $tenantId)
                                           ->whereDate('call_started_at', $today)
                                           ->where('call_outcome', 'connected')
                                           ->count(),
                'leads_contacted' => CallLog::whereIn('user_id', $teamAgents)
                                           ->where('tenant_id', $tenantId)
                                           ->whereDate('call_started_at', $today)
                                           ->distinct('lead_id')
                                           ->count('lead_id')
            ],
            'this_week' => [
                'total_calls' => CallLog::whereIn('user_id', $teamAgents)
                                       ->where('tenant_id', $tenantId)
                                       ->where('call_started_at', '>=', $thisWeek)
                                       ->count(),
                'leads_converted' => CallLog::whereIn('user_id', $teamAgents)
                                           ->where('tenant_id', $tenantId)
                                           ->where('call_started_at', '>=', $thisWeek)
                                           ->whereIn('call_outcome', ['interested', 'converted'])
                                           ->count()
            ]
        ];

        // Agent performance ranking
        $agentPerformance = User::whereIn('id', $teamAgents)
                               ->with(['performanceMetrics' => function($query) use ($today, $tenantId) {
                                   $query->where('tenant_id', $tenantId)
                                        ->where('metric_date', $today)
                                        ->where('metric_period', 'daily');
                               }])
                               ->get()
                               ->map(function($agent) use ($today, $tenantId) {
                                   $metric = $agent->performanceMetrics->first();
                                   return [
                                       'id' => $agent->id,
                                       'name' => $agent->name,
                                       'email' => $agent->email,
                                       'calls_today' => CallLog::where('user_id', $agent->id)
                                                              ->where('tenant_id', $tenantId)
                                                              ->whereDate('call_started_at', $today)
                                                              ->count(),
                                       'productivity_score' => $metric ? $metric->productivity_score : 0,
                                       'is_top_performer' => $metric ? $metric->is_top_performer : false
                                   ];
                               })
                               ->sortByDesc('productivity_score')
                               ->values();

        // Pending assignments by agent
        $pendingAssignments = LeadAssignment::whereIn('agent_id', $teamAgents)
                                           ->where('tenant_id', $tenantId)
                                           ->where('assignment_status', 'pending')
                                           ->select('agent_id', DB::raw('count(*) as pending_count'))
                                           ->groupBy('agent_id')
                                           ->with('agent:id,name')
                                           ->get();

        return [
            'team_stats' => $teamStats,
            'agent_performance' => $agentPerformance,
            'pending_assignments' => $pendingAssignments
        ];
    }

    /**
     * Get admin dashboard data
     */
    private function getAdminDashboardData($tenantId, $today, $thisWeek, $thisMonth)
    {
        // System overview
        $systemStats = [
            'total_users' => User::where('parent_id', $tenantId)->count(),
            'total_agents' => User::where('parent_id', $tenantId)->where('type', 'agent')->count(),
            'total_leads' => Lead::count(), // Uses global scope for tenant isolation
            'today' => [
                'total_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->whereDate('call_started_at', $today)->count(),
                'total_assignments' => 0, // LeadAssignment table not properly implemented
                'active_agents' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->whereDate('call_started_at', $today)->distinct('user_id')->count('user_id'),
                'new_leads' => Lead::whereDate('created_at', $today)->count()
            ],
            'this_month' => [
                'total_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->where('call_started_at', '>=', $thisMonth)->count(),
                'total_assignments' => 0, // LeadAssignment table not properly implemented
                'leads_converted' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->where('call_started_at', '>=', $thisMonth)->whereIn('call_outcome', ['converted'])->count()
            ]
        ];

        // Top performing agents
        $topAgents = AgentPerformanceMetric::where('tenant_id', $tenantId)
                                          ->where('metric_date', $today)
                                          ->where('metric_period', 'daily')
                                          ->where('is_top_performer', true)
                                          ->with('agent:id,name,email')
                                          ->orderByDesc('productivity_score')
                                          ->limit(10)
                                          ->get();

        // Usage trends
        $usageTrends = [
            'daily_calls' => CallLog::whereHas('user', function($q) use ($tenantId) {
                    $q->where(function($query) use ($tenantId) {
                        $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                    });
                })->where('call_started_at', '>=', $thisMonth)
                  ->select(DB::raw('DATE(call_started_at) as date'), DB::raw('count(*) as calls'))
                  ->groupBy('date')
                  ->orderBy('date')
                  ->get(),
            'daily_assignments' => collect() // LeadAssignment table not properly implemented
        ];

        return [
            'system_stats' => $systemStats,
            'top_agents' => $topAgents,
            'usage_trends' => $usageTrends
        ];
    }

    /**
     * Sync calling system data
     */
    public function sync(Request $request)
    {
        if (!Gate::check('calling_system_dashboard_admin') && !Gate::check('calling_system_dashboard_manager')) {
            abort(403, 'Access denied');
        }

        $user = Auth::user();
        $tenantId = $user->parent_id ?? $user->id;

        try {
            $syncResults = [
                'leads_synced' => 0,
                'assignments_updated' => 0,
                'follow_ups_processed' => 0,
                'queue_items_updated' => 0,
                'performance_calculated' => 0
            ];

            // 1. Sync lead assignments - update overdue assignments
            $overdueAssignments = LeadAssignment::where('assignment_status', 'pending')
                ->where('assigned_at', '<', now()->subHours(2))
                ->whereNull('accepted_at')
                ->get();

            foreach ($overdueAssignments as $assignment) {
                $assignment->update([
                    'assignment_status' => 'expired',
                    'expires_at' => now()
                ]);
                $syncResults['assignments_updated']++;
            }

            // 2. Process overdue follow-ups
            $overdueFollowUps = FollowUp::where('status', 'scheduled')
                ->where('scheduled_date', '<', now())
                ->get();

            foreach ($overdueFollowUps as $followUp) {
                $followUp->update([
                    'status' => 'overdue',
                    'updated_at' => now()
                ]);
                $syncResults['follow_ups_processed']++;
            }

            // 3. Update call queue priorities
            $queueItems = CallQueue::where('status', 'queued')
                ->orderBy('queue_position')
                ->get();

            foreach ($queueItems as $index => $item) {
                $newPosition = $index + 1;
                if ($item->queue_position !== $newPosition) {
                    $item->update(['queue_position' => $newPosition]);
                    $syncResults['queue_items_updated']++;
                }
            }

            // 4. Calculate performance metrics for today
            $agents = User::where('parent_id', $tenantId)
                ->where('type', 'agent')
                ->get();

            foreach ($agents as $agent) {
                $todayCallLogs = CallLog::where('user_id', $agent->id)
                    ->whereDate('call_date', today())
                    ->count();

                if ($todayCallLogs > 0) {
                    // Update or create performance metric
                    AgentPerformanceMetric::updateOrCreate([
                        'tenant_id' => $tenantId,
                        'agent_id' => $agent->id,
                        'metric_date' => today(),
                        'metric_type' => 'daily_calls'
                    ], [
                        'metric_value' => $todayCallLogs,
                        'updated_at' => now()
                    ]);
                    $syncResults['performance_calculated']++;
                }
            }

            // 5. Sync lead data - update lead scores based on recent activities
            $recentLeads = Lead::where('updated_at', '>=', now()->subDays(7))
                ->get();

            foreach ($recentLeads as $lead) {
                // Simple lead scoring based on activities
                $activitiesCount = $lead->activities()->count();
                $callLogsCount = CallLog::where('lead_id', $lead->id)->count();
                $followUpsCount = FollowUp::where('lead_id', $lead->id)->count();

                $newScore = min(100, ($activitiesCount * 10) + ($callLogsCount * 15) + ($followUpsCount * 5));

                if ($lead->score !== $newScore) {
                    $lead->update(['score' => $newScore]);
                    $syncResults['leads_synced']++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Calling system data synchronized successfully!',
                'results' => $syncResults,
                'sync_time' => now()->format('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
