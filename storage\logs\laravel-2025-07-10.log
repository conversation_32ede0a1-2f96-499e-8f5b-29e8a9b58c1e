[2025-07-10 05:26:57] ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:38)"}
[2025-07-10 05:45:07] ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'calling_agent_crm.users' doesn't exist (SQL: select * from `users` where `id` = 2 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'calling_agent_crm.users' doesn't exist (SQL: select * from `users` where `id` = 2 limit 1) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'calling_agent_crm.users' doesn't exist at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-10 05:45:27] ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'calling_agent_crm.users' doesn't exist (SQL: select * from `users` where `id` = 2 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'calling_agent_crm.users' doesn't exist (SQL: select * from `users` where `id` = 2 limit 1) at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'calling_agent_crm.users' doesn't exist at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)"}
[2025-07-10 06:12:22] ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:38)"}
[2025-07-10 06:14:18] ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at D:\\xampp\\htdocs\\calling_agent_crm\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:38)"}
[2025-07-10 06:19:04] INFO: 🚀 IMPORT WIZARD: Method called {"timestamp":"2025-07-10 06:19:04","user_id":2,"user_type":"owner","user_name":"Owner","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"PmLISYn8JwdaYPW0rmovcyrtUdaUHfLCa0zf3vxx"}
[2025-07-10 06:19:05] INFO: 🔐 IMPORT WIZARD: Permission setup completed {"permission_exists":true,"owner_role_exists":true,"permission_assigned":true}
[2025-07-10 06:19:05] INFO: 🔍 IMPORT WIZARD: Permission check results {"has_import_permission":true,"has_import_wizard_permission":true,"is_owner":true,"user_type":"owner","user_id":2}
[2025-07-10 06:19:05] INFO: 📊 IMPORT WIZARD: Loading projects {"tenant_id":1,"user_parent_id":1}
[2025-07-10 06:19:05] INFO: ✅ IMPORT WIZARD: Projects loaded successfully {"projects_count":51,"projects":[{"id":1,"name":"Luxury Residences Project"},{"id":2,"name":"Royal Plaza Industrial"},{"id":8,"name":"Premium Park Retail"},{"id":11,"name":"Green Tower Industrial"},{"id":15,"name":"Royal Square Industrial"},{"id":16,"name":"Grand Heights Retail"},{"id":17,"name":"Luxury Heights Retail"},{"id":20,"name":"Luxury Heights Residential"},{"id":24,"name":"Grand Plaza Residential"},{"id":25,"name":"Royal Residency Mixed Use"},{"id":27,"name":"Urban Residency Mixed Use"},{"id":33,"name":"Urban Residency Residential"},{"id":34,"name":"Royal Gardens Industrial"},{"id":42,"name":"Modern Plaza Residential"},{"id":43,"name":"Luxury Enclave Commercial"},{"id":44,"name":"Smart Heights Commercial"},{"id":46,"name":"Grand Plaza Residential"},{"id":50,"name":"Modern Park Residential"},{"id":6,"name":"Grand Gardens Retail"},{"id":7,"name":"Royal Park Residential"},{"id":18,"name":"Golden City Industrial"},{"id":19,"name":"Royal Plaza Residential"},{"id":21,"name":"Urban City Industrial"},{"id":22,"name":"Grand Gardens Residential"},{"id":23,"name":"Luxury Gardens Mixed Use"},{"id":26,"name":"Elite City Commercial"},{"id":29,"name":"Grand Plaza Commercial"},{"id":30,"name":"Luxury City Retail"},{"id":31,"name":"Elite Residency Mixed Use"},{"id":32,"name":"Elite Complex Commercial"},{"id":48,"name":"Golden City Retail"},{"id":51,"name":"Golden Plaza Retail"},{"id":3,"name":"Luxury Plaza Retail"},{"id":4,"name":"Grand Square Mixed Use"},{"id":5,"name":"Royal Tower Mixed Use"},{"id":9,"name":"Premium Plaza Retail"},{"id":10,"name":"Luxury Residency Mixed Use"},{"id":12,"name":"Golden Enclave Industrial"},{"id":13,"name":"Golden Tower Retail"},{"id":14,"name":"Grand Heights Mixed Use"},{"id":28,"name":"Luxury Gardens Retail"},{"id":35,"name":"Grand Heights Commercial"},{"id":36,"name":"Premium Heights Retail"},{"id":37,"name":"Modern Residency Mixed Use"},{"id":38,"name":"Royal Park Mixed Use"},{"id":39,"name":"Elite Heights Industrial"},{"id":40,"name":"Grand Tower Residential"},{"id":41,"name":"Green Gardens Residential"},{"id":45,"name":"Premium Gardens Retail"},{"id":47,"name":"Premium Enclave Industrial"},{"id":49,"name":"Golden Park Residential"}]}
