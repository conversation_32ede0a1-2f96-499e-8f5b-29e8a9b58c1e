<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class TaskComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'user_id',
        'comment',
        'type',
        'metadata',
        'is_internal',
        'is_pinned'
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_internal' => 'boolean',
        'is_pinned' => 'boolean'
    ];

    // Comment types
    public static $types = [
        'comment' => 'Comment',
        'status_change' => 'Status Change',
        'assignment_change' => 'Assignment Change',
        'system' => 'System'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set user_id when creating
        static::creating(function ($model) {
            if (Auth::check() && !$model->user_id) {
                $model->user_id = Auth::user()->id;
            }
        });
    }

    // Relationships
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_internal', false);
    }

    public function scopeInternal($query)
    {
        return $query->where('is_internal', true);
    }

    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeUserComments($query)
    {
        return $query->where('type', 'comment');
    }

    public function scopeSystemComments($query)
    {
        return $query->whereIn('type', ['status_change', 'assignment_change', 'system']);
    }

    // Accessors
    public function getTypeLabelAttribute()
    {
        return self::$types[$this->type] ?? $this->type;
    }

    public function getIsSystemCommentAttribute()
    {
        return in_array($this->type, ['status_change', 'assignment_change', 'system']);
    }

    public function getFormattedCommentAttribute()
    {
        // For system comments, format based on metadata
        if ($this->is_system_comment && $this->metadata) {
            return $this->formatSystemComment();
        }

        return $this->comment;
    }

    // Business Logic Methods
    public function pin()
    {
        $this->update(['is_pinned' => true]);
        return $this;
    }

    public function unpin()
    {
        $this->update(['is_pinned' => false]);
        return $this;
    }

    public function makeInternal()
    {
        $this->update(['is_internal' => true]);
        return $this;
    }

    public function makePublic()
    {
        $this->update(['is_internal' => false]);
        return $this;
    }

    protected function formatSystemComment()
    {
        $metadata = $this->metadata;
        
        switch ($this->type) {
            case 'status_change':
                if (isset($metadata['old_status']) && isset($metadata['new_status'])) {
                    return "Status changed from {$metadata['old_status']} to {$metadata['new_status']}";
                }
                break;
                
            case 'assignment_change':
                if (isset($metadata['assigned_to'])) {
                    $user = User::find($metadata['assigned_to']);
                    return "Task assigned to " . ($user ? $user->name : 'Unknown User');
                }
                break;
        }

        return $this->comment;
    }
}
