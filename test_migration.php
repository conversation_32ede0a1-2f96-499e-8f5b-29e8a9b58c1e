<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'calling_agent_crm',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Test database connection
    $pdo = $capsule->getConnection()->getPdo();
    echo "✅ Database connection successful\n";
    
    // Test if we can create a simple table
    $capsule->schema()->dropIfExists('test_table');
    $capsule->schema()->create('test_table', function ($table) {
        $table->id();
        $table->string('name');
        $table->timestamps();
    });
    echo "✅ Table creation successful\n";
    
    // Clean up
    $capsule->schema()->dropIfExists('test_table');
    echo "✅ Table cleanup successful\n";
    
    echo "✅ All tests passed - migrations should work\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
