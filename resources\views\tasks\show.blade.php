@extends('layouts.app')

@section('page-title')
    {{ __('Task Details') }} - {{ $task->title }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('tasks.index') }}">{{ __('Tasks') }}</a></li>
    <li class="breadcrumb-item">{{ $task->title }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        @can('edit tasks')
            <a href="#" data-url="{{ route('tasks.edit', $task->id) }}" data-size="lg" 
               data-ajax-popup="true" data-title="{{ __('Edit Task') }}" 
               class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="{{ __('Edit') }}">
                <i class="ti ti-pencil"></i>
            </a>
        @endcan
        
        @can('assign tasks')
            <a href="#" data-url="#" data-size="md" data-ajax-popup="true" 
               data-title="{{ __('Assign Task') }}" class="btn btn-sm btn-info" 
               data-bs-toggle="tooltip" title="{{ __('Assign') }}">
                <i class="ti ti-user-plus"></i>
            </a>
        @endcan
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Task Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">{{ $task->title }}</h5>
                        </div>
                        <div class="col-auto">
                            @php
                                $statusColors = [
                                    'not_started' => 'secondary',
                                    'in_progress' => 'warning',
                                    'completed' => 'success',
                                    'cancelled' => 'danger',
                                    'overdue' => 'danger'
                                ];
                                $priorityColors = [
                                    'low' => 'success',
                                    'medium' => 'warning', 
                                    'high' => 'danger',
                                    'urgent' => 'dark'
                                ];
                            @endphp
                            <span class="badge bg-{{ $statusColors[$task->status] ?? 'secondary' }} me-2">
                                {{ $task->status_label }}
                            </span>
                            <span class="badge bg-{{ $priorityColors[$task->priority] ?? 'secondary' }}">
                                {{ $task->priority_label }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Task Description -->
                    @if($task->description)
                        <div class="mb-4">
                            <h6>{{ __('Description') }}</h6>
                            <p class="text-muted">{{ $task->description }}</p>
                        </div>
                    @endif

                    <!-- Task Details Grid -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>{{ __('Category') }}:</strong>
                                <span class="badge bg-secondary ms-2">{{ $task->category_label }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>{{ __('Created By') }}:</strong>
                                <span class="ms-2">{{ $task->createdBy->name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>{{ __('Assigned To') }}:</strong>
                                <span class="ms-2">
                                    @if($task->assignedTo)
                                        {{ $task->assignedTo->name }}
                                    @else
                                        <span class="text-muted">{{ __('Unassigned') }}</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>{{ __('Due Date') }}:</strong>
                                <span class="ms-2 {{ $task->is_overdue ? 'text-danger' : '' }}">
                                    @if($task->due_date)
                                        {{ $task->due_date->format('M d, Y H:i') }}
                                        @if($task->is_overdue)
                                            <small class="text-danger">({{ __('Overdue') }})</small>
                                        @endif
                                    @else
                                        <span class="text-muted">{{ __('No due date') }}</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                        @if($task->estimated_hours)
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>{{ __('Estimated Hours') }}:</strong>
                                    <span class="ms-2">{{ $task->estimated_hours }}h</span>
                                </div>
                            </div>
                        @endif
                        @if($task->actual_hours)
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>{{ __('Actual Hours') }}:</strong>
                                    <span class="ms-2">{{ $task->actual_hours }}h</span>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Related Entity -->
                    @if($task->lead || $task->project || $task->property)
                        <div class="mb-4">
                            <h6>{{ __('Related Entity') }}</h6>
                            @if($task->lead)
                                <div class="alert alert-primary">
                                    <strong>{{ __('Lead') }}:</strong> 
                                    <a href="{{ route('leads.show', $task->lead->id) }}" class="text-primary">
                                        {{ $task->lead->name }}
                                    </a>
                                </div>
                            @elseif($task->project)
                                <div class="alert alert-info">
                                    <strong>{{ __('Project') }}:</strong> 
                                    <a href="{{ route('projects.show', $task->project->id) }}" class="text-info">
                                        {{ $task->project->name }}
                                    </a>
                                </div>
                            @elseif($task->property)
                                <div class="alert alert-success">
                                    <strong>{{ __('Property') }}:</strong> 
                                    <a href="{{ route('properties.show', $task->property->id) }}" class="text-success">
                                        {{ $task->property->title }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif

                    <!-- Task Progress -->
                    <div class="mb-4">
                        <h6>{{ __('Progress') }}</h6>
                        <div class="progress">
                            <div class="progress-bar bg-{{ $statusColors[$task->status] ?? 'secondary' }}" 
                                 role="progressbar" style="width: {{ $task->progress_percentage }}%" 
                                 aria-valuenow="{{ $task->progress_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                {{ $task->progress_percentage }}%
                            </div>
                        </div>
                    </div>

                    <!-- Status Update Actions -->
                    @can('update task status')
                        <div class="mb-4">
                            <h6>{{ __('Quick Actions') }}</h6>
                            <div class="btn-group" role="group">
                                @if($task->status == 'not_started')
                                    <button type="button" class="btn btn-warning btn-sm" onclick="updateTaskStatus('in_progress')">
                                        <i class="ti ti-play"></i> {{ __('Start Task') }}
                                    </button>
                                @endif
                                
                                @if(in_array($task->status, ['not_started', 'in_progress']))
                                    <button type="button" class="btn btn-success btn-sm" onclick="updateTaskStatus('completed')">
                                        <i class="ti ti-check"></i> {{ __('Mark Complete') }}
                                    </button>
                                @endif
                                
                                @if($task->status != 'cancelled')
                                    <button type="button" class="btn btn-danger btn-sm" onclick="updateTaskStatus('cancelled')">
                                        <i class="ti ti-x"></i> {{ __('Cancel Task') }}
                                    </button>
                                @endif
                            </div>
                        </div>
                    @endcan
                </div>
            </div>

            <!-- Task Comments -->
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Comments & Activity') }}</h5>
                </div>
                <div class="card-body">
                    <!-- Add Comment Form -->
                    @can('add task comments')
                        <form id="comment-form" class="mb-4">
                            @csrf
                            <div class="form-group">
                                <textarea name="comment" class="form-control" rows="3" 
                                         placeholder="{{ __('Add a comment...') }}" required></textarea>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="is_internal" id="is_internal">
                                <label class="form-check-label" for="is_internal">
                                    {{ __('Internal comment (not visible to clients)') }}
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="ti ti-send"></i> {{ __('Add Comment') }}
                            </button>
                        </form>
                    @endcan

                    <!-- Comments List -->
                    <div id="comments-list">
                        @foreach($task->comments->sortByDesc('created_at') as $comment)
                            <div class="comment-item border-bottom pb-3 mb-3">
                                <div class="d-flex">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong>{{ $comment->user->name }}</strong>
                                            <small class="text-muted ms-2">{{ $comment->created_at->diffForHumans() }}</small>
                                            @if($comment->is_internal)
                                                <span class="badge bg-warning ms-2">{{ __('Internal') }}</span>
                                            @endif
                                            @if($comment->type != 'comment')
                                                <span class="badge bg-info ms-2">{{ $comment->type_label }}</span>
                                            @endif
                                        </div>
                                        <p class="mb-0">{{ $comment->formatted_comment }}</p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Sidebar -->
        <div class="col-lg-4">
            <!-- Task Attachments -->
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Attachments') }}</h5>
                </div>
                <div class="card-body">
                    @can('upload task attachments')
                        <form id="attachment-form" enctype="multipart/form-data" class="mb-3">
                            @csrf
                            <div class="form-group">
                                <input type="file" name="file" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <input type="text" name="description" class="form-control" 
                                       placeholder="{{ __('Description (optional)') }}">
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="ti ti-upload"></i> {{ __('Upload') }}
                            </button>
                        </form>
                    @endcan

                    <!-- Attachments List -->
                    <div id="attachments-list">
                        @foreach($task->attachments as $attachment)
                            <div class="attachment-item d-flex align-items-center justify-content-between mb-2 p-2 border rounded">
                                <div class="d-flex align-items-center">
                                    <i class="{{ $attachment->icon_class }} me-2"></i>
                                    <div>
                                        <div class="fw-bold">{{ $attachment->original_filename }}</div>
                                        <small class="text-muted">{{ $attachment->file_size_human }}</small>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    @can('download task attachments')
                                        <a href="{{ route('tasks.attachments.download', [$task->id, $attachment->id]) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="ti ti-download"></i>
                                        </a>
                                    @endcan
                                    @can('delete task attachments')
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteAttachment({{ $attachment->id }})">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    @endcan
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Task Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Timeline') }}</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ __('Task Created') }}</h6>
                                <p class="timeline-text">{{ __('by') }} {{ $task->createdBy->name }}</p>
                                <small class="text-muted">{{ $task->created_at->format('M d, Y H:i') }}</small>
                            </div>
                        </div>

                        @if($task->started_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{ __('Task Started') }}</h6>
                                    <small class="text-muted">{{ $task->started_at->format('M d, Y H:i') }}</small>
                                </div>
                            </div>
                        @endif

                        @if($task->completed_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{ __('Task Completed') }}</h6>
                                    @if($task->completion_notes)
                                        <p class="timeline-text">{{ $task->completion_notes }}</p>
                                    @endif
                                    <small class="text-muted">{{ $task->completed_at->format('M d, Y H:i') }}</small>
                                </div>
                            </div>
                        @endif

                        @if($task->cancelled_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{ __('Task Cancelled') }}</h6>
                                    <small class="text-muted">{{ $task->cancelled_at->format('M d, Y H:i') }}</small>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <script>
        // Update task status
        function updateTaskStatus(status) {
            let notes = prompt('{{ __("Add notes (optional):") }}');
            
            $.ajax({
                url: '{{ route("tasks.update-status", $task->id) }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    status: status,
                    notes: notes
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('{{ __("Error updating task status") }}');
                    }
                },
                error: function() {
                    alert('{{ __("Error updating task status") }}');
                }
            });
        }

        // Add comment
        $('#comment-form').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: '{{ route("tasks.add-comment", $task->id) }}',
                method: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('{{ __("Error adding comment") }}');
                    }
                },
                error: function() {
                    alert('{{ __("Error adding comment") }}');
                }
            });
        });

        // Upload attachment
        $('#attachment-form').submit(function(e) {
            e.preventDefault();
            
            let formData = new FormData(this);
            
            $.ajax({
                url: '{{ route("tasks.attachments.store", $task->id) }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('{{ __("Error uploading file") }}');
                    }
                },
                error: function() {
                    alert('{{ __("Error uploading file") }}');
                }
            });
        });

        // Delete attachment
        function deleteAttachment(attachmentId) {
            if (confirm('{{ __("Are you sure you want to delete this attachment?") }}')) {
                $.ajax({
                    url: '{{ route("tasks.attachments.destroy", [$task->id, ":id"]) }}'.replace(':id', attachmentId),
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('{{ __("Error deleting attachment") }}');
                        }
                    },
                    error: function() {
                        alert('{{ __("Error deleting attachment") }}');
                    }
                });
            }
        }
    </script>
@endpush

@push('css-page')
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }

        .timeline-title {
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .timeline-text {
            margin: 0 0 5px 0;
            font-size: 13px;
        }

        .comment-item:last-child {
            border-bottom: none !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        .attachment-item:hover {
            background-color: #f8f9fa;
        }
    </style>
@endpush
