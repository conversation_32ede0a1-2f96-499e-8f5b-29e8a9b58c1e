<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'category',
        'priority',
        'status',
        'due_date',
        'started_at',
        'completed_at',
        'cancelled_at',
        'created_by',
        'assigned_to',
        'lead_id',
        'project_id',
        'property_id',
        'completion_notes',
        'estimated_hours',
        'actual_hours'
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'estimated_hours' => 'integer',
        'actual_hours' => 'integer'
    ];

    // Task categories
    public static $categories = [
        'lead_task' => 'Lead Task',
        'project_task' => 'Project Task',
        'property_task' => 'Property Task',
        'site_visit_task' => 'Site Visit Task',
        'followup_task' => 'Follow-up Task',
        'general_task' => 'General Task'
    ];

    // Task priorities
    public static $priorities = [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'urgent' => 'Urgent'
    ];

    // Task statuses
    public static $statuses = [
        'not_started' => 'Not Started',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'overdue' => 'Overdue'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-scope queries to current tenant (owner) using created_by and assigned_to relationships
        static::addGlobalScope('tenant', function ($builder) {
            if (Auth::check()) {
                $user = Auth::user();
                $tenantId = $user->parent_id ?? $user->id;
                
                if ($user->type == 'owner') {
                    // Owner sees all tasks in their organization
                    $builder->where(function($q) use ($user, $tenantId) {
                        $q->whereHas('createdBy', function($subQ) use ($tenantId) {
                            $subQ->where(function($query) use ($tenantId) {
                                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                            });
                        })->orWhereHas('assignedTo', function($subQ) use ($tenantId) {
                            $subQ->where(function($query) use ($tenantId) {
                                $query->where('id', $tenantId)->orWhere('parent_id', $tenantId);
                            });
                        });
                    });
                } elseif ($user->type == 'manager') {
                    // Manager sees tasks created by them or assigned to their team
                    $builder->where(function($q) use ($user, $tenantId) {
                        $q->where('created_by', $user->id)
                          ->orWhere('assigned_to', $user->id)
                          ->orWhereHas('assignedTo', function($subQ) use ($user) {
                              $subQ->where('parent_id', $user->id);
                          });
                    });
                } else {
                    // Agents and channel partners see only their assigned tasks or tasks they created
                    $builder->where(function($q) use ($user) {
                        $q->where('assigned_to', $user->id)->orWhere('created_by', $user->id);
                    });
                }
            }
        });

        // Auto-set created_by when creating
        static::creating(function ($model) {
            if (Auth::check() && !$model->created_by) {
                $model->created_by = Auth::user()->id;
            }
        });

        // Auto-update status based on dates
        static::updating(function ($model) {
            if ($model->isDirty('started_at') && $model->started_at && $model->status == 'not_started') {
                $model->status = 'in_progress';
            }
            
            if ($model->isDirty('completed_at') && $model->completed_at) {
                $model->status = 'completed';
            }
            
            if ($model->isDirty('cancelled_at') && $model->cancelled_at) {
                $model->status = 'cancelled';
            }
        });
    }

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(TaskAssignment::class);
    }

    public function currentAssignment(): BelongsTo
    {
        return $this->belongsTo(TaskAssignment::class, 'id', 'task_id')
                    ->where('is_current', true);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(TaskComment::class);
    }

    public function attachments(): HasMany
    {
        return $this->hasMany(TaskAttachment::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('created_by', $userId);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', today())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeDueSoon($query, $days = 3)
    {
        return $query->whereBetween('due_date', [now(), now()->addDays($days)])
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    // Accessors & Mutators
    public function getCategoryLabelAttribute()
    {
        return self::$categories[$this->category] ?? $this->category;
    }

    public function getPriorityLabelAttribute()
    {
        return self::$priorities[$this->priority] ?? $this->priority;
    }

    public function getStatusLabelAttribute()
    {
        return self::$statuses[$this->status] ?? $this->status;
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getDaysUntilDueAttribute()
    {
        if (!$this->due_date) return null;
        
        return now()->diffInDays($this->due_date, false);
    }

    public function getProgressPercentageAttribute()
    {
        switch ($this->status) {
            case 'not_started':
                return 0;
            case 'in_progress':
                return 50;
            case 'completed':
                return 100;
            case 'cancelled':
                return 0;
            default:
                return 0;
        }
    }

    // Business Logic Methods
    public function markAsStarted($notes = null)
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now()
        ]);

        if ($notes) {
            $this->addComment($notes, 'status_change');
        }
    }

    public function markAsCompleted($notes = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'completion_notes' => $notes
        ]);

        if ($notes) {
            $this->addComment($notes, 'status_change');
        }
    }

    public function markAsCancelled($notes = null)
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now()
        ]);

        if ($notes) {
            $this->addComment($notes, 'status_change');
        }
    }

    public function addComment($comment, $type = 'comment', $metadata = null)
    {
        return $this->comments()->create([
            'user_id' => Auth::id(),
            'comment' => $comment,
            'type' => $type,
            'metadata' => $metadata
        ]);
    }

    public function assignTo($userId, $notes = null)
    {
        // Mark previous assignments as not current
        $this->assignments()->update(['is_current' => false]);

        // Create new assignment
        $assignment = $this->assignments()->create([
            'assigned_by' => Auth::id(),
            'assigned_to' => $userId,
            'assigned_at' => now(),
            'assignment_notes' => $notes,
            'is_current' => true
        ]);

        // Update task assigned_to
        $this->update(['assigned_to' => $userId]);

        // Add system comment
        $assignedUser = User::find($userId);
        $this->addComment(
            "Task assigned to {$assignedUser->name}" . ($notes ? ": {$notes}" : ""),
            'assignment_change',
            ['assigned_to' => $userId, 'assigned_by' => Auth::id()]
        );

        return $assignment;
    }

    // Helper methods for task analytics
    public function getDurationInHours()
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->started_at->diffInHours($this->completed_at);
    }

    public function isHighPriority()
    {
        return in_array($this->priority, ['high', 'urgent']);
    }

    public function getRelatedEntityAttribute()
    {
        if ($this->lead_id) {
            return $this->lead;
        } elseif ($this->project_id) {
            return $this->project;
        } elseif ($this->property_id) {
            return $this->property;
        }

        return null;
    }

    public function getRelatedEntityTypeAttribute()
    {
        if ($this->lead_id) return 'lead';
        if ($this->project_id) return 'project';
        if ($this->property_id) return 'property';
        return null;
    }
}
