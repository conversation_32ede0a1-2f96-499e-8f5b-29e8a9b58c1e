<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

echo "=== TASK ROUTES DEBUG ===\n\n";

// Get a user to authenticate as
$user = User::where('type', 'owner')->first();
if (!$user) {
    $user = User::first();
}

if (!$user) {
    echo "ERROR: No users found in database\n";
    exit(1);
}

echo "Authenticating as user: {$user->name} (ID: {$user->id}, Type: {$user->type})\n";
Auth::login($user);

echo "User authenticated: " . (Auth::check() ? 'YES' : 'NO') . "\n";
echo "User ID: " . Auth::id() . "\n";
echo "User Type: " . Auth::user()->type . "\n\n";

// Check permissions
echo "PERMISSION CHECKS:\n";
$permissions = [
    'view tasks',
    'manage tasks',
    'create tasks',
    'edit tasks',
    'delete tasks',
    'view task calendar',
    'view task analytics'
];

foreach ($permissions as $permission) {
    $hasPermission = Gate::check($permission);
    echo "- {$permission}: " . ($hasPermission ? 'YES' : 'NO') . "\n";
}

echo "\nUSER ROLES:\n";
$roles = $user->roles;
foreach ($roles as $role) {
    echo "- {$role->name}\n";
}

echo "\nUSER PERMISSIONS (first 20):\n";
$userPermissions = $user->getAllPermissions()->take(20);
foreach ($userPermissions as $permission) {
    echo "- {$permission->name}\n";
}

// Test the controller methods directly
echo "\n=== TESTING CONTROLLER METHODS ===\n";

try {
    $controller = new App\Http\Controllers\TaskController();
    
    echo "\nTesting calendar method...\n";
    $calendarResponse = $controller->calendar();
    if ($calendarResponse instanceof \Illuminate\View\View) {
        echo "Calendar method: SUCCESS (returned view)\n";
        echo "View name: " . $calendarResponse->getName() . "\n";
    } elseif ($calendarResponse instanceof \Illuminate\Http\RedirectResponse) {
        echo "Calendar method: REDIRECT\n";
        $session = $calendarResponse->getSession();
        if ($session && $session->has('error')) {
            echo "Error message: " . $session->get('error') . "\n";
        }
    } else {
        echo "Calendar method: UNKNOWN RESPONSE TYPE\n";
        echo "Response type: " . get_class($calendarResponse) . "\n";
    }
    
    echo "\nTesting analytics method...\n";
    $analyticsResponse = $controller->analytics();
    if ($analyticsResponse instanceof \Illuminate\View\View) {
        echo "Analytics method: SUCCESS (returned view)\n";
        echo "View name: " . $analyticsResponse->getName() . "\n";
    } elseif ($analyticsResponse instanceof \Illuminate\Http\RedirectResponse) {
        echo "Analytics method: REDIRECT\n";
        $session = $analyticsResponse->getSession();
        if ($session && $session->has('error')) {
            echo "Error message: " . $session->get('error') . "\n";
        }
    } else {
        echo "Analytics method: UNKNOWN RESPONSE TYPE\n";
        echo "Response type: " . get_class($analyticsResponse) . "\n";
    }
    
} catch (Exception $e) {
    echo "ERROR testing controller methods: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
