<?php echo e(Form::model($property, ['route' => ['properties.update-images-media', $property->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data'])); ?>

<div class="modal-body">
    <div class="row">
        <!-- Current Images Section -->
        <?php if($property->images && count($property->images) > 0): ?>
        <div class="col-lg-12">
            <h6 class="mb-3"><?php echo e(__('Current Images')); ?></h6>
            <div class="row">
                <?php $__currentLoopData = $property->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card">
                        <img src="<?php echo e(asset('storage/' . $image)); ?>" class="card-img-top" style="height: 150px; object-fit: cover;" alt="Property Image">
                        <div class="card-body p-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="remove_images[]" value="<?php echo e($index); ?>" id="remove_<?php echo e($index); ?>">
                                <label class="form-check-label small" for="remove_<?php echo e($index); ?>">
                                    <?php echo e(__('Remove this image')); ?>

                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Image Upload Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Add New Images')); ?></h6>
        </div>
        
        <div class="col-12">
            <div class="image-upload-area" id="image-upload-area">
                <div class="upload-content">
                    <div style="font-size: 48px; color: #6c757d; margin-bottom: 1rem;">☁️</div>
                    <h5 class="mt-3 mb-2"><?php echo e(__('Drag & Drop Images Here')); ?></h5>
                    <p class="text-muted mb-3"><?php echo e(__('or click to browse and select images')); ?></p>
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('property-images').click()">
                        ➕ <?php echo e(__('Select Images')); ?>

                    </button>
                    <input type="file" id="property-images" name="images[]" multiple accept="image/*" style="display: none;">
                </div>
            </div>
            <div class="upload-progress" id="upload-progress" style="display: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <span><?php echo e(__('Uploading images...')); ?></span>
                    <span id="progress-text">0%</span>
                </div>
                <div class="progress-bar-custom">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
            </div>
            <small class="form-text text-muted mt-2">
                <?php echo e(__('Supported formats: JPG, PNG, GIF. Maximum 10 images, 5MB each. Tag each image for better organization.')); ?>

            </small>
        </div>

        <div class="col-12 mt-3">
            <div class="image-preview" id="image-preview"></div>
        </div>

        <!-- Floor Plans Section -->
        <div class="col-lg-12 mt-4">
            <h6 class="mb-3"><?php echo e(__('Floor Plans & Brochures')); ?></h6>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('floor_plan', __('Floor Plan'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::file('floor_plan', ['class' => 'form-control', 'accept' => 'image/*,application/pdf'])); ?>

                <?php if($property->floor_plan): ?>
                    <small class="text-muted"><?php echo e(__('Current: ')); ?> 
                        <a href="<?php echo e(asset('storage/' . $property->floor_plan)); ?>" target="_blank"><?php echo e(__('View Floor Plan')); ?></a>
                    </small>
                <?php endif; ?>
            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('brochure', __('Property Brochure'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::file('brochure', ['class' => 'form-control', 'accept' => 'application/pdf,image/*'])); ?>

                <?php if($property->brochure): ?>
                    <small class="text-muted"><?php echo e(__('Current: ')); ?> 
                        <a href="<?php echo e(asset('storage/' . $property->brochure)); ?>" target="_blank"><?php echo e(__('View Brochure')); ?></a>
                    </small>
                <?php endif; ?>
            </div>
        </div>

        <!-- Virtual Tours Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Virtual Tours & Videos')); ?></h6>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('virtual_tour_url', __('360° Virtual Tour URL'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::url('virtual_tour_url', null, ['class' => 'form-control', 'placeholder' => __('Enter virtual tour URL')])); ?>

                <small class="form-text text-muted"><?php echo e(__('Link to 360° virtual tour (e.g., Matterport, Google Street View)')); ?></small>
            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('video_tour_url', __('Video Tour URL'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::url('video_tour_url', null, ['class' => 'form-control', 'placeholder' => __('Enter video tour URL')])); ?>

                <small class="form-text text-muted"><?php echo e(__('Link to property video tour (YouTube, Vimeo, etc.)')); ?></small>
            </div>
        </div>

        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('media_notes', __('Media Notes'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('media_notes', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Additional notes about property media, photography instructions, etc.')])); ?>

            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Update Images & Media')); ?>" class="btn btn-primary">
</div>

<style>
.image-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-upload-area:hover {
    border-color: #007bff;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.image-upload-area.dragover {
    border-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
}

.upload-content {
    text-align: center;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.image-preview-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    background: white;
    border: 1px solid #e9ecef;
}

.image-preview-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.image-preview-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.image-info {
    padding: 12px;
    background: white;
}

.image-tag-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 12px;
    margin-bottom: 8px;
    background: white;
}

.custom-tag-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 11px;
    margin-top: 4px;
    display: none;
}

.custom-tag-input.show {
    display: block;
}

.image-tag-display {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    display: inline-block;
    margin-top: 4px;
}

.remove-image {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
}

.remove-image:hover {
    background: #dc3545;
    transform: scale(1.1);
}

.image-counter {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.upload-progress {
    margin-top: 1rem;
    font-size: 14px;
    color: #6c757d;
}

.progress-bar-custom {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745);
    transition: width 0.3s ease;
    border-radius: 2px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced Image upload functionality with tagging
    const imageUploadArea = document.getElementById('image-upload-area');
    const imageInput = document.getElementById('property-images');
    const imagePreview = document.getElementById('image-preview');
    const uploadProgress = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    
    let selectedImages = [];
    let imageCounter = 0;

    // Image tag options
    const imageTagOptions = [
        { value: '', text: 'Select image type...' },
        { value: 'exterior', text: 'Exterior View' },
        { value: 'interior', text: 'Interior View' },
        { value: 'bedroom', text: 'Bedroom' },
        { value: 'bathroom', text: 'Bathroom' },
        { value: 'kitchen', text: 'Kitchen' },
        { value: 'living_room', text: 'Living Room' },
        { value: 'dining_room', text: 'Dining Room' },
        { value: 'balcony', text: 'Balcony/Terrace' },
        { value: 'garden', text: 'Garden/Lawn' },
        { value: 'parking', text: 'Parking Area' },
        { value: 'entrance', text: 'Entrance/Lobby' },
        { value: 'amenities', text: 'Amenities' },
        { value: 'view', text: 'View from Property' },
        { value: 'floor_plan', text: 'Floor Plan' },
        { value: 'custom', text: 'Custom Tag...' }
    ];

    // Enhanced drag and drop functionality
    imageUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        imageUploadArea.classList.add('dragover');
    });

    imageUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!imageUploadArea.contains(e.relatedTarget)) {
            imageUploadArea.classList.remove('dragover');
        }
    });

    imageUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        imageUploadArea.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        handleImageFiles(files);
    });

    imageUploadArea.addEventListener('click', function(e) {
        if (e.target.tagName !== 'BUTTON') {
            imageInput.click();
        }
    });

    imageInput.addEventListener('change', function() {
        const files = Array.from(this.files);
        handleImageFiles(files);
    });

    function handleImageFiles(files) {
        const validFiles = files.filter(file => {
            if (!file.type.startsWith('image/')) {
                alert('Only image files are allowed!');
                return false;
            }
            if (file.size > 5 * 1024 * 1024) {
                alert(`File ${file.name} is too large. Maximum size is 5MB.`);
                return false;
            }
            if (selectedImages.length >= 10) {
                alert('Maximum 10 images allowed!');
                return false;
            }
            return true;
        });

        if (validFiles.length > 0) {
            showUploadProgress();
            processImagesWithProgress(validFiles);
        }
    }

    function processImagesWithProgress(files) {
        let processed = 0;
        const total = files.length;

        files.forEach((file, index) => {
            setTimeout(() => {
                selectedImages.push({
                    file: file,
                    tag: '',
                    customTag: '',
                    id: ++imageCounter
                });
                
                displayImagePreview(file, imageCounter);
                processed++;
                
                const progress = (processed / total) * 100;
                updateProgress(progress);
                
                if (processed === total) {
                    setTimeout(() => {
                        hideUploadProgress();
                        updateImageInput();
                    }, 500);
                }
            }, index * 100);
        });
    }

    function displayImagePreview(file, imageId) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const previewItem = document.createElement('div');
            previewItem.className = 'image-preview-item';
            previewItem.setAttribute('data-image-id', imageId);
            
            previewItem.innerHTML = `
                <div class="image-counter">#${imageId}</div>
                <img src="${e.target.result}" alt="Preview" loading="lazy">
                <button type="button" class="remove-image" onclick="removeImage(${imageId})" title="Remove image">×</button>
                <div class="image-info">
                    <select class="image-tag-select" onchange="handleTagChange(${imageId}, this.value)">
                        ${imageTagOptions.map(option => 
                            `<option value="${option.value}">${option.text}</option>`
                        ).join('')}
                    </select>
                    <input type="text" class="custom-tag-input" placeholder="Enter custom tag..." 
                           onchange="handleCustomTag(${imageId}, this.value)" style="display: none;">
                    <div class="image-tag-display" id="tag-display-${imageId}" style="display: none;"></div>
                </div>
            `;
            
            imagePreview.appendChild(previewItem);
        };
        reader.readAsDataURL(file);
    }

    window.removeImage = function(imageId) {
        const imageIndex = selectedImages.findIndex(img => img.id === imageId);
        if (imageIndex > -1) {
            selectedImages.splice(imageIndex, 1);
            
            const previewItem = document.querySelector(`[data-image-id="${imageId}"]`);
            if (previewItem) {
                previewItem.remove();
                updateImageInput();
            }
        }
    };

    window.handleTagChange = function(imageId, tagValue) {
        const imageIndex = selectedImages.findIndex(img => img.id === imageId);
        if (imageIndex > -1) {
            selectedImages[imageIndex].tag = tagValue;
            
            const customInput = document.querySelector(`[data-image-id="${imageId}"] .custom-tag-input`);
            const tagDisplay = document.getElementById(`tag-display-${imageId}`);
            
            if (tagValue === 'custom') {
                customInput.style.display = 'block';
                customInput.focus();
                tagDisplay.style.display = 'none';
            } else if (tagValue) {
                customInput.style.display = 'none';
                const tagText = imageTagOptions.find(opt => opt.value === tagValue)?.text || tagValue;
                tagDisplay.textContent = tagText;
                tagDisplay.style.display = 'inline-block';
                selectedImages[imageIndex].customTag = '';
            } else {
                customInput.style.display = 'none';
                tagDisplay.style.display = 'none';
                selectedImages[imageIndex].customTag = '';
            }
        }
    };

    window.handleCustomTag = function(imageId, customTag) {
        const imageIndex = selectedImages.findIndex(img => img.id === imageId);
        if (imageIndex > -1) {
            selectedImages[imageIndex].customTag = customTag;
            
            const tagDisplay = document.getElementById(`tag-display-${imageId}`);
            if (customTag.trim()) {
                tagDisplay.textContent = customTag.trim();
                tagDisplay.style.display = 'inline-block';
            } else {
                tagDisplay.style.display = 'none';
            }
        }
    };

    function updateImageInput() {
        const dt = new DataTransfer();
        selectedImages.forEach(imageData => dt.items.add(imageData.file));
        imageInput.files = dt.files;
        
        // Create hidden inputs for tags
        removeExistingTagInputs();
        selectedImages.forEach((imageData, index) => {
            if (imageData.tag || imageData.customTag) {
                const tagInput = document.createElement('input');
                tagInput.type = 'hidden';
                tagInput.name = `image_tags[${index}]`;
                tagInput.value = imageData.customTag || imageData.tag;
                tagInput.className = 'image-tag-input';
                imageInput.parentNode.appendChild(tagInput);
            }
        });
    }

    function removeExistingTagInputs() {
        const existingInputs = document.querySelectorAll('.image-tag-input');
        existingInputs.forEach(input => input.remove());
    }

    function showUploadProgress() {
        uploadProgress.style.display = 'block';
        updateProgress(0);
    }

    function hideUploadProgress() {
        uploadProgress.style.display = 'none';
    }

    function updateProgress(percentage) {
        progressFill.style.width = percentage + '%';
        progressText.textContent = Math.round(percentage) + '%';
    }
});
</script>

<?php echo e(Form::close()); ?>

<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/edit-images-media.blade.php ENDPATH**/ ?>