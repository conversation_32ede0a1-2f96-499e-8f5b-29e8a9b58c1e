<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class TaskAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'assigned_by',
        'assigned_to',
        'status',
        'assignment_notes',
        'response_notes',
        'assigned_at',
        'responded_at',
        'due_date',
        'is_current',
        'previous_assignment_id'
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'responded_at' => 'datetime',
        'due_date' => 'datetime',
        'is_current' => 'boolean'
    ];

    // Assignment statuses
    public static $statuses = [
        'pending' => 'Pending',
        'accepted' => 'Accepted',
        'rejected' => 'Rejected',
        'reassigned' => 'Reassigned'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set assigned_at when creating
        static::creating(function ($model) {
            if (!$model->assigned_at) {
                $model->assigned_at = now();
            }
        });
    }

    // Relationships
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function previousAssignment(): BelongsTo
    {
        return $this->belongsTo(TaskAssignment::class, 'previous_assignment_id');
    }

    // Scopes
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    // Accessors
    public function getStatusLabelAttribute()
    {
        return self::$statuses[$this->status] ?? $this->status;
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               $this->status == 'pending';
    }

    // Business Logic Methods
    public function accept($notes = null)
    {
        $this->update([
            'status' => 'accepted',
            'responded_at' => now(),
            'response_notes' => $notes
        ]);

        // Add comment to task
        $this->task->addComment(
            "Assignment accepted" . ($notes ? ": {$notes}" : ""),
            'assignment_change',
            ['assignment_id' => $this->id, 'action' => 'accepted']
        );

        return $this;
    }

    public function reject($notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'responded_at' => now(),
            'response_notes' => $notes
        ]);

        // Add comment to task
        $this->task->addComment(
            "Assignment rejected" . ($notes ? ": {$notes}" : ""),
            'assignment_change',
            ['assignment_id' => $this->id, 'action' => 'rejected']
        );

        return $this;
    }

    public function reassign($newUserId, $notes = null)
    {
        // Mark current assignment as reassigned
        $this->update([
            'status' => 'reassigned',
            'responded_at' => now(),
            'response_notes' => $notes,
            'is_current' => false
        ]);

        // Create new assignment
        $newAssignment = TaskAssignment::create([
            'task_id' => $this->task_id,
            'assigned_by' => Auth::id(),
            'assigned_to' => $newUserId,
            'assigned_at' => now(),
            'assignment_notes' => $notes,
            'is_current' => true,
            'previous_assignment_id' => $this->id
        ]);

        // Update task assigned_to
        $this->task->update(['assigned_to' => $newUserId]);

        // Add comment to task
        $newUser = User::find($newUserId);
        $this->task->addComment(
            "Task reassigned to {$newUser->name}" . ($notes ? ": {$notes}" : ""),
            'assignment_change',
            ['assignment_id' => $newAssignment->id, 'action' => 'reassigned', 'previous_assignment_id' => $this->id]
        );

        return $newAssignment;
    }
}
