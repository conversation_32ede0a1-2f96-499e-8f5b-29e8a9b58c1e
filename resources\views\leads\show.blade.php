@extends('layouts.app')

@section('page-title')
    {{ __('Lead Details') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('leads.index') }}">{{ __('Leads') }}</a></li>
    <li class="breadcrumb-item" aria-current="page">{{ $lead->name }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center g-2">
                        <div class="col">
                            <h5>{{ $lead->name }}</h5>
                        </div>
                        <div class="col-auto">
                            @if (Gate::check('create lead tasks'))
                                <a href="#" class="btn btn-success customModal" data-size="lg"
                                    data-url="{{ route('tasks.create', ['lead_id' => $lead->id]) }}" data-title="{{ __('Create Task for Lead') }}">
                                    <i class="ti ti-plus align-text-bottom"></i> {{ __('Create Task') }}
                                </a>
                            @endif
                            @if (Gate::check('edit leads'))
                                <a href="#" class="btn btn-primary customModal" data-size="lg"
                                    data-url="{{ route('leads.edit', $lead->id) }}" data-title="{{ __('Edit Lead') }}">
                                    <i class="ti ti-pencil align-text-bottom"></i> {{ __('Edit') }}
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Name') }}:</strong></td>
                                    <td>{{ $lead->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Phone') }}:</strong></td>
                                    <td>
                                        <a href="tel:{{ $lead->phone }}">{{ $lead->phone }}</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Email') }}:</strong></td>
                                    <td>
                                        @if($lead->email)
                                            <a href="mailto:{{ $lead->email }}">{{ $lead->email }}</a>
                                        @else
                                            <span class="text-muted">{{ __('Not provided') }}</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Alternate Phone') }}:</strong></td>
                                    <td>
                                        @if($lead->alternate_phone)
                                            <a href="tel:{{ $lead->alternate_phone }}">{{ $lead->alternate_phone }}</a>
                                        @else
                                            <span class="text-muted">{{ __('Not provided') }}</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Status') }}:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $lead->status == 'active' ? 'success' : ($lead->status == 'converted' ? 'primary' : 'warning') }}">
                                            {{ ucfirst($lead->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Priority') }}:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $lead->priority == 'high' ? 'danger' : ($lead->priority == 'medium' ? 'warning' : 'info') }}">
                                            {{ ucfirst($lead->priority) }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Score') }}:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $lead->score_grade == 'A' ? 'success' : ($lead->score_grade == 'B' ? 'warning' : 'secondary') }}">
                                            {{ $lead->score ?? 0 }} ({{ $lead->score_grade ?? 'N/A' }})
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Assigned Agent') }}:</strong></td>
                                    <td>
                                        @if($lead->assignedAgent)
                                            {{ $lead->assignedAgent->name }}
                                        @else
                                            <span class="text-muted">{{ __('Unassigned') }}</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('City') }}:</strong></td>
                                    <td>{{ $lead->city ?? __('Not provided') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('State') }}:</strong></td>
                                    <td>{{ $lead->state ?? __('Not provided') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Pincode') }}:</strong></td>
                                    <td>{{ $lead->pincode ?? __('Not provided') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Created') }}:</strong></td>
                                    <td>{{ $lead->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($lead->address)
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>{{ __('Address') }}</h6>
                                <p>{{ $lead->address }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($lead->requirements)
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>{{ __('Requirements') }}</h6>
                                <p>{{ $lead->requirements }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Related Tasks --}}
            @if (Gate::check('view tasks'))
                <div class="card mt-4">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5>{{ __('Related Tasks') }}</h5>
                            </div>
                            <div class="col-auto">
                                @if (Gate::check('create lead tasks'))
                                    <a href="#" class="btn btn-sm btn-primary customModal" data-size="lg"
                                        data-url="{{ route('tasks.create', ['lead_id' => $lead->id]) }}" data-title="{{ __('Create Task for Lead') }}">
                                        <i class="ti ti-plus"></i> {{ __('Create Task') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @php
                            $leadTasks = \App\Models\Task::where('lead_id', $lead->id)
                                                        ->with(['assignedTo', 'createdBy'])
                                                        ->orderBy('created_at', 'desc')
                                                        ->get();
                        @endphp

                        @if($leadTasks->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Task') }}</th>
                                            <th>{{ __('Status') }}</th>
                                            <th>{{ __('Priority') }}</th>
                                            <th>{{ __('Assigned To') }}</th>
                                            <th>{{ __('Due Date') }}</th>
                                            <th>{{ __('Action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($leadTasks as $task)
                                            <tr>
                                                <td>
                                                    <strong>{{ $task->title }}</strong>
                                                    @if($task->description)
                                                        <br><small class="text-muted">{{ Str::limit($task->description, 50) }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    @php
                                                        $statusColors = [
                                                            'not_started' => 'secondary',
                                                            'in_progress' => 'warning',
                                                            'completed' => 'success',
                                                            'cancelled' => 'danger',
                                                            'overdue' => 'danger'
                                                        ];
                                                    @endphp
                                                    <span class="badge bg-{{ $statusColors[$task->status] ?? 'secondary' }}">
                                                        {{ $task->status_label }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @php
                                                        $priorityColors = [
                                                            'low' => 'success',
                                                            'medium' => 'warning',
                                                            'high' => 'danger',
                                                            'urgent' => 'dark'
                                                        ];
                                                    @endphp
                                                    <span class="badge bg-{{ $priorityColors[$task->priority] ?? 'secondary' }}">
                                                        {{ $task->priority_label }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($task->assignedTo)
                                                        {{ $task->assignedTo->name }}
                                                    @else
                                                        <span class="text-muted">{{ __('Unassigned') }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($task->due_date)
                                                        <span class="{{ $task->is_overdue ? 'text-danger' : '' }}">
                                                            {{ $task->due_date->format('M d, Y') }}
                                                        </span>
                                                    @else
                                                        <span class="text-muted">{{ __('No due date') }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <a href="{{ route('tasks.show', $task->id) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="ti ti-clipboard-list text-muted" style="font-size: 3rem;"></i>
                                <h6 class="text-muted mt-2">{{ __('No tasks found for this lead') }}</h6>
                                @if (Gate::check('create lead tasks'))
                                    <a href="#" class="btn btn-primary customModal" data-size="lg"
                                        data-url="{{ route('tasks.create', ['lead_id' => $lead->id]) }}" data-title="{{ __('Create Task for Lead') }}">
                                        <i class="ti ti-plus"></i> {{ __('Create First Task') }}
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
