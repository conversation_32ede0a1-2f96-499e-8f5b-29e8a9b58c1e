<?php echo e(Form::model($property, ['route' => ['properties.update-additional-details', $property->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data'])); ?>

<div class="modal-body">
    <div class="row">
        <!-- Contact Information Section -->
        <div class="col-lg-12">
            <h6 class="mb-3"><?php echo e(__('Contact Information')); ?></h6>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('contact_person', __('Contact Person'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('contact_person', null, ['class' => 'form-control', 'placeholder' => __('Contact person name')])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('contact_phone', __('Contact Phone'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::tel('contact_phone', null, ['class' => 'form-control', 'placeholder' => __('Contact phone number')])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('contact_email', __('Contact Email'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::email('contact_email', null, ['class' => 'form-control', 'placeholder' => __('Contact email address')])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('alternate_phone', __('Alternate Phone'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::tel('alternate_phone', null, ['class' => 'form-control', 'placeholder' => __('Alternate phone number')])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('whatsapp_number', __('WhatsApp Number'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::tel('whatsapp_number', null, ['class' => 'form-control', 'placeholder' => __('WhatsApp number')])); ?>

            </div>
        </div>

        <!-- Availability Timeline Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Availability Timeline')); ?></h6>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('available_from', __('Available From'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::date('available_from', null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('possession_date', __('Possession Date'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::date('possession_date', null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('construction_status', __('Construction Status'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('construction_status', [
                    '' => __('Select construction status'),
                    'under_construction' => __('Under Construction'),
                    'ready_to_move' => __('Ready to Move'),
                    'new_launch' => __('New Launch'),
                    'resale' => __('Resale'),
                    'pre_launch' => __('Pre Launch')
                ], null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('construction_year', __('Construction/Launch Year'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::number('construction_year', null, ['class' => 'form-control', 'min' => '1900', 'max' => date('Y') + 5, 'placeholder' => __('Construction year')])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('age_of_property', __('Age of Property'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('age_of_property', [
                    '' => __('Select property age'),
                    'under_construction' => __('Under Construction'),
                    'new_launch' => __('New Launch'),
                    '0-1' => __('0-1 Years'),
                    '1-5' => __('1-5 Years'),
                    '5-10' => __('5-10 Years'),
                    '10-15' => __('10-15 Years'),
                    '15+' => __('15+ Years')
                ], null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <!-- Legal Documentation Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Legal Documentation')); ?></h6>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('rera_number', __('RERA Registration Number'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('rera_number', null, ['class' => 'form-control', 'placeholder' => __('Enter RERA number')])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('approval_authority', __('Approval Authority'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('approval_authority', null, ['class' => 'form-control', 'placeholder' => __('Approval authority name')])); ?>

            </div>
        </div>

        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('legal_clearances', __('Legal Clearances'), ['class' => 'form-label'])); ?>

                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'approved_plan', null, ['class' => 'form-check-input', 'id' => 'approved_plan'])); ?>

                            <?php echo e(Form::label('approved_plan', __('Approved Plan'), ['class' => 'form-check-label'])); ?>

                        </div>
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'noc_fire', null, ['class' => 'form-check-input', 'id' => 'noc_fire'])); ?>

                            <?php echo e(Form::label('noc_fire', __('Fire NOC'), ['class' => 'form-check-label'])); ?>

                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'noc_electricity', null, ['class' => 'form-check-input', 'id' => 'noc_electricity'])); ?>

                            <?php echo e(Form::label('noc_electricity', __('Electricity NOC'), ['class' => 'form-check-label'])); ?>

                        </div>
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'water_connection', null, ['class' => 'form-check-input', 'id' => 'water_connection'])); ?>

                            <?php echo e(Form::label('water_connection', __('Water Connection'), ['class' => 'form-check-label'])); ?>

                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'sewage_connection', null, ['class' => 'form-check-input', 'id' => 'sewage_connection'])); ?>

                            <?php echo e(Form::label('sewage_connection', __('Sewage Connection'), ['class' => 'form-check-label'])); ?>

                        </div>
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'occupancy_certificate', null, ['class' => 'form-check-input', 'id' => 'occupancy_certificate'])); ?>

                            <?php echo e(Form::label('occupancy_certificate', __('Occupancy Certificate'), ['class' => 'form-check-label'])); ?>

                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'completion_certificate', null, ['class' => 'form-check-input', 'id' => 'completion_certificate'])); ?>

                            <?php echo e(Form::label('completion_certificate', __('Completion Certificate'), ['class' => 'form-check-label'])); ?>

                        </div>
                        <div class="form-check mb-2">
                            <?php echo e(Form::checkbox('legal_clearances[]', 'environmental_clearance', null, ['class' => 'form-check-input', 'id' => 'environmental_clearance'])); ?>

                            <?php echo e(Form::label('environmental_clearance', __('Environmental Clearance'), ['class' => 'form-check-label'])); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Settings Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Additional Settings')); ?></h6>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <div class="form-check form-switch">
                    <?php echo e(Form::checkbox('is_featured', 1, null, ['class' => 'form-check-input', 'id' => 'is_featured'])); ?>

                    <?php echo e(Form::label('is_featured', __('Featured Property'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <div class="form-check form-switch">
                    <?php echo e(Form::checkbox('is_premium', 1, null, ['class' => 'form-check-input', 'id' => 'is_premium'])); ?>

                    <?php echo e(Form::label('is_premium', __('Premium Property'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <div class="form-check form-switch">
                    <?php echo e(Form::checkbox('is_verified', 1, null, ['class' => 'form-check-input', 'id' => 'is_verified'])); ?>

                    <?php echo e(Form::label('is_verified', __('Verified Property'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="form-group">
                <div class="form-check form-switch">
                    <?php echo e(Form::checkbox('is_active', 1, null, ['class' => 'form-check-input', 'id' => 'is_active'])); ?>

                    <?php echo e(Form::label('is_active', __('Active Property'), ['class' => 'form-check-label'])); ?>

                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('assigned_agent_id', __('Assigned Agent'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('assigned_agent_id', $agents->pluck('name', 'id'), null, ['class' => 'form-control select2', 'placeholder' => __('Select Agent')])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('priority_level', __('Priority Level'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('priority_level', [
                    '' => __('Select priority'),
                    'low' => __('Low Priority'),
                    'medium' => __('Medium Priority'),
                    'high' => __('High Priority'),
                    'urgent' => __('Urgent')
                ], null, ['class' => 'form-control'])); ?>

            </div>
        </div>

        <!-- Notes Section -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Notes & Comments')); ?></h6>
        </div>

        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('internal_notes', __('Internal Notes'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('internal_notes', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Internal notes (not visible to clients)')])); ?>

            </div>
        </div>

        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('public_remarks', __('Public Remarks'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('public_remarks', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Public remarks (visible to clients)')])); ?>

            </div>
        </div>

        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('special_features', __('Special Features'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('special_features', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Highlight special features and unique selling points')])); ?>

            </div>
        </div>

        <!-- Marketing Information -->
        <div class="col-lg-12 mt-3">
            <h6 class="mb-3"><?php echo e(__('Marketing Information')); ?></h6>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('marketing_headline', __('Marketing Headline'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('marketing_headline', null, ['class' => 'form-control', 'placeholder' => __('Catchy marketing headline')])); ?>

            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="form-group">
                <?php echo e(Form::label('seo_keywords', __('SEO Keywords'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('seo_keywords', null, ['class' => 'form-control', 'placeholder' => __('SEO keywords (comma separated)')])); ?>

            </div>
        </div>

        <div class="col-lg-12">
            <div class="form-group">
                <?php echo e(Form::label('meta_description', __('Meta Description'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::textarea('meta_description', null, ['class' => 'form-control', 'rows' => 2, 'placeholder' => __('Meta description for SEO (max 160 characters)'), 'maxlength' => '160'])); ?>

                <small class="form-text text-muted"><?php echo e(__('Characters remaining: ')); ?><span id="meta-char-count">160</span></small>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Update Additional Details')); ?>" class="btn btn-primary">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Meta description character counter
    const metaDescription = document.querySelector('[name="meta_description"]');
    const charCount = document.getElementById('meta-char-count');
    
    if (metaDescription && charCount) {
        metaDescription.addEventListener('input', function() {
            const remaining = 160 - this.value.length;
            charCount.textContent = remaining;
            charCount.style.color = remaining < 20 ? '#dc3545' : '#6c757d';
        });
        
        // Initial count
        const remaining = 160 - metaDescription.value.length;
        charCount.textContent = remaining;
    }
});
</script>

<?php echo e(Form::close()); ?>

<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/properties/edit-additional-details.blade.php ENDPATH**/ ?>