<?php echo e(Form::model($lead, ['route' => ['leads.update', $lead->id], 'method' => 'PUT'])); ?>

<div class="modal-body">
    <div class="row">
        <div class="form-group col-md-6">
            <?php echo e(Form::label('name', __('Name'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter lead name'), 'required' => 'required'])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('phone', __('Phone'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::text('phone', null, ['class' => 'form-control', 'placeholder' => __('Enter phone number'), 'required' => 'required'])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('email', __('Email'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter email address')])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('alternate_phone', __('Alternate Phone'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::text('alternate_phone', null, ['class' => 'form-control', 'placeholder' => __('Enter alternate phone')])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('status', __('Status'), ['class' => 'form-label'])); ?>

            <?php echo Form::select('status', \App\Models\Lead::$statuses, null, ['class' => 'form-control hidesearch', 'required' => 'required']); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('priority', __('Priority'), ['class' => 'form-label'])); ?>

            <?php echo Form::select('priority', \App\Models\Lead::$priorities, null, ['class' => 'form-control hidesearch', 'required' => 'required']); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('project_id', __('Project'), ['class' => 'form-label'])); ?>

            <?php echo Form::select('project_id', ['' => __('Select Project')] + \App\Models\Project::active()->pluck('name', 'id')->toArray(), null, ['class' => 'form-control hidesearch']); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('assigned_agent_id', __('Assigned Agent'), ['class' => 'form-label'])); ?>

            <?php echo Form::select('assigned_agent_id', ['' => __('Select Agent')] + \App\Models\User::where('type', 'agent')->where('parent_id', parentId())->pluck('name', 'id')->toArray(), null, ['class' => 'form-control hidesearch']); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('channel_partner_id', __('Channel Partner'), ['class' => 'form-label'])); ?>

            <?php echo Form::select('channel_partner_id', ['' => __('Select Channel Partner')] + \App\Models\User::where('type', 'channel partner')->where('parent_id', parentId())->pluck('name', 'id')->toArray(), null, ['class' => 'form-control hidesearch']); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('budget_min', __('Budget Min'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::number('budget_min', null, ['class' => 'form-control', 'placeholder' => __('Enter minimum budget'), 'step' => '0.01'])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('budget_max', __('Budget Max'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::number('budget_max', null, ['class' => 'form-control', 'placeholder' => __('Enter maximum budget'), 'step' => '0.01'])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('city', __('City'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::text('city', null, ['class' => 'form-control', 'placeholder' => __('Enter city')])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('state', __('State'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::text('state', null, ['class' => 'form-control', 'placeholder' => __('Enter state')])); ?>

        </div>
        <div class="form-group col-md-6">
            <?php echo e(Form::label('pincode', __('Pincode'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::text('pincode', null, ['class' => 'form-control', 'placeholder' => __('Enter pincode')])); ?>

        </div>
        <div class="form-group col-md-12">
            <?php echo e(Form::label('address', __('Address'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::textarea('address', null, ['class' => 'form-control', 'placeholder' => __('Enter address'), 'rows' => 2])); ?>

        </div>
        <div class="form-group col-md-12">
            <?php echo e(Form::label('requirements', __('Requirements'), ['class' => 'form-label'])); ?>

            <?php echo e(Form::textarea('requirements', null, ['class' => 'form-control', 'placeholder' => __('Enter lead requirements'), 'rows' => 2])); ?>

        </div>
    </div>
</div>
<div class="modal-footer">
    <?php echo e(Form::submit(__('Update'), ['class' => 'btn btn-secondary ml-10'])); ?>

</div>
<?php echo e(Form::close()); ?>

<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/leads/edit.blade.php ENDPATH**/ ?>