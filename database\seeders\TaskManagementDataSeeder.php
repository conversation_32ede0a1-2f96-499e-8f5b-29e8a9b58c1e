<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Task;
use App\Models\TaskComment;
use App\Models\TaskAssignment;
use App\Models\User;
use App\Models\Lead;
use App\Models\Project;
use App\Models\Property;
use Carbon\Carbon;
use Faker\Factory as Faker;

class TaskManagementDataSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker::create();
        
        echo "Creating task management test data...\n";

        // Get users for assignments
        $users = User::all();
        $owners = User::where('type', 'owner')->get();
        $agents = User::where('type', 'agent')->get();
        $managers = User::where('type', 'manager')->get();
        
        // Get related entities
        $leads = Lead::limit(20)->get();
        $projects = Project::limit(10)->get();
        $properties = Property::limit(10)->get();

        if ($users->isEmpty()) {
            echo "No users found. Please run user seeders first.\n";
            return;
        }

        // Task templates for different categories
        $taskTemplates = [
            'lead_task' => [
                'Follow up with lead',
                'Schedule property viewing',
                'Send property brochures',
                'Conduct needs assessment call',
                'Prepare property proposal',
                'Schedule site visit',
                'Send follow-up email',
                'Update lead status',
                'Collect required documents',
                'Schedule closing meeting'
            ],
            'project_task' => [
                'Review project requirements',
                'Update project timeline',
                'Coordinate with contractors',
                'Conduct site inspection',
                'Prepare progress report',
                'Schedule team meeting',
                'Review budget allocation',
                'Update project documentation',
                'Coordinate material delivery',
                'Quality assurance check'
            ],
            'property_task' => [
                'Property inspection',
                'Update property listing',
                'Schedule maintenance',
                'Prepare marketing materials',
                'Coordinate photography',
                'Update property documentation',
                'Schedule open house',
                'Property valuation',
                'Coordinate repairs',
                'Update property status'
            ],
            'site_visit_task' => [
                'Schedule site visit with client',
                'Prepare site visit checklist',
                'Coordinate site access',
                'Conduct property walkthrough',
                'Document site conditions',
                'Follow up after site visit',
                'Schedule second viewing',
                'Prepare site visit report'
            ],
            'followup_task' => [
                'Follow up on proposal',
                'Check application status',
                'Send reminder email',
                'Schedule follow-up call',
                'Update client on progress',
                'Send thank you message',
                'Request feedback',
                'Schedule next meeting'
            ],
            'general_task' => [
                'Prepare monthly report',
                'Update CRM data',
                'Team meeting preparation',
                'Training session',
                'Market research',
                'Competitor analysis',
                'Update documentation',
                'System maintenance',
                'Data backup',
                'Performance review'
            ]
        ];

        $priorities = ['low', 'medium', 'high', 'urgent'];
        $statuses = ['not_started', 'in_progress', 'completed', 'cancelled'];
        $categories = array_keys($taskTemplates);

        $tasksCreated = 0;

        // Create tasks for each category
        foreach ($categories as $category) {
            $tasksToCreate = $category === 'general_task' ? 15 : 10;
            
            for ($i = 0; $i < $tasksToCreate; $i++) {
                $owner = $owners->random();
                $assignedUser = $users->where('parent_id', $owner->id)->random() ?? $agents->random() ?? $owner;
                
                // Select related entity based on category
                $relatedEntity = null;
                $leadId = null;
                $projectId = null;
                $propertyId = null;
                
                if ($category === 'lead_task' && $leads->isNotEmpty()) {
                    $relatedEntity = $leads->random();
                    $leadId = $relatedEntity->id;
                } elseif ($category === 'project_task' && $projects->isNotEmpty()) {
                    $relatedEntity = $projects->random();
                    $projectId = $relatedEntity->id;
                } elseif (in_array($category, ['property_task', 'site_visit_task']) && $properties->isNotEmpty()) {
                    $relatedEntity = $properties->random();
                    $propertyId = $relatedEntity->id;
                }

                // Generate realistic due dates
                $dueDate = null;
                if (rand(1, 10) <= 8) { // 80% of tasks have due dates
                    $daysFromNow = rand(-10, 30); // Some overdue, some future
                    $dueDate = Carbon::now()->addDays($daysFromNow);
                }

                // Select status with realistic distribution
                $statusWeights = [
                    'not_started' => 30,
                    'in_progress' => 40,
                    'completed' => 25,
                    'cancelled' => 5
                ];
                $status = $this->weightedRandom($statusWeights);

                // Adjust status for overdue tasks
                if ($dueDate && $dueDate->isPast() && in_array($status, ['not_started', 'in_progress'])) {
                    $status = rand(1, 10) <= 7 ? 'overdue' : 'completed';
                }

                $title = $taskTemplates[$category][array_rand($taskTemplates[$category])];
                
                // Add context to title if related entity exists
                if ($relatedEntity) {
                    if ($category === 'lead_task') {
                        $title .= " - " . $relatedEntity->name;
                    } elseif ($category === 'project_task') {
                        $title .= " - " . $relatedEntity->name;
                    } elseif (in_array($category, ['property_task', 'site_visit_task'])) {
                        $title .= " - " . $relatedEntity->title;
                    }
                }

                $task = Task::create([
                    'title' => $title,
                    'description' => $faker->paragraph(2),
                    'category' => $category,
                    'priority' => $this->weightedRandom([
                        'low' => 20,
                        'medium' => 50,
                        'high' => 25,
                        'urgent' => 5
                    ]),
                    'status' => $status,
                    'due_date' => $dueDate,
                    'created_by' => $owner->id,
                    'assigned_to' => $assignedUser->id,
                    'lead_id' => $leadId,
                    'project_id' => $projectId,
                    'property_id' => $propertyId,
                    'estimated_hours' => rand(1, 20),
                    'actual_hours' => $status === 'completed' ? rand(1, 25) : null,
                    'started_at' => in_array($status, ['in_progress', 'completed', 'cancelled']) ? 
                                   Carbon::now()->subDays(rand(1, 5)) : null,
                    'completed_at' => $status === 'completed' ? 
                                     Carbon::now()->subDays(rand(0, 3)) : null,
                    'cancelled_at' => $status === 'cancelled' ? 
                                     Carbon::now()->subDays(rand(0, 2)) : null,
                    'completion_notes' => in_array($status, ['completed', 'cancelled']) ? 
                                         $faker->sentence() : null,
                    'created_at' => Carbon::now()->subDays(rand(0, 30)),
                    'updated_at' => Carbon::now()->subDays(rand(0, 5))
                ]);

                // Create task assignment record
                TaskAssignment::create([
                    'task_id' => $task->id,
                    'assigned_by' => $owner->id,
                    'assigned_to' => $assignedUser->id,
                    'status' => 'accepted',
                    'assigned_at' => $task->created_at,
                    'responded_at' => $task->created_at->addMinutes(rand(10, 120)),
                    'is_current' => true,
                    'assignment_notes' => $faker->sentence()
                ]);

                // Create some comments for tasks
                $commentCount = rand(0, 5);
                for ($j = 0; $j < $commentCount; $j++) {
                    $commentUser = rand(1, 10) <= 7 ? $assignedUser : $owner;
                    $commentType = rand(1, 10) <= 8 ? 'comment' : 'status_change';
                    
                    TaskComment::create([
                        'task_id' => $task->id,
                        'user_id' => $commentUser->id,
                        'comment' => $commentType === 'comment' ? 
                                   $faker->sentence() : 
                                   "Status updated to " . $task->status,
                        'type' => $commentType,
                        'is_internal' => rand(1, 10) <= 3,
                        'created_at' => $task->created_at->addHours(rand(1, 48))
                    ]);
                }

                $tasksCreated++;
            }
        }

        echo "Created {$tasksCreated} tasks with assignments and comments\n";
        echo "Task management data seeding completed successfully!\n";
    }

    /**
     * Select random item based on weights
     */
    private function weightedRandom($weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($weights as $item => $weight) {
            $currentWeight += $weight;
            if ($random <= $currentWeight) {
                return $item;
            }
        }
        
        return array_key_first($weights);
    }
}
