<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CustomForm;
use App\Models\FormSubmission;
use App\Models\FormField;
use App\Models\FormTemplate;
use App\Models\User;
use Faker\Factory as Faker;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ComprehensiveFormDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker::create();
        
        echo "Creating comprehensive form builder data...\n";
        
        // Get users for tenant logic using Spatie roles
        $owners = User::role('owner')->get();
        $managers = User::role('manager')->get();
        $agents = User::role('agent')->get();
        $allUsers = User::all();
        
        // Create form templates first
        $this->createFormTemplates($faker);
        
        // Create custom forms for each tenant
        foreach ($owners as $owner) {
            $tenantId = $owner->parent_id ?? $owner->id;
            $tenantUsers = User::where(function($query) use ($tenantId) {
                $query->where('id', $tenantId)
                      ->orWhere('parent_id', $tenantId);
            })->get();
            
            $this->createFormsForTenant($faker, $tenantId, $tenantUsers);
        }
        
        echo "Successfully created comprehensive form builder data!\n";
    }
    
    /**
     * Create form templates
     */
    private function createFormTemplates($faker)
    {
        echo "Creating form templates...\n";
        
        $templates = [
            [
                'name' => 'Lead Capture Form',
                'description' => 'Standard lead capture form for real estate inquiries',
                'category' => 'lead_capture',
                'json_structure' => [
                    'fields' => [
                        [
                            'type' => 'text',
                            'name' => 'full_name',
                            'label' => 'Full Name',
                            'required' => true,
                            'placeholder' => 'Enter your full name'
                        ],
                        [
                            'type' => 'email',
                            'name' => 'email',
                            'label' => 'Email Address',
                            'required' => true,
                            'placeholder' => 'Enter your email'
                        ],
                        [
                            'type' => 'phone',
                            'name' => 'phone',
                            'label' => 'Phone Number',
                            'required' => true,
                            'placeholder' => 'Enter your phone number'
                        ],
                        [
                            'type' => 'select',
                            'name' => 'property_type',
                            'label' => 'Property Type Interest',
                            'required' => false,
                            'options' => ['Apartment', 'Villa', 'Commercial', 'Plot']
                        ],
                        [
                            'type' => 'select',
                            'name' => 'budget_range',
                            'label' => 'Budget Range',
                            'required' => false,
                            'options' => ['Under 50L', '50L-1Cr', '1Cr-2Cr', '2Cr+']
                        ],
                        [
                            'type' => 'textarea',
                            'name' => 'message',
                            'label' => 'Additional Requirements',
                            'required' => false,
                            'placeholder' => 'Tell us about your requirements'
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Property Inquiry Form',
                'description' => 'Detailed property inquiry form with specific requirements',
                'category' => 'contact',
                'json_structure' => [
                    'fields' => [
                        [
                            'type' => 'text',
                            'name' => 'name',
                            'label' => 'Name',
                            'required' => true
                        ],
                        [
                            'type' => 'email',
                            'name' => 'email',
                            'label' => 'Email',
                            'required' => true
                        ],
                        [
                            'type' => 'phone',
                            'name' => 'phone',
                            'label' => 'Phone',
                            'required' => true
                        ],
                        [
                            'type' => 'text',
                            'name' => 'property_id',
                            'label' => 'Property ID',
                            'required' => false
                        ],
                        [
                            'type' => 'select',
                            'name' => 'inquiry_type',
                            'label' => 'Inquiry Type',
                            'required' => true,
                            'options' => ['Buy', 'Rent', 'Investment', 'Site Visit']
                        ],
                        [
                            'type' => 'date',
                            'name' => 'preferred_date',
                            'label' => 'Preferred Visit Date',
                            'required' => false
                        ],
                        [
                            'type' => 'textarea',
                            'name' => 'specific_requirements',
                            'label' => 'Specific Requirements',
                            'required' => false
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Site Visit Booking',
                'description' => 'Form for booking property site visits',
                'category' => 'booking',
                'json_structure' => [
                    'fields' => [
                        [
                            'type' => 'text',
                            'name' => 'visitor_name',
                            'label' => 'Visitor Name',
                            'required' => true
                        ],
                        [
                            'type' => 'email',
                            'name' => 'email',
                            'label' => 'Email',
                            'required' => true
                        ],
                        [
                            'type' => 'phone',
                            'name' => 'phone',
                            'label' => 'Phone',
                            'required' => true
                        ],
                        [
                            'type' => 'select',
                            'name' => 'property_location',
                            'label' => 'Property Location',
                            'required' => true,
                            'options' => ['Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Chennai']
                        ],
                        [
                            'type' => 'date',
                            'name' => 'visit_date',
                            'label' => 'Preferred Visit Date',
                            'required' => true
                        ],
                        [
                            'type' => 'select',
                            'name' => 'visit_time',
                            'label' => 'Preferred Time',
                            'required' => true,
                            'options' => ['10:00 AM', '12:00 PM', '2:00 PM', '4:00 PM', '6:00 PM']
                        ],
                        [
                            'type' => 'number',
                            'name' => 'group_size',
                            'label' => 'Number of Visitors',
                            'required' => false,
                            'min' => 1,
                            'max' => 10
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Callback Request',
                'description' => 'Simple callback request form',
                'category' => 'contact',
                'json_structure' => [
                    'fields' => [
                        [
                            'type' => 'text',
                            'name' => 'name',
                            'label' => 'Name',
                            'required' => true
                        ],
                        [
                            'type' => 'phone',
                            'name' => 'phone',
                            'label' => 'Phone Number',
                            'required' => true
                        ],
                        [
                            'type' => 'select',
                            'name' => 'preferred_time',
                            'label' => 'Preferred Call Time',
                            'required' => false,
                            'options' => ['Morning (9-12)', 'Afternoon (12-5)', 'Evening (5-8)']
                        ],
                        [
                            'type' => 'textarea',
                            'name' => 'purpose',
                            'label' => 'Purpose of Call',
                            'required' => false
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Investment Inquiry',
                'description' => 'Form for investment opportunity inquiries',
                'category' => 'application',
                'json_structure' => [
                    'fields' => [
                        [
                            'type' => 'text',
                            'name' => 'investor_name',
                            'label' => 'Investor Name',
                            'required' => true
                        ],
                        [
                            'type' => 'email',
                            'name' => 'email',
                            'label' => 'Email',
                            'required' => true
                        ],
                        [
                            'type' => 'phone',
                            'name' => 'phone',
                            'label' => 'Phone',
                            'required' => true
                        ],
                        [
                            'type' => 'select',
                            'name' => 'investment_type',
                            'label' => 'Investment Type',
                            'required' => true,
                            'options' => ['Residential', 'Commercial', 'Mixed Use', 'Land']
                        ],
                        [
                            'type' => 'select',
                            'name' => 'investment_budget',
                            'label' => 'Investment Budget',
                            'required' => true,
                            'options' => ['1-5 Cr', '5-10 Cr', '10-25 Cr', '25+ Cr']
                        ],
                        [
                            'type' => 'select',
                            'name' => 'investment_timeline',
                            'label' => 'Investment Timeline',
                            'required' => false,
                            'options' => ['Immediate', '3 months', '6 months', '1 year']
                        ],
                        [
                            'type' => 'textarea',
                            'name' => 'investment_goals',
                            'label' => 'Investment Goals',
                            'required' => false
                        ]
                    ]
                ]
            ]
        ];
        
        foreach ($templates as $templateData) {
            FormTemplate::create([
                'name' => $templateData['name'],
                'description' => $templateData['description'],
                'category' => $templateData['category'],
                'json_structure' => $templateData['json_structure'],
                'is_system_template' => true,
                'is_active' => true,
                'usage_count' => $faker->numberBetween(5, 50),
                'rating' => $faker->randomFloat(1, 4.0, 5.0),
                'rating_count' => $faker->numberBetween(10, 100),
                'created_by' => 1, // System user
            ]);
        }
    }

    /**
     * Create forms for a specific tenant
     */
    private function createFormsForTenant($faker, $tenantId, $tenantUsers)
    {
        echo "Creating forms for tenant {$tenantId}...\n";

        $templates = FormTemplate::where('is_active', true)->get();
        $createdForms = [];

        // Create 8-12 forms per tenant
        $formCount = $faker->numberBetween(8, 12);

        for ($i = 0; $i < $formCount; $i++) {
            $template = $faker->randomElement($templates);
            $creator = $faker->randomElement($tenantUsers);

            $form = CustomForm::create([
                'title' => $this->generateFormTitle($faker, $template->category),
                'description' => $faker->paragraph,
                'json_structure' => $this->customizeFormStructure($faker, $template->json_structure),
                'status' => $faker->randomElement(['draft', 'published', 'published', 'published']), // More published
                'slug' => Str::slug($this->generateFormTitle($faker, $template->category)) . '-' . time() . '-' . $faker->randomNumber(4),
                'settings' => [
                    'template_id' => $template->id,
                    'template_name' => $template->name,
                    'notifications' => [
                        'email_on_submit' => $faker->boolean(80),
                        'notify_admin' => $faker->boolean(90),
                        'auto_responder' => $faker->boolean(60)
                    ],
                    'styling' => [
                        'theme' => $faker->randomElement(['default', 'modern', 'minimal', 'corporate']),
                        'primary_color' => $faker->hexColor,
                        'button_style' => $faker->randomElement(['rounded', 'square', 'pill'])
                    ]
                ],
                'meta_title' => $this->generateFormTitle($faker, $template->category),
                'meta_description' => $faker->sentence,
                'meta_keywords' => implode(', ', $faker->words(5)),
                'is_public' => $faker->boolean(70),
                'public_url' => $faker->boolean(70) ? $faker->url : null,
                'require_login' => $faker->boolean(30),
                'allow_multiple_submissions' => $faker->boolean(60),
                'enable_captcha' => $faker->boolean(40),
                'enable_notifications' => $faker->boolean(80),
                'success_message' => $faker->sentence,
                'redirect_url' => $faker->boolean(30) ? $faker->url : null,
                'expires_at' => $faker->boolean(20) ? $faker->dateTimeBetween('now', '+6 months') : null,
                'max_submissions' => $faker->boolean(30) ? $faker->numberBetween(100, 1000) : null,
                'view_count' => $faker->numberBetween(50, 500),
                'submission_count' => $faker->numberBetween(5, 100),
                'conversion_rate' => $faker->randomFloat(2, 5, 25),
                'last_submission_at' => $faker->dateTimeBetween('-1 month', 'now'),
                'created_by' => $creator->id,
                'created_at' => $faker->dateTimeBetween('-3 months', 'now'),
                'updated_at' => $faker->dateTimeBetween('-1 month', 'now'),
            ]);

            $createdForms[] = $form;

            // Create form submissions
            $this->createFormSubmissions($faker, $form, $tenantUsers);
        }

        return $createdForms;
    }

    /**
     * Generate form title based on category
     */
    private function generateFormTitle($faker, $category)
    {
        $titles = [
            'lead_capture' => [
                'Property Interest Form',
                'Lead Capture Form',
                'Real Estate Inquiry',
                'Property Lead Form',
                'Interest Registration'
            ],
            'contact' => [
                'Property Details Inquiry',
                'Specific Property Query',
                'Property Information Request',
                'Detailed Property Inquiry',
                'Request Callback',
                'Call Me Back',
                'Schedule Call',
                'Callback Request Form'
            ],
            'booking' => [
                'Site Visit Booking',
                'Property Tour Request',
                'Schedule Site Visit',
                'Book Property Visit'
            ],
            'application' => [
                'Investment Inquiry',
                'Investment Opportunity',
                'Investor Registration',
                'Investment Interest Form'
            ],
            'quote_request' => [
                'Property Quote Request',
                'Price Inquiry Form',
                'Get Quote Form'
            ]
        ];

        $categoryTitles = $titles[$category] ?? $titles['lead_capture'];
        return $faker->randomElement($categoryTitles);
    }

    /**
     * Customize form structure
     */
    private function customizeFormStructure($faker, $baseStructure)
    {
        $structure = $baseStructure;

        // Randomly modify some field properties
        foreach ($structure['fields'] as &$field) {
            // Add random validation
            if ($faker->boolean(30)) {
                $field['validation'] = $faker->randomElement(['required', 'email', 'numeric', 'min:3', 'max:100']);
            }

            // Add random help text
            if ($faker->boolean(40)) {
                $field['help_text'] = $faker->sentence;
            }

            // Add random CSS classes
            if ($faker->boolean(20)) {
                $field['css_class'] = $faker->randomElement(['form-control-lg', 'form-control-sm', 'custom-input']);
            }
        }

        return $structure;
    }

    /**
     * Create form fields
     */
    private function createFormFields($faker, $form, $tenantId)
    {
        $fields = $form->json_structure['fields'] ?? [];

        foreach ($fields as $index => $fieldData) {
            FormField::create([
                'tenant_id' => $tenantId,
                'form_id' => $form->id,
                'field_type' => $fieldData['type'],
                'field_name' => $fieldData['name'],
                'label' => $fieldData['label'],
                'placeholder' => $fieldData['placeholder'] ?? null,
                'field_options' => isset($fieldData['options']) ? $fieldData['options'] : null,
                'validation_rules' => [
                    'required' => $fieldData['required'] ?? false,
                    'min_length' => $fieldData['min'] ?? null,
                    'max_length' => $fieldData['max'] ?? null,
                    'pattern' => $fieldData['pattern'] ?? null
                ],
                'field_order' => $index + 1,
                'is_required' => $fieldData['required'] ?? false,
                'help_text' => $fieldData['help_text'] ?? null,
                'css_class' => $fieldData['css_class'] ?? null,
                'visibility_conditions' => $faker->boolean(10) ? ['show_if' => 'field_name', 'equals' => 'value'] : null,
                'is_conditional' => $faker->boolean(10),
                'field_width' => $faker->randomElement(['full', 'half', 'third', 'quarter']),
                'min_length' => $fieldData['min'] ?? null,
                'max_length' => $fieldData['max'] ?? null,
            ]);
        }
    }

    /**
     * Create form submissions
     */
    private function createFormSubmissions($faker, $form, $tenantUsers)
    {
        // Create 5-25 submissions per form
        $submissionCount = $faker->numberBetween(5, 25);

        for ($i = 0; $i < $submissionCount; $i++) {
            $submissionData = $this->generateSubmissionData($faker, $form);

            FormSubmission::create([
                'tenant_id' => $tenantUsers->first()->parent_id ?? $tenantUsers->first()->id,
                'form_id' => $form->id,
                'submission_data' => $submissionData,
                'ip_address' => $faker->ipv4,
                'utm_parameters' => $faker->boolean(40) ? [
                    'utm_source' => $faker->randomElement(['google', 'facebook', 'website', 'email']),
                    'utm_medium' => $faker->randomElement(['cpc', 'organic', 'social', 'email']),
                    'utm_campaign' => $faker->words(3, true),
                    'utm_content' => $faker->boolean(50) ? $faker->word : null,
                    'utm_term' => $faker->boolean(50) ? $faker->word : null
                ] : null,
                'device_info' => [
                    'browser' => $faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
                    'os' => $faker->randomElement(['Windows', 'macOS', 'Linux', 'iOS', 'Android']),
                    'device_type' => $faker->randomElement(['desktop', 'mobile', 'tablet'])
                ],
                'referrer_url' => $faker->boolean(60) ? $faker->url : null,
                'user_agent' => $faker->userAgent,
                'lead_id' => null, // Would be set if converted
                'converted_to_lead' => $faker->boolean(30),
                'converted_at' => $faker->boolean(30) ? $faker->dateTimeBetween('-1 month', 'now') : null,
                'converted_by' => $faker->boolean(30) ? $faker->randomElement($tenantUsers)->id : null,
                'status' => $faker->randomElement(['new', 'processed', 'converted', 'spam', 'archived']),
                'is_spam' => $faker->boolean(5),
                'spam_score' => $faker->randomFloat(2, 0, 1),
                'notes' => $faker->boolean(40) ? $faker->paragraph : null,
                'contact_name' => $submissionData['full_name'] ?? $submissionData['name'] ?? $submissionData['visitor_name'] ?? null,
                'contact_email' => $submissionData['email'] ?? null,
                'contact_phone' => $submissionData['phone'] ?? null,
                'form_completion_time' => $faker->numberBetween(30, 600), // 30 seconds to 10 minutes
                'field_interaction_data' => [
                    'total_time' => $faker->numberBetween(30, 600),
                    'field_focus_times' => [],
                    'form_abandonment_points' => []
                ],
                'created_at' => $faker->dateTimeBetween('-2 months', 'now'),
                'updated_at' => $faker->dateTimeBetween('-1 month', 'now'),
            ]);
        }
    }

    /**
     * Generate realistic submission data based on form structure
     */
    private function generateSubmissionData($faker, $form)
    {
        $fields = $form->json_structure['fields'] ?? [];
        $submissionData = [];

        foreach ($fields as $field) {
            $fieldName = $field['name'];
            $fieldType = $field['type'];

            switch ($fieldType) {
                case 'text':
                    if (strpos($fieldName, 'name') !== false) {
                        $submissionData[$fieldName] = $faker->name;
                    } elseif (strpos($fieldName, 'company') !== false) {
                        $submissionData[$fieldName] = $faker->company;
                    } else {
                        $submissionData[$fieldName] = $faker->words(3, true);
                    }
                    break;

                case 'email':
                    $submissionData[$fieldName] = $faker->email;
                    break;

                case 'phone':
                    $submissionData[$fieldName] = $faker->phoneNumber;
                    break;

                case 'number':
                    $min = $field['min'] ?? 1;
                    $max = $field['max'] ?? 100;
                    $submissionData[$fieldName] = $faker->numberBetween($min, $max);
                    break;

                case 'select':
                    if (isset($field['options']) && !empty($field['options'])) {
                        $submissionData[$fieldName] = $faker->randomElement($field['options']);
                    }
                    break;

                case 'textarea':
                    $submissionData[$fieldName] = $faker->paragraph;
                    break;

                case 'date':
                    $submissionData[$fieldName] = $faker->date();
                    break;

                case 'checkbox':
                    $submissionData[$fieldName] = $faker->boolean();
                    break;

                case 'radio':
                    if (isset($field['options']) && !empty($field['options'])) {
                        $submissionData[$fieldName] = $faker->randomElement($field['options']);
                    }
                    break;

                default:
                    $submissionData[$fieldName] = $faker->word;
            }
        }

        return $submissionData;
    }
}
