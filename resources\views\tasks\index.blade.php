@extends('layouts.admin')

@section('page-title')
    {{ __('Task Management') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Task Management') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        @can('create tasks')
            <a href="#" data-url="{{ route('tasks.create') }}" data-size="lg" data-ajax-popup="true" 
               data-title="{{ __('Create New Task') }}" data-bs-toggle="tooltip" title="{{ __('Create') }}" 
               class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i>
            </a>
        @endcan
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Task Statistics -->
        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Total Tasks') }}</h6>
                            <h3 class="text-primary">{{ $stats['total'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-primary-light ti ti-list"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('In Progress') }}</h6>
                            <h3 class="text-warning">{{ $stats['in_progress'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-warning-light ti ti-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Overdue') }}</h6>
                            <h3 class="text-danger">{{ $stats['overdue'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-danger-light ti ti-alert-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="card comp-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-b-20">{{ __('Completed') }}</h6>
                            <h3 class="text-success">{{ $stats['completed'] }}</h3>
                        </div>
                        <div class="col-auto">
                            <i class="bg-success-light ti ti-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <div class="row">
                        <div class="col-lg-9">
                            <h5>{{ __('Tasks') }}</h5>
                        </div>
                        <div class="col-lg-3">
                            <div class="float-end">
                                @can('create tasks')
                                    <a href="#" data-url="{{ route('tasks.create') }}" data-size="lg" 
                                       data-ajax-popup="true" data-title="{{ __('Create New Task') }}" 
                                       class="btn btn-sm btn-primary">
                                        <i class="ti ti-plus"></i> {{ __('Create Task') }}
                                    </a>
                                @endcan
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body">
                    <form method="GET" action="{{ route('tasks.index') }}">
                        <div class="row">
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">{{ __('All Status') }}</option>
                                    @foreach(\App\Models\Task::$statuses as $key => $status)
                                        <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                                            {{ __($status) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="priority" class="form-control">
                                    <option value="">{{ __('All Priority') }}</option>
                                    @foreach(\App\Models\Task::$priorities as $key => $priority)
                                        <option value="{{ $key }}" {{ request('priority') == $key ? 'selected' : '' }}>
                                            {{ __($priority) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="category" class="form-control">
                                    <option value="">{{ __('All Categories') }}</option>
                                    @foreach(\App\Models\Task::$categories as $key => $category)
                                        <option value="{{ $key }}" {{ request('category') == $key ? 'selected' : '' }}>
                                            {{ __($category) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="assigned_to" class="form-control">
                                    <option value="">{{ __('All Assignees') }}</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ request('assigned_to') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" 
                                       placeholder="{{ __('Search tasks...') }}" value="{{ request('search') }}">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Tasks Table -->
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>{{ __('Task') }}</th>
                                    <th>{{ __('Category') }}</th>
                                    <th>{{ __('Priority') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Assigned To') }}</th>
                                    <th>{{ __('Due Date') }}</th>
                                    <th>{{ __('Related') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tasks as $task)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $task->title }}</strong>
                                                @if($task->description)
                                                    <br><small class="text-muted">{{ Str::limit($task->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $task->category_label }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $priorityColors = [
                                                    'low' => 'success',
                                                    'medium' => 'warning', 
                                                    'high' => 'danger',
                                                    'urgent' => 'dark'
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $priorityColors[$task->priority] ?? 'secondary' }}">
                                                {{ $task->priority_label }}
                                            </span>
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'not_started' => 'secondary',
                                                    'in_progress' => 'warning',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger',
                                                    'overdue' => 'danger'
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$task->status] ?? 'secondary' }}">
                                                {{ $task->status_label }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($task->assignedTo)
                                                {{ $task->assignedTo->name }}
                                            @else
                                                <span class="text-muted">{{ __('Unassigned') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($task->due_date)
                                                <span class="{{ $task->is_overdue ? 'text-danger' : '' }}">
                                                    {{ $task->due_date->format('M d, Y') }}
                                                </span>
                                                @if($task->is_overdue)
                                                    <br><small class="text-danger">{{ __('Overdue') }}</small>
                                                @endif
                                            @else
                                                <span class="text-muted">{{ __('No due date') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($task->lead)
                                                <small class="text-primary">{{ __('Lead') }}: {{ $task->lead->name }}</small>
                                            @elseif($task->project)
                                                <small class="text-info">{{ __('Project') }}: {{ $task->project->name }}</small>
                                            @elseif($task->property)
                                                <small class="text-success">{{ __('Property') }}: {{ $task->property->title }}</small>
                                            @else
                                                <span class="text-muted">{{ __('General') }}</span>
                                            @endif
                                        </td>
                                        <td class="Action">
                                            <span>
                                                @can('view tasks')
                                                    <div class="action-btn bg-warning ms-2">
                                                        <a href="{{ route('tasks.show', $task->id) }}" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           data-bs-toggle="tooltip" title="{{ __('View') }}">
                                                            <i class="ti ti-eye text-white"></i>
                                                        </a>
                                                    </div>
                                                @endcan

                                                @can('edit tasks')
                                                    <div class="action-btn bg-info ms-2">
                                                        <a href="#" data-url="{{ route('tasks.edit', $task->id) }}" 
                                                           data-size="lg" data-ajax-popup="true" 
                                                           data-title="{{ __('Edit Task') }}" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           data-bs-toggle="tooltip" title="{{ __('Edit') }}">
                                                            <i class="ti ti-pencil text-white"></i>
                                                        </a>
                                                    </div>
                                                @endcan

                                                @can('delete tasks')
                                                    <div class="action-btn bg-danger ms-2">
                                                        {!! Form::open(['method' => 'DELETE', 'route' => ['tasks.destroy', $task->id], 'id' => 'delete-form-' . $task->id]) !!}
                                                        <a href="#" class="mx-3 btn btn-sm align-items-center bs-pass-para" 
                                                           data-bs-toggle="tooltip" title="{{ __('Delete') }}">
                                                            <i class="ti ti-trash text-white text-white"></i>
                                                        </a>
                                                        {!! Form::close() !!}
                                                    </div>
                                                @endcan
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="card-footer">
                    {{ $tasks->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <script>
        $(document).ready(function() {
            // Auto-submit form on filter change
            $('select[name="status"], select[name="priority"], select[name="category"], select[name="assigned_to"]').change(function() {
                $(this).closest('form').submit();
            });
        });
    </script>
@endpush
