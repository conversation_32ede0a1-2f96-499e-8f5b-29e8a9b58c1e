<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Document Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Document Dashboard')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-button'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create documents')): ?>
            <a href="#" class="btn btn-sm btn-primary" onclick="commonModal('<?php echo e(route('documents.create')); ?>', '<?php echo e(__('Create Document')); ?>')" data-size="xl" data-ajax-popup="true" data-title="<?php echo e(__('Create Document')); ?>">
                <i class="ti ti-plus"></i> <?php echo e(__('Create Document')); ?>

            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-primary">
                                    <i class="ti ti-files"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Total Documents')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['total_documents']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-warning">
                                    <i class="ti ti-file-text"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Draft Documents')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['draft_documents']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-success">
                                    <i class="ti ti-file-check"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Published Documents')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['published_documents']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-info">
                                    <i class="ti ti-signature"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Pending Signatures')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['pending_signatures']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-danger">
                                    <i class="ti ti-clock-exclamation"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Expiring Soon')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['expiring_soon']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-secondary">
                                    <i class="ti ti-eye-check"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('For Review')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['for_review']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-dark">
                                    <i class="ti ti-shield-lock"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Confidential')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['confidential_documents']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto mb-3 mb-sm-0">
                            <div class="d-flex align-items-center">
                                <div class="theme-avtar bg-info">
                                    <i class="ti ti-certificate"></i>
                                </div>
                                <div class="ms-3">
                                    <small class="text-muted"><?php echo e(__('Compliance')); ?></small>
                                    <h6 class="m-0"><?php echo e($stats['compliance_documents']); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Documents by Type Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Documents by Type')); ?></h5>
                </div>
                <div class="card-body">
                    <div id="documents-by-type-chart"></div>
                </div>
            </div>
        </div>

        <!-- Documents by Status Chart -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Documents by Status')); ?></h5>
                </div>
                <div class="card-body">
                    <div id="documents-by-status-chart"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Documents -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Recent Documents')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Document')); ?></th>
                                    <th><?php echo e(__('Type')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Project')); ?></th>
                                    <th><?php echo e(__('Created')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $recentDocuments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <?php if($document->is_confidential): ?>
                                                        <span class="badge bg-danger"><?php echo e(__('Confidential')); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e($document->name); ?></h6>
                                                    <small class="text-muted"><?php echo e($document->document_number); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e(\App\Models\Document::$types[$document->type] ?? $document->type); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo e($document->status == 'published' ? 'success' : ($document->status == 'draft' ? 'warning' : 'secondary')); ?>">
                                                <?php echo e(\App\Models\Document::$statuses[$document->status] ?? $document->status); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($document->project): ?>
                                                <a href="<?php echo e(route('projects.show', $document->project)); ?>" class="text-decoration-none">
                                                    <?php echo e(Str::limit($document->project->name, 20)); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('No Project')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo e($document->created_at->format('M d, Y')); ?></small><br>
                                            <small class="text-muted"><?php echo e(__('by')); ?> <?php echo e($document->creator->name ?? 'Unknown'); ?></small>
                                        </td>
                                        <td>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view documents')): ?>
                                                <a href="<?php echo e(route('documents.show', $document)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="text-center"><?php echo e(__('No recent documents found')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Signatures -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Pending Signatures')); ?></h5>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $pendingSignatures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $signature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo e(Str::limit($signature->document->name, 25)); ?></h6>
                                <small class="text-muted"><?php echo e($signature->signer_name); ?></small>
                            </div>
                            <div>
                                <?php if($signature->deadline): ?>
                                    <small class="badge bg-<?php echo e($signature->is_overdue ? 'danger' : 'warning'); ?>">
                                        <?php echo e($signature->deadline->format('M d')); ?>

                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-muted text-center"><?php echo e(__('No pending signatures')); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('assets/js/plugins/apexcharts.min.js')); ?>"></script>
    <script>
        // Documents by Type Chart
        var typeChartOptions = {
            series: <?php echo json_encode(array_values($documentsByType), 15, 512) ?>,
            chart: {
                type: 'donut',
                height: 300
            },
            labels: <?php echo json_encode(array_map(function($key) { return \App\Models\Document::$types[$key] ?? $key; }, array_keys($documentsByType)), 512) ?>,
            colors: ['#6fd943', '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#f0932b', '#eb4d4b', '#6c5ce7'],
            legend: {
                position: 'bottom'
            }
        };
        var typeChart = new ApexCharts(document.querySelector("#documents-by-type-chart"), typeChartOptions);
        typeChart.render();

        // Documents by Status Chart
        var statusChartOptions = {
            series: <?php echo json_encode(array_values($documentsByStatus), 15, 512) ?>,
            chart: {
                type: 'donut',
                height: 300
            },
            labels: <?php echo json_encode(array_map(function($key) { return \App\Models\Document::$statuses[$key] ?? $key; }, array_keys($documentsByStatus)), 512) ?>,
            colors: ['#28a745', '#ffc107', '#6c757d', '#17a2b8', '#dc3545'],
            legend: {
                position: 'bottom'
            }
        };
        var statusChart = new ApexCharts(document.querySelector("#documents-by-status-chart"), statusChartOptions);
        statusChart.render();
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/documents/dashboard.blade.php ENDPATH**/ ?>