<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class TaskManagementPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        // Task Management Permissions
        $taskPermissions = [
            // Core task permissions
            'manage tasks' => 'Full task management access',
            'create tasks' => 'Create new tasks',
            'edit tasks' => 'Edit existing tasks',
            'delete tasks' => 'Delete tasks',
            'view tasks' => 'View task details',
            'assign tasks' => 'Assign tasks to users',
            'complete tasks' => 'Mark tasks as complete',
            
            // Task dashboard and reporting
            'view task dashboard' => 'View task dashboard',
            'view task analytics' => 'View task analytics and reports',
            'export task data' => 'Export task data',
            
            // Task assignment permissions
            'manage task assignments' => 'Manage task assignments',
            'accept task assignments' => 'Accept task assignments',
            'reject task assignments' => 'Reject task assignments',
            'reassign tasks' => 'Reassign tasks to other users',
            
            // Task comments and collaboration
            'add task comments' => 'Add comments to tasks',
            'edit task comments' => 'Edit task comments',
            'delete task comments' => 'Delete task comments',
            'view task comments' => 'View task comments',
            'pin task comments' => 'Pin important task comments',
            
            // Task attachments
            'upload task attachments' => 'Upload task attachments',
            'download task attachments' => 'Download task attachments',
            'delete task attachments' => 'Delete task attachments',
            'view task attachments' => 'View task attachments',
            
            // Advanced task features
            'bulk manage tasks' => 'Bulk task operations',
            'update task status' => 'Update task status',
            'set task priority' => 'Set task priority levels',
            'manage task categories' => 'Manage task categories',
            'view task history' => 'View task history and changes',
            
            // Task integration permissions
            'link tasks to leads' => 'Link tasks to leads',
            'link tasks to projects' => 'Link tasks to projects',
            'link tasks to properties' => 'Link tasks to properties',
            'create lead tasks' => 'Create lead-related tasks',
            'create project tasks' => 'Create project-related tasks',
            'create property tasks' => 'Create property-related tasks',
            
            // Task notifications and reminders
            'receive task notifications' => 'Receive task notifications',
            'send task notifications' => 'Send task notifications',
            'manage task reminders' => 'Manage task reminders',
            
            // Task calendar and scheduling
            'view task calendar' => 'View task calendar',
            'schedule tasks' => 'Schedule tasks',
            'reschedule tasks' => 'Reschedule tasks',
        ];

        echo "Creating task management permissions...\n";
        
        foreach ($taskPermissions as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission]);
            echo "Created permission: {$permission}\n";
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles($taskPermissions);
        
        echo "Task management permissions setup completed successfully!\n";
    }

    /**
     * Assign permissions to roles based on hierarchy
     */
    private function assignPermissionsToRoles($permissions)
    {
        // Owner permissions (full access)
        $ownerPermissions = array_keys($permissions);
        
        // Manager permissions (most permissions except some admin functions)
        $managerPermissions = [
            'manage tasks',
            'create tasks',
            'edit tasks',
            'delete tasks',
            'view tasks',
            'assign tasks',
            'complete tasks',
            'view task dashboard',
            'view task analytics',
            'export task data',
            'manage task assignments',
            'accept task assignments',
            'reject task assignments',
            'reassign tasks',
            'add task comments',
            'edit task comments',
            'delete task comments',
            'view task comments',
            'pin task comments',
            'upload task attachments',
            'download task attachments',
            'delete task attachments',
            'view task attachments',
            'bulk manage tasks',
            'update task status',
            'set task priority',
            'view task history',
            'link tasks to leads',
            'link tasks to projects',
            'link tasks to properties',
            'create lead tasks',
            'create project tasks',
            'create property tasks',
            'receive task notifications',
            'send task notifications',
            'manage task reminders',
            'view task calendar',
            'schedule tasks',
            'reschedule tasks',
        ];

        // Agent permissions (basic task management)
        $agentPermissions = [
            'create tasks',
            'edit tasks',
            'view tasks',
            'complete tasks',
            'view task dashboard',
            'accept task assignments',
            'reject task assignments',
            'add task comments',
            'view task comments',
            'upload task attachments',
            'download task attachments',
            'view task attachments',
            'update task status',
            'link tasks to leads',
            'link tasks to projects',
            'link tasks to properties',
            'create lead tasks',
            'create project tasks',
            'create property tasks',
            'receive task notifications',
            'view task calendar',
        ];

        // Channel Partner permissions (similar to agents but with some restrictions)
        $channelPartnerPermissions = [
            'create tasks',
            'edit tasks',
            'view tasks',
            'complete tasks',
            'view task dashboard',
            'accept task assignments',
            'reject task assignments',
            'add task comments',
            'view task comments',
            'upload task attachments',
            'download task attachments',
            'view task attachments',
            'update task status',
            'link tasks to leads',
            'link tasks to properties',
            'create lead tasks',
            'create property tasks',
            'receive task notifications',
            'view task calendar',
        ];

        // Assign permissions to roles
        $rolePermissions = [
            'Owner' => $ownerPermissions,
            'Manager' => $managerPermissions,
            'Agent' => $agentPermissions,
            'Channel Partner' => $channelPartnerPermissions,
        ];

        foreach ($rolePermissions as $roleName => $rolePerms) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $validPermissions = Permission::whereIn('name', $rolePerms)->pluck('name')->toArray();
                $role->syncPermissions($validPermissions);
                echo "Assigned " . count($validPermissions) . " task permissions to {$roleName} role\n";
            } else {
                echo "Role not found: {$roleName}\n";
            }
        }
    }
}
