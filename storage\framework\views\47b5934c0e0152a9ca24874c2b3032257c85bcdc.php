<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($form->title); ?></title>
    
    <!-- SEO Meta Tags -->
    <?php if($form->meta_title): ?>
        <meta name="title" content="<?php echo e($form->meta_title); ?>">
    <?php endif; ?>
    <?php if($form->meta_description): ?>
        <meta name="description" content="<?php echo e($form->meta_description); ?>">
    <?php endif; ?>
    <?php if($form->meta_keywords): ?>
        <meta name="keywords" content="<?php echo e($form->meta_keywords); ?>">
    <?php endif; ?>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin: 50px auto;
            max-width: 600px;
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-header h1 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-submit:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .required {
            color: #dc3545;
        }
        
        .help-text {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-check {
            margin-bottom: 10px;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .success-message {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .success-message.show {
            display: block;
        }
        
        .success-message i {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <!-- Form Header -->
            <div class="form-header">
                <h1><?php echo e($form->title); ?></h1>
                <?php if($form->description): ?>
                    <p><?php echo e($form->description); ?></p>
                <?php endif; ?>
            </div>

            <!-- Success Message (Hidden by default) -->
            <div class="success-message" id="success-message">
                <i class="fas fa-check-circle"></i>
                <h3>Thank You!</h3>
                <p id="success-text"><?php echo e($form->success_message ?: 'Your form has been submitted successfully!'); ?></p>
            </div>

            <!-- Form -->
            <form id="public-form" style="display: block;">
                <?php echo csrf_field(); ?>
                
                <?php if($form->fields->count() > 0): ?>
                    <?php $__currentLoopData = $form->fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if(!$field->is_input_field): ?>
                            <!-- Non-input fields (dividers, headings, paragraphs) -->
                            <div class="form-group">
                                <?php echo $field->generateHtml(); ?>

                            </div>
                        <?php else: ?>
                            <!-- Input fields -->
                            <div class="form-group">
                                <label class="form-label" for="field_<?php echo e($field->id); ?>">
                                    <?php echo e($field->label); ?>

                                    <?php if($field->is_required): ?>
                                        <span class="required">*</span>
                                    <?php endif; ?>
                                </label>
                                
                                <?php echo $field->generateHtml(); ?>

                                
                                <?php if($field->help_text): ?>
                                    <div class="help-text"><?php echo e($field->help_text); ?></div>
                                <?php endif; ?>
                                
                                <div class="invalid-feedback" id="error_<?php echo e($field->field_name); ?>"></div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    
                    <!-- Submit Button -->
                    <div class="form-group">
                        <button type="submit" class="btn btn-submit" id="submit-btn">
                            <span class="submit-text">Submit Form</span>
                            <span class="loading">
                                <i class="fas fa-spinner fa-spin"></i> Submitting...
                            </span>
                        </button>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        This form has no fields configured yet.
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Form submission
            $('#public-form').on('submit', function(e) {
                e.preventDefault();
                
                // Clear previous errors
                $('.invalid-feedback').text('');
                $('.form-control').removeClass('is-invalid');
                
                // Show loading state
                $('#submit-btn').prop('disabled', true);
                $('.submit-text').hide();
                $('.loading').addClass('show');
                
                // Collect form data
                var formData = new FormData(this);
                
                // Submit form
                $.ajax({
                    url: '<?php echo e(route("public.forms.submit", $form->slug)); ?>',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            // Show success message
                            $('#public-form').hide();
                            $('#success-message').addClass('show');
                            
                            // Update success message if provided
                            if (response.message) {
                                $('#success-text').text(response.message);
                            }
                            
                            // Redirect if specified
                            if (response.redirect_url) {
                                setTimeout(function() {
                                    window.location.href = response.redirect_url;
                                }, 3000);
                            }
                        } else {
                            showError('Submission failed: ' + response.message);
                            resetSubmitButton();
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            // Validation errors
                            var errors = xhr.responseJSON.errors;
                            $.each(errors, function(field, messages) {
                                $('#error_' + field).text(messages[0]);
                                $('[name="' + field + '"]').addClass('is-invalid');
                            });
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            showError(xhr.responseJSON.message);
                        } else {
                            showError('An error occurred while submitting the form. Please try again.');
                        }
                        resetSubmitButton();
                    }
                });
            });
            
            function showError(message) {
                // Remove existing alerts
                $('.alert-danger').remove();
                
                // Add error alert
                var errorAlert = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-exclamation-circle"></i> ' + message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>';
                
                $('#public-form').prepend(errorAlert);
            }
            
            function resetSubmitButton() {
                $('#submit-btn').prop('disabled', false);
                $('.submit-text').show();
                $('.loading').removeClass('show');
            }
            
            // Track form start time for analytics
            var formStartTime = Date.now();
            
            // Track field interactions
            $('.form-control').on('focus', function() {
                // Track field interaction for analytics
                console.log('Field focused:', $(this).attr('name'));
            });
            
            // Auto-resize textareas
            $('textarea').on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        });
    </script>
</body>
</html>
<?php /**PATH D:\xampp\htdocs\calling_agent_crm\resources\views/public/forms/show.blade.php ENDPATH**/ ?>