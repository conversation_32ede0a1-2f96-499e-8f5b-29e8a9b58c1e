<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            DefaultDataUsersTableSeeder::class,
            NewModulePermissionsSeeder::class,
            DummyPropertiesSeeder::class,
            CallingSystemPermissionsSeeder::class,
            CallingSystemRolePermissionsSeeder::class,
            LeadPermissionsSeeder::class,
            TaskManagementPermissionsSeeder::class,
            FormBuilderPermissionsSeeder::class,
            FormTemplatesSeeder::class,
            RealisticCallDataSeeder::class,
            ComprehensiveProjectDataSeeder::class,
            ComprehensiveDocumentDataSeeder::class,
            ComprehensiveFormDataSeeder::class,
            TaskManagementDataSeeder::class,
        ]);
    }
}
