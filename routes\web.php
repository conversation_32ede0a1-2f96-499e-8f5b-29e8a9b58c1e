<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\AuthPageController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\NoticeBoardController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\FAQController;
use App\Http\Controllers\HomePageController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OTPController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\EstimationController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ItemCategoryController;
use App\Http\Controllers\ItemTaxController;
use App\Http\Controllers\SalesCategoryController;
use App\Http\Controllers\ItemUnitController;
use App\Http\Controllers\LeadController;
use App\Http\Controllers\LeadDuplicateController;
use App\Http\Controllers\LeadLifecycleController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\PropertyController;
// Controllers are now created
use App\Http\Controllers\PropertyUnitController;
use App\Http\Controllers\PropertyPhotoController;
use App\Http\Controllers\PropertyDocumentController;
use App\Http\Controllers\PropertyPricingController;
use App\Http\Controllers\PropertyAvailabilityController;
use App\Http\Controllers\VirtualTourController;
use App\Http\Controllers\PropertyAnalyticsController;
// TODO: Create these controllers
// use App\Http\Controllers\PropertyLeadMatchingController;
// use App\Http\Controllers\PropertyApiController;





use App\Models\User;
use GuzzleHttp\Middleware;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

require __DIR__ . '/auth.php';

Route::get('/', [HomeController::class,'index'])->middleware(
    [

        'XSS',
    ]
);
Route::get('home', [HomeController::class,'index'])->name('home')->middleware(
    [

        'XSS',
    ]
);
Route::get('dashboard', [HomeController::class,'index'])->name('dashboard')->middleware(
    [

        'XSS',
    ]
);

//-------------------------------User-------------------------------------------

Route::resource('users', UserController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);


Route::get('login/otp', [OTPController::class, 'show'])->name('otp.show')->middleware(
    [

        'XSS',
    ]
);
Route::post('login/otp', [OTPController::class, 'check'])->name('otp.check')->middleware(
    [

        'XSS',
    ]
);
Route::get('login/2fa/disable', [OTPController::class, 'disable'])->name('2fa.disable')->middleware(['XSS',]);

//-------------------------------Subscription-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function (){

    Route::resource('subscriptions', SubscriptionController::class);
    Route::get('coupons/history', [CouponController::class,'history'])->name('coupons.history');
    Route::delete('coupons/history/{id}/destroy', [CouponController::class,'historyDestroy'])->name('coupons.history.destroy');
    Route::get('coupons/apply', [CouponController::class, 'apply'])->name('coupons.apply');
    Route::resource('coupons', CouponController::class);
    Route::get('subscription/transaction', [SubscriptionController::class,'transaction'])->name('subscription.transaction');
}
);

//-------------------------------Subscription Payment-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function (){

    Route::post('subscription/{id}/stripe/payment', [SubscriptionController::class,'stripePayment'])->name('subscription.stripe.payment');
}
);
//-------------------------------Settings-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function (){
    Route::get('settings', [SettingController::class,'index'])->name('setting.index');

    Route::post('settings/account', [SettingController::class,'accountData'])->name('setting.account');
    Route::delete('settings/account/delete', [SettingController::class,'accountDelete'])->name('setting.account.delete');
    Route::post('settings/password', [SettingController::class,'passwordData'])->name('setting.password');
    Route::post('settings/general', [SettingController::class,'generalData'])->name('setting.general');
    Route::post('settings/smtp', [SettingController::class,'smtpData'])->name('setting.smtp');
    Route::get('settings/smtp-test', [SettingController::class, 'smtpTest'])->name('setting.smtp.test');
    Route::post('settings/smtp-test', [SettingController::class, 'smtpTestMailSend'])->name('setting.smtp.testing');
    Route::post('settings/payment', [SettingController::class,'paymentData'])->name('setting.payment');
    Route::post('settings/site-seo', [SettingController::class,'siteSEOData'])->name('setting.site.seo');
    Route::post('settings/google-recaptcha', [SettingController::class,'googleRecaptchaData'])->name('setting.google.recaptcha');
    Route::post('settings/company', [SettingController::class,'companyData'])->name('setting.company');
    Route::post('settings/2fa', [SettingController::class, 'twofaEnable'])->name('setting.twofa.enable');

    Route::get('footer-setting', [SettingController::class, 'footerSetting'])->name('footerSetting');
    Route::post('settings/footer', [SettingController::class,'footerData'])->name('setting.footer');

    // Language Management Routes
    Route::get('languages', [\App\Http\Controllers\LanguageController::class, 'getLanguages'])->name('languages.index');
    Route::post('language/switch', [\App\Http\Controllers\LanguageController::class, 'switchLanguage'])->name('language.switch');
    Route::get('language/{lang}', [\App\Http\Controllers\LanguageController::class, 'switchLanguage'])->name('language.change');
    Route::post('language/reset', [\App\Http\Controllers\LanguageController::class, 'resetLanguage'])->name('language.reset');
    Route::get('language/stats', [\App\Http\Controllers\LanguageController::class, 'getLanguageStats'])->name('language.stats');
    Route::post('theme/settings', [SettingController::class,'themeSettings'])->name('theme.settings');
}
);


//-------------------------------Role & Permissions-------------------------------------------
Route::resource('permission', PermissionController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

Route::resource('role', RoleController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Note-------------------------------------------
Route::resource('note', NoticeBoardController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Contact-------------------------------------------
Route::resource('contact', ContactController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------logged History-------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function () {

    Route::get('logged/history', [UserController::class,'loggedHistory'])->name('logged.history');
    Route::get('logged/{id}/history/show', [UserController::class,'loggedHistoryShow'])->name('logged.history.show');
    Route::delete('logged/{id}/history', [UserController::class,'loggedHistoryDestroy'])->name('logged.history.destroy');
});


//-------------------------------Plan Payment-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function (){
    Route::post('subscription/{id}/bank-transfer', [PaymentController::class, 'subscriptionBankTransfer'])->name('subscription.bank.transfer');
    Route::get('subscription/{id}/bank-transfer/action/{status}', [PaymentController::class, 'subscriptionBankTransferAction'])->name('subscription.bank.transfer.action');
    Route::post('subscription/{id}/paypal', [PaymentController::class, 'subscriptionPaypal'])->name('subscription.paypal');
    Route::get('subscription/{id}/paypal/{status}', [PaymentController::class, 'subscriptionPaypalStatus'])->name('subscription.paypal.status');
    Route::post('subscription/{id}/{user_id}/manual-assign-package', [PaymentController::class, 'subscriptionManualAssignPackage'])->name('subscription.manual_assign_package');
    Route::get('subscription/flutterwave/{sid}/{tx_ref}', [PaymentController::class, 'subscriptionFlutterwave'])->name('subscription.flutterwave');
}
);


//-------------------------------Client-------------------------------------------
Route::resource('client', ClientController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Category-------------------------------------------
Route::resource('item-category', ItemCategoryController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Tax-------------------------------------------
Route::resource('item-tax', ItemTaxController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Sales Category-------------------------------------------
Route::resource('sales-category', SalesCategoryController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Item Units-------------------------------------------
Route::resource('item-unit', ItemUnitController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Items-------------------------------------------
Route::resource('item', ItemController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-----------------------------------Estimation------------------------------------------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function () {
    Route::post('estimation/item/details', [EstimationController::class, 'getItemDetails'])->name('get.estimation.item.details');
    Route::get('estimation/{id}/status', [EstimationController::class,'estimationStatus'])->name('estimation.status');
    Route::get('estimation/{id}/create/item', [EstimationController::class,'estimationItemCreate'])->name('estimation.create.item');
    Route::post('estimation/{id}/item/store', [EstimationController::class,'estimationItemStore'])->name('estimation.item.store');
    Route::post('estimation/{id}/item/{tid}/store', [EstimationController::class,'estimationItemDestroy'])->name('estimation.item.destroy');
    Route::get('invoice/{id}/estimationSendMail',[EstimationController::class,'estimationSendMail'])->name('estimationSendMail');
    Route::resource('estimation', EstimationController::class);
}
);

//-----------------------------------Invoice------------------------------------------------------------------------------

Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function () {
    Route::post('invoice/item/details', [InvoiceController::class, 'getItemDetails'])->name('get.invoice.item.details');
    Route::get('invoice/{id}/status', [InvoiceController::class,'invoiceStatus'])->name('invoice.status');
    Route::get('invoice/{id}/item/create', [InvoiceController::class,'invoiceItemCreate'])->name('invoice.create.item');
    Route::post('invoice/{id}/item/store', [InvoiceController::class,'invoiceItemStore'])->name('invoice.item.store');
    Route::post('invoice/{id}/item/{tid}/store', [InvoiceController::class,'invoiceItemDestroy'])->name('invoice.item.destroy');

    Route::get('invoice/{id}/payment/create', [InvoiceController::class,'invoicePaymentCreate'])->name('invoice.payment.create');
    Route::post('invoice/{id}/payment/store', [InvoiceController::class,'invoicePaymentStore'])->name('invoice.payment.store');
    Route::post('invoice/{id}/payment/{pid}/destroy', [InvoiceController::class,'invoicePaymentDestroy'])->name('invoice.payment.destroy');
    Route::get('invoice/{id}/sendMail',[InvoiceController::class,'sendMail'])->name('sendMail');
    Route::resource('invoice', InvoiceController::class);
}
);

//-------------------------------Expense-------------------------------------------
Route::group(
    [
        'middleware' => [
            'auth',
            'XSS',
        ],
    ], function () {

    Route::resource('expense', ExpenseController::class);
});




//-------------------------------Notification-------------------------------------------
Route::resource('notification', NotificationController::class)->middleware(
    [
        'auth',
        'XSS',

    ]
 );

 Route::get('email-verification/{token}', [VerifyEmailController::class, 'verifyEmail'])->name('email-verification')->middleware(
    [
        'XSS',
    ]
);

//-------------------------------FAQ-------------------------------------------
Route::resource('FAQ', FAQController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Home Page-------------------------------------------
Route::resource('homepage', HomePageController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);
//-------------------------------FAQ-------------------------------------------
Route::resource('pages', PageController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);

//-------------------------------Auth page-------------------------------------------
Route::resource('authPage', AuthPageController::class)->middleware(
    [
        'auth',
        'XSS',
    ]
);


Route::get('page/{slug}', [PageController::class, 'page'])->name('page');
//-------------------------------FAQ-------------------------------------------
Route::impersonate();


//-------------------------------LEAD MANAGEMENT SYSTEM-------------------------------------------

// Core Lead Management Routes
Route::prefix('leads')->name('leads.')->middleware(['auth', 'XSS'])->group(function () {

   
     // Advanced Import Wizard
    Route::get('/import-wizard', [LeadController::class, 'importWizard'])->name('import-wizard');
    
    Route::post('/import/analyze', [LeadController::class, 'analyzeImportFile'])->name('import.analyze');
    Route::post('/import/execute', [LeadController::class, 'executeImport'])->name('import.execute');

    // Template Management Routes
    Route::get('/import/templates', [LeadController::class, 'getImportTemplates'])->name('import.templates');
    Route::post('/import/templates', [LeadController::class, 'saveImportTemplate'])->name('import.templates.save');
    Route::delete('/import/templates/{id}', [LeadController::class, 'deleteImportTemplate'])->name('import.templates.delete');

     // Import/Export Operations
    Route::post('/import', [LeadController::class, 'import'])->name('import');
    Route::post('/export', [LeadController::class, 'export'])->name('export');

    
    // Basic CRUD Operations
    Route::get('/', [LeadController::class, 'index'])->name('index');
    Route::get('/create', [LeadController::class, 'create'])->name('create');
    Route::post('/', [LeadController::class, 'store'])->name('store');
    Route::get('/{lead}', [LeadController::class, 'show'])->name('show');
    Route::get('/{lead}/edit', [LeadController::class, 'edit'])->name('edit');
    Route::put('/{lead}', [LeadController::class, 'update'])->name('update');
    Route::delete('/{lead}', [LeadController::class, 'destroy'])->name('destroy');

    

    // Lead Actions
    Route::post('/{lead}/assign', [LeadController::class, 'assign'])->name('assign');
    Route::post('/{lead}/convert', [LeadController::class, 'convert'])->name('convert');

   
    


    

    // DEEP DEBUG: Comprehensive debug routes
    Route::get('/debug/system-check', function() {
        return response()->json([
            'success' => true,
            'timestamp' => now(),
            'system_info' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'environment' => app()->environment(),
                'debug_mode' => config('app.debug'),
                'timezone' => config('app.timezone'),
                'locale' => config('app.locale')
            ],
            'user_info' => [
                'authenticated' => Auth::check(),
                'user_id' => Auth::id(),
                'user_type' => Auth::user()->type ?? 'N/A',
                'user_name' => Auth::user()->name ?? 'N/A'
            ],
            'routes' => [
                'import_wizard' => route('leads.import-wizard'),
                'import_analyze' => route('leads.import.analyze'),
                'import_execute' => route('leads.import.execute')
            ],
            'permissions' => [
                'import_leads' => Gate::check('import leads'),
                'import_wizard' => Gate::check('import-wizard')
            ],
            'session_info' => [
                'session_id' => session()->getId(),
                'csrf_token' => csrf_token()
            ]
        ]);
    })->name('debug.system.check');

    Route::post('/debug/file-upload', function(Request $request) {
        Log::info('🧪 DEBUG ROUTE: File upload test', [
            'timestamp' => now(),
            'user_id' => Auth::id(),
            'has_file' => $request->hasFile('file'),
            'request_method' => $request->method(),
            'request_size' => strlen(serialize($request->all())),
            'headers' => $request->headers->all()
        ]);

        $response = [
            'success' => true,
            'message' => 'Debug file upload route working',
            'timestamp' => now(),
            'request_info' => [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'has_file' => $request->hasFile('file')
            ]
        ];

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $response['file_info'] = [
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'size_formatted' => round($file->getSize() / 1024 / 1024, 2) . ' MB',
                'type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension(),
                'is_valid' => $file->isValid(),
                'error' => $file->getError(),
                'path' => $file->getPathname()
            ];
        }

        $response['request_data'] = $request->except(['_token']);

        return response()->json($response);
    })->name('debug.file.upload');

    // Bulk Operations
    Route::post('/bulk-delete', [LeadController::class, 'bulkDelete'])->name('bulk-delete');
    Route::post('/bulk-assign', [LeadController::class, 'bulkAssign'])->name('bulk-assign');
    Route::post('/bulk-status', [LeadController::class, 'bulkUpdateStatus'])->name('bulk-status');
    Route::post('/bulk-update-status', [LeadController::class, 'bulkUpdateStatus'])->name('bulk-update-status');

});

// Lead Duplicate Detection
Route::prefix('leads')->name('leads.')->middleware(['auth', 'XSS'])->group(function () {

    // Duplicate Management
    Route::prefix('duplicates')->name('duplicates.')->group(function () {
        Route::get('/stats', [LeadDuplicateController::class, 'getStats'])->name('stats');
        Route::get('/search', [LeadDuplicateController::class, 'searchDuplicates'])->name('search');
        Route::post('/merge', [LeadDuplicateController::class, 'merge'])->name('merge');
        Route::post('/preview-merge', [LeadDuplicateController::class, 'previewMerge'])->name('preview-merge');
        Route::post('/mark-not-duplicate', [LeadDuplicateController::class, 'markAsNotDuplicate'])->name('mark-not-duplicate');
        Route::get('/{lead}/find', [LeadDuplicateController::class, 'findDuplicates'])->name('find');
    });

    // Lifecycle Management
    Route::prefix('lifecycle')->name('lifecycle.')->group(function () {
        Route::get('/analytics', [LeadLifecycleController::class, 'getAnalytics'])->name('analytics');
        Route::post('/process-aging', [LeadLifecycleController::class, 'processAging'])->name('process-aging');
        Route::post('/cleanup-stale', [LeadLifecycleController::class, 'cleanupStaleLeads'])->name('cleanup-stale');
        Route::get('/stale-report', [LeadLifecycleController::class, 'getStaleLeadsReport'])->name('stale-report');
        Route::get('/progression-report', [LeadLifecycleController::class, 'getStageProgressionReport'])->name('progression-report');
        Route::post('/batch-process', [LeadLifecycleController::class, 'batchProcessRules'])->name('batch-process');
        Route::post('/{lead}/process-rules', [LeadLifecycleController::class, 'processRules'])->name('process-rules');
    });
});

//-------------------------------TASK MANAGEMENT SYSTEM-------------------------------------------

// Core Task Management Routes
Route::prefix('tasks')->name('tasks.')->middleware(['auth', 'XSS'])->group(function () {

    // Dashboard
    Route::get('/dashboard', [App\Http\Controllers\TaskController::class, 'dashboard'])->name('dashboard');

    // Basic CRUD Operations
    Route::get('/', [App\Http\Controllers\TaskController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\TaskController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\TaskController::class, 'store'])->name('store');
    Route::get('/{task}', [App\Http\Controllers\TaskController::class, 'show'])->name('show');
    Route::get('/{task}/edit', [App\Http\Controllers\TaskController::class, 'edit'])->name('edit');
    Route::put('/{task}', [App\Http\Controllers\TaskController::class, 'update'])->name('update');
    Route::delete('/{task}', [App\Http\Controllers\TaskController::class, 'destroy'])->name('destroy');

    // Task Status Management
    Route::post('/{task}/status', [App\Http\Controllers\TaskController::class, 'updateStatus'])->name('update-status');
    Route::post('/{task}/assign', [App\Http\Controllers\TaskController::class, 'assign'])->name('assign');
    Route::post('/{task}/comments', [App\Http\Controllers\TaskController::class, 'addComment'])->name('add-comment');

    // Task Attachments
    Route::post('/{task}/attachments', [App\Http\Controllers\TaskAttachmentController::class, 'store'])->name('attachments.store');
    Route::get('/{task}/attachments/{attachment}/download', [App\Http\Controllers\TaskAttachmentController::class, 'download'])->name('attachments.download');
    Route::delete('/{task}/attachments/{attachment}', [App\Http\Controllers\TaskAttachmentController::class, 'destroy'])->name('attachments.destroy');

    // Task Calendar
    Route::get('/calendar', [App\Http\Controllers\TaskController::class, 'calendar'])->name('calendar');
    Route::get('/calendar/data', [App\Http\Controllers\TaskController::class, 'calendarData'])->name('calendar.data');
    Route::get('/calendar/data', [App\Http\Controllers\TaskController::class, 'calendarData'])->name('calendar.data');

    // Task Analytics
    Route::get('/analytics', [App\Http\Controllers\TaskController::class, 'analytics'])->name('analytics');
    Route::get('/reports', [App\Http\Controllers\TaskController::class, 'reports'])->name('reports');
    Route::get('/export', [App\Http\Controllers\TaskController::class, 'export'])->name('export');

    // Bulk Operations
    Route::post('/bulk/assign', [App\Http\Controllers\TaskController::class, 'bulkAssign'])->name('bulk.assign');
    Route::post('/bulk/status', [App\Http\Controllers\TaskController::class, 'bulkUpdateStatus'])->name('bulk.status');
    Route::post('/bulk/delete', [App\Http\Controllers\TaskController::class, 'bulkDelete'])->name('bulk.delete');
});

//-------------------------------PROJECT MANAGEMENT SYSTEM-------------------------------------------

// Core Project Management Routes
Route::prefix('projects')->name('projects.')->middleware(['auth', 'XSS'])->group(function () {

    // Dashboard
    Route::get('/dashboard', [ProjectController::class, 'dashboard'])->name('dashboard');

    // Basic CRUD Operations
    Route::get('/', [ProjectController::class, 'index'])->name('index');
    Route::get('/create', [ProjectController::class, 'create'])->name('create');
    Route::post('/', [ProjectController::class, 'store'])->name('store');
    Route::get('/{project}', [ProjectController::class, 'show'])->name('show');
    Route::get('/{project}/edit', [ProjectController::class, 'edit'])->name('edit');
    Route::put('/{project}', [ProjectController::class, 'update'])->name('update');
    Route::delete('/{project}', [ProjectController::class, 'destroy'])->name('destroy');

    // Import/Export Operations
    Route::post('/import', [ProjectController::class, 'import'])->name('import');
    Route::get('/export', [ProjectController::class, 'export'])->name('export');

});

//-------------------------------DOCUMENT MANAGEMENT SYSTEM-------------------------------------------

// Core Document Management Routes
Route::prefix('documents')->name('documents.')->middleware(['auth', 'XSS'])->group(function () {

    // Dashboard
    Route::get('/dashboard', [DocumentController::class, 'dashboard'])->name('dashboard');

    // Basic CRUD Operations
    Route::get('/', [DocumentController::class, 'index'])->name('index');
    Route::get('/create', [DocumentController::class, 'create'])->name('create');
    Route::post('/', [DocumentController::class, 'store'])->name('store');
    Route::get('/{document}', [DocumentController::class, 'show'])->name('show');
    Route::get('/{document}/edit', [DocumentController::class, 'edit'])->name('edit');
    Route::put('/{document}', [DocumentController::class, 'update'])->name('update');
    Route::delete('/{document}', [DocumentController::class, 'destroy'])->name('destroy');

    // File Operations
    Route::get('/{document}/download', [DocumentController::class, 'download'])->name('download');
    Route::post('/{document}/upload-version', [DocumentController::class, 'uploadVersion'])->name('upload-version');

    // Signature Operations
    Route::post('/{document}/request-signature', [DocumentController::class, 'requestSignature'])->name('request-signature');
    Route::get('/sign/{token}', [DocumentController::class, 'showSignature'])->name('sign');
    Route::post('/sign/{token}', [DocumentController::class, 'processSignature'])->name('process-signature');
    Route::post('/signature/{signature}/decline', [DocumentController::class, 'declineSignature'])->name('decline-signature');

    // Access Management
    Route::post('/{document}/grant-access', [DocumentController::class, 'grantAccess'])->name('grant-access');
    Route::post('/{document}/revoke-access', [DocumentController::class, 'revokeAccess'])->name('revoke-access');

    // Template Operations
    Route::get('/templates', [DocumentController::class, 'templates'])->name('templates');
    Route::post('/create-from-template', [DocumentController::class, 'createFromTemplate'])->name('create-from-template');

    // Import/Export Operations
    Route::post('/import', [DocumentController::class, 'import'])->name('import');
    Route::get('/export', [DocumentController::class, 'export'])->name('export');

    // Audit and Compliance
    Route::get('/{document}/audit-log', [DocumentController::class, 'auditLog'])->name('audit-log');
    Route::get('/{document}/versions', [DocumentController::class, 'versions'])->name('versions');

});

//-------------------------------PROPERTY INVENTORY MANAGEMENT SYSTEM-------------------------------------------

// Core Property Management Routes
Route::prefix('properties')->name('properties.')->middleware(['auth', 'XSS'])->group(function () {

    
    // Basic CRUD Operations
    Route::get('/', [PropertyController::class, 'index'])->name('index');
    Route::get('/create', [PropertyController::class, 'create'])->name('create');
    Route::get('/add', [PropertyController::class, 'add'])->name('add');
    Route::post('/', [PropertyController::class, 'store'])->name('store');

    // Dashboard (must be before {property} routes)
    Route::get('/dashboard', [PropertyController::class, 'dashboard'])->name('dashboard');

    // Bulk Operations
    Route::post('/bulk-status', [PropertyController::class, 'bulkStatus'])->name('bulk-status');
    Route::post('/bulk-assign', [PropertyController::class, 'bulkAssign'])->name('bulk-assign');

    // Import/Export
    Route::post('/import', [PropertyController::class, 'import'])->name('import');
    Route::post('/export', [PropertyController::class, 'export'])->name('export');

    // Analytics and Reporting (must be before {property} routes)
    Route::prefix('/analytics')->name('analytics.')->group(function () {
        Route::get('/', [PropertyAnalyticsController::class, 'index'])->name('index');
        Route::get('/dashboard', [PropertyAnalyticsController::class, 'dashboard'])->name('dashboard');
        Route::get('/performance', [PropertyAnalyticsController::class, 'performance'])->name('performance');
        Route::get('/occupancy', [PropertyAnalyticsController::class, 'occupancy'])->name('occupancy');
        Route::get('/pricing', [PropertyAnalyticsController::class, 'pricing'])->name('pricing');
        Route::get('/leads', [PropertyAnalyticsController::class, 'leads'])->name('leads');
        Route::get('/export', [PropertyAnalyticsController::class, 'export'])->name('export');
    });

    // TODO: Lead Matching (must be before {property} routes) - PropertyLeadMatchingController not implemented yet
    /*
    Route::prefix('/lead-matching')->name('lead-matching.')->group(function () {
        Route::get('/', [PropertyLeadMatchingController::class, 'index'])->name('index');
        Route::post('/match', [PropertyLeadMatchingController::class, 'match'])->name('match');
        Route::get('/{property}/matches', [PropertyLeadMatchingController::class, 'getMatches'])->name('get-matches');
        Route::post('/{property}/recommend', [PropertyLeadMatchingController::class, 'recommend'])->name('recommend');
    });
    */

    // Property-specific routes (must be after static routes)
    Route::get('/{property}', [PropertyController::class, 'show'])->name('show');
    Route::get('/{property}/edit', [PropertyController::class, 'edit'])->name('edit');
    Route::put('/{property}', [PropertyController::class, 'update'])->name('update');
    Route::delete('/{property}', [PropertyController::class, 'destroy'])->name('destroy');

    // Individual section update routes
    Route::get('/{property}/edit-unit-details', [PropertyController::class, 'editUnitDetails'])->name('edit-unit-details');
    Route::put('/{property}/update-unit-details', [PropertyController::class, 'updateUnitDetails'])->name('update-unit-details');

    Route::get('/{property}/edit-pricing-details', [PropertyController::class, 'editPricingDetails'])->name('edit-pricing-details');
    Route::put('/{property}/update-pricing-details', [PropertyController::class, 'updatePricingDetails'])->name('update-pricing-details');

    Route::get('/{property}/edit-images-media', [PropertyController::class, 'editImagesMedia'])->name('edit-images-media');
    Route::put('/{property}/update-images-media', [PropertyController::class, 'updateImagesMedia'])->name('update-images-media');

    Route::get('/{property}/edit-additional-details', [PropertyController::class, 'editAdditionalDetails'])->name('edit-additional-details');
    Route::put('/{property}/update-additional-details', [PropertyController::class, 'updateAdditionalDetails'])->name('update-additional-details');

    // Property Units Management
    Route::prefix('/{property}/units')->name('units.')->group(function () {
        Route::get('/', [PropertyUnitController::class, 'index'])->name('index');
        Route::get('/create', [PropertyUnitController::class, 'create'])->name('create');
        Route::post('/', [PropertyUnitController::class, 'store'])->name('store');
        Route::get('/{unit}', [PropertyUnitController::class, 'show'])->name('show');
        Route::get('/{unit}/edit', [PropertyUnitController::class, 'edit'])->name('edit');
        Route::put('/{unit}', [PropertyUnitController::class, 'update'])->name('update');
        Route::delete('/{unit}', [PropertyUnitController::class, 'destroy'])->name('destroy');
    });

    // Remaining property management routes
    // Photo Management
    Route::prefix('/{property}/photos')->name('photos.')->group(function () {
        Route::get('/', [PropertyPhotoController::class, 'index'])->name('index');
        Route::post('/upload', [PropertyPhotoController::class, 'upload'])->name('upload');
        Route::post('/bulk-upload', [PropertyPhotoController::class, 'bulkUpload'])->name('bulk-upload');
        Route::put('/{photo}', [PropertyPhotoController::class, 'update'])->name('update');
        Route::delete('/{photo}', [PropertyPhotoController::class, 'destroy'])->name('destroy');
        Route::post('/reorder', [PropertyPhotoController::class, 'reorder'])->name('reorder');
        Route::post('/{photo}/set-primary', [PropertyPhotoController::class, 'setPrimary'])->name('set-primary');
    });

    // Document Management
    Route::prefix('/{property}/documents')->name('documents.')->group(function () {
        Route::get('/', [PropertyDocumentController::class, 'index'])->name('index');
        Route::post('/upload', [PropertyDocumentController::class, 'upload'])->name('upload');
        Route::get('/{document}', [PropertyDocumentController::class, 'show'])->name('show');
        Route::get('/{document}/download', [PropertyDocumentController::class, 'download'])->name('download');
        Route::put('/{document}', [PropertyDocumentController::class, 'update'])->name('update');
        Route::delete('/{document}', [PropertyDocumentController::class, 'destroy'])->name('destroy');
    });

    // Pricing Management
    Route::prefix('/{property}/pricing')->name('pricing.')->group(function () {
        Route::get('/', [PropertyPricingController::class, 'index'])->name('index');
        Route::post('/', [PropertyPricingController::class, 'store'])->name('store');
        Route::put('/{pricing}', [PropertyPricingController::class, 'update'])->name('update');
        Route::delete('/{pricing}', [PropertyPricingController::class, 'destroy'])->name('destroy');
        Route::post('/{pricing}/set-current', [PropertyPricingController::class, 'setCurrent'])->name('set-current');
    });

    // Availability Management
    Route::prefix('/{property}/availability')->name('availability.')->group(function () {
        Route::get('/', [PropertyAvailabilityController::class, 'index'])->name('index');
        Route::get('/calendar', [PropertyAvailabilityController::class, 'calendar'])->name('calendar');
        Route::post('/', [PropertyAvailabilityController::class, 'store'])->name('store');
        Route::put('/{availability}', [PropertyAvailabilityController::class, 'update'])->name('update');
        Route::delete('/{availability}', [PropertyAvailabilityController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-update', [PropertyAvailabilityController::class, 'bulkUpdate'])->name('bulk-update');
    });

    // Virtual Tours Management
    Route::prefix('/{property}/virtual-tours')->name('virtual-tours.')->group(function () {
        Route::get('/', [VirtualTourController::class, 'index'])->name('index');
        Route::get('/create', [VirtualTourController::class, 'create'])->name('create');
        Route::post('/', [VirtualTourController::class, 'store'])->name('store');
        Route::get('/{tour}', [VirtualTourController::class, 'show'])->name('show');
        Route::get('/{tour}/edit', [VirtualTourController::class, 'edit'])->name('edit');
        Route::put('/{tour}', [VirtualTourController::class, 'update'])->name('update');
        Route::delete('/{tour}', [VirtualTourController::class, 'destroy'])->name('destroy');
        Route::get('/{tour}/embed', [VirtualTourController::class, 'embed'])->name('embed');
    });



    // TODO: Implement import/export and bulk operations (already implemented in PropertyController)
    /*
    // Import/Export Operations
    Route::post('/import', [PropertyController::class, 'import'])->name('import');
    Route::get('/export', [PropertyController::class, 'export'])->name('export');

    // Bulk Operations
    Route::post('/bulk-delete', [PropertyController::class, 'bulkDelete'])->name('bulk-delete');
    Route::post('/bulk-update-status', [PropertyController::class, 'bulkUpdateStatus'])->name('bulk-update-status');
    Route::post('/bulk-assign-agent', [PropertyController::class, 'bulkAssignAgent'])->name('bulk-assign-agent');
    */

});

// TODO: Implement PropertyApiController
/*
// Property API Routes for Mobile/External Integration
Route::prefix('api/properties')->name('api.properties.')->middleware(['auth:sanctum', 'XSS'])->group(function () {
    Route::get('/', [PropertyApiController::class, 'index'])->name('index');
    Route::get('/search', [PropertyApiController::class, 'search'])->name('search');
    Route::get('/filters', [PropertyApiController::class, 'getFilters'])->name('filters');
    Route::get('/{property}', [PropertyApiController::class, 'show'])->name('show');
    Route::get('/{property}/availability', [PropertyApiController::class, 'availability'])->name('availability');
    Route::post('/{property}/inquiry', [PropertyApiController::class, 'inquiry'])->name('inquiry');
    Route::post('/{property}/shortlist', [PropertyApiController::class, 'shortlist'])->name('shortlist');
});
*/

//-------------------------------FORM BUILDER SYSTEM-------------------------------------------

// Form Builder Controllers
use App\Http\Controllers\FormBuilderController;
use App\Http\Controllers\PublicFormController;
use App\Http\Controllers\FormSubmissionController;

// Form Builder Routes
Route::prefix('forms')->name('forms.')->middleware(['auth', 'XSS'])->group(function () {
    Route::get('/', [FormBuilderController::class, 'index'])->name('index');
    Route::get('/create', [FormBuilderController::class, 'create'])->name('create');
    Route::post('/', [FormBuilderController::class, 'store'])->name('store');
    Route::get('/{form}', [FormBuilderController::class, 'show'])->name('show');
    Route::get('/{form}/edit', [FormBuilderController::class, 'edit'])->name('edit');
    Route::put('/{form}', [FormBuilderController::class, 'update'])->name('update');
    Route::delete('/{form}', [FormBuilderController::class, 'destroy'])->name('destroy');

    // Form Actions
    Route::post('/{form}/duplicate', [FormBuilderController::class, 'duplicate'])->name('duplicate');
    Route::post('/{form}/publish', [FormBuilderController::class, 'publish'])->name('publish');
    Route::post('/{form}/archive', [FormBuilderController::class, 'archive'])->name('archive');
    Route::get('/{form}/export', [FormBuilderController::class, 'export'])->name('export');

    // Templates
    Route::get('/templates/list', [FormBuilderController::class, 'templates'])->name('templates');
});

// Form Submissions Routes
Route::prefix('form-submissions')->name('form-submissions.')->middleware(['auth', 'XSS'])->group(function () {
    Route::get('/', [FormSubmissionController::class, 'index'])->name('index');
    Route::get('/{submission}', [FormSubmissionController::class, 'show'])->name('show');
    Route::delete('/{submission}', [FormSubmissionController::class, 'destroy'])->name('destroy');

    // Submission Actions
    Route::post('/{submission}/convert-to-lead', [FormSubmissionController::class, 'convertToLead'])->name('convert-to-lead');
    Route::post('/{submission}/mark-as-spam', [FormSubmissionController::class, 'markAsSpam'])->name('mark-as-spam');
    Route::put('/{submission}/status', [FormSubmissionController::class, 'updateStatus'])->name('update-status');

    // Bulk Actions
    Route::post('/bulk-action', [FormSubmissionController::class, 'bulkAction'])->name('bulk-action');
    Route::get('/export', [FormSubmissionController::class, 'export'])->name('export');
});

// Public Form Routes (no auth required)
Route::prefix('public/forms')->name('public.forms.')->group(function () {
    Route::get('/{slug}', [PublicFormController::class, 'show'])->name('show');
    Route::post('/{slug}/submit', [PublicFormController::class, 'submit'])->name('submit');
    Route::get('/{slug}/embed', [PublicFormController::class, 'embed'])->name('embed');
    Route::get('/{slug}/qr-code', [PublicFormController::class, 'qrCode'])->name('qr-code');
    Route::get('/{slug}/preview', [PublicFormController::class, 'preview'])->name('preview');
    Route::get('/{slug}/schema', [PublicFormController::class, 'schema'])->name('schema');
});

//-------------------------------Calling System-------------------------------------------
Route::group([
    'middleware' => ['auth', 'XSS'],
], function () {



    // Calling System Dashboard Routes
    Route::prefix('calling')->name('calling.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\CallingDashboardController::class, 'index'])->name('dashboard');
        Route::post('/sync', [App\Http\Controllers\CallingDashboardController::class, 'sync'])->name('sync');
        Route::get('/performance', [App\Http\Controllers\CallingPerformanceController::class, 'index'])->name('performance');
        Route::get('/analytics', [App\Http\Controllers\CallingAnalyticsController::class, 'index'])->name('analytics');
    });

    // Call Logs Routes
    Route::resource('call-logs', App\Http\Controllers\CallLogController::class);
    Route::post('call-logs/import', [App\Http\Controllers\CallLogController::class, 'import'])->name('call-logs.import');
    Route::get('call-logs/export', [App\Http\Controllers\CallLogController::class, 'export'])->name('call-logs.export');

    // Lead Assignments Routes
    Route::resource('lead-assignments', App\Http\Controllers\LeadAssignmentController::class);
    Route::get('lead-assignments/agents', [App\Http\Controllers\LeadAssignmentController::class, 'agents'])->name('lead-assignments.agents');
    Route::post('lead-assignments/bulk', [App\Http\Controllers\LeadAssignmentController::class, 'bulkAssign'])->name('lead-assignments.bulk');
    Route::post('lead-assignments/{assignment}/accept', [App\Http\Controllers\LeadAssignmentController::class, 'accept'])->name('lead-assignments.accept');
    Route::post('lead-assignments/{assignment}/reject', [App\Http\Controllers\LeadAssignmentController::class, 'reject'])->name('lead-assignments.reject');
    Route::post('lead-assignments/{assignment}/transfer', [App\Http\Controllers\LeadAssignmentController::class, 'transfer'])->name('lead-assignments.transfer');

    // Follow-ups Routes
    Route::resource('follow-ups', App\Http\Controllers\FollowUpController::class);
    Route::post('follow-ups/{followUp}/complete', [App\Http\Controllers\FollowUpController::class, 'complete'])->name('follow-ups.complete');
    Route::post('follow-ups/{followUp}/reschedule', [App\Http\Controllers\FollowUpController::class, 'reschedule'])->name('follow-ups.reschedule');
    Route::get('follow-ups/calendar', [App\Http\Controllers\FollowUpController::class, 'calendar'])->name('follow-ups.calendar');

    // Call Queue Routes
    Route::resource('call-queue', App\Http\Controllers\CallQueueController::class);
    Route::post('call-queue/{queueItem}/start', [App\Http\Controllers\CallQueueController::class, 'start'])->name('call-queue.start');
    Route::post('call-queue/{queueItem}/complete', [App\Http\Controllers\CallQueueController::class, 'complete'])->name('call-queue.complete');
    Route::post('call-queue/{queueItem}/skip', [App\Http\Controllers\CallQueueController::class, 'skip'])->name('call-queue.skip');
    Route::post('call-queue/reorder', [App\Http\Controllers\CallQueueController::class, 'reorder'])->name('call-queue.reorder');
});

